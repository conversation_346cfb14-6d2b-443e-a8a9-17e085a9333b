{"compilerOptions": {"lib": ["es2022"], "target": "es2017", "module": "commonjs", "allowJs": true, "checkJs": false, "declaration": false, "outDir": "build", "strict": true, "resolveJsonModule": true, "alwaysStrict": true, "noUnusedLocals": true, "noImplicitAny": false, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "node", "typeRoots": ["./node_modules/@types", "./src/types"], "baseUrl": "."}, "include": ["src", "test_e2e"], "exclude": ["src/start_consumers.ts"]}