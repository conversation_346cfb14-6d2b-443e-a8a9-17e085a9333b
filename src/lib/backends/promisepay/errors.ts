import { ProviderError } from '../error';
import logger from '../../logger';
import { truncateDeclineReason } from '../../../models/transaction';

const declineCodes = {
  failed_to_create_bank_account: 'Account is missing a bank account id',
  failed_to_create_card: 'Account is missing a card account id',
  unknown_user_for_transaction: 'Missing buyer or seller id',
  payment_unsuccessful: 'Backend payment creation failed on promisepay',
  authentication_required:
    'The card was declined as the transaction requires authentication.',
  approve_with_id: 'The payment can’t be authorized.',
  call_issuer: 'The card was declined for an unknown reason.',
  card_not_supported: 'The card does not support this type of purchase.',
  card_velocity_exceeded:
    'The customer has exceeded the balance or credit limit available on their card.',
  currency_not_supported: 'The card does not support the specified currency.',
  do_not_try_again: 'The card was declined for an unknown reason.',
  duplicate_transaction:
    'A transaction with identical amount and credit card information was submitted very recently.',
  expired_card: 'The card has expired.',
  generic_decline:
    'The card was declined for an unknown reason or possibly triggered by a blocked payment rule.',
  incorrect_number: 'The card number is incorrect.',
  incorrect_cvc: 'The CVC number is incorrect.',
  incorrect_pin:
    'The PIN entered is incorrect. This decline code only applies to payments made with a card reader.',
  incorrect_zip: 'The postal code is incorrect.',
  account_must_belong_to_the_user:
    'You’ve entered a bank or card account number that is not associated with the buyer.',
  invalid_format:
    'You’ve entered a bank or card account number in a format that our system does not recognise.',
  payment_is_already_made_for:
    'You are trying to pay for an item that is already paid for.',
  state_transition_invalid:
    'You are trying to pay for an item that could already be fully paid for, be in a payment pending state, be in a payment held state due to suspected fraudulent activity, or have previously been refunded.',
  invalid:
    'You’ve entered an incorrect item ID, or an invalid bank or card account number.',
  invalid_billing_descriptor:
    'A credit card payment could not be processed due to how your platform is configured to display Soft Descriptors. This error may appear on US-based platforms.',
  do_not_honor:
    'The credit card used for this transaction could have temporary or permanent restrictions placed on it by the issuing bank, have been blocked due to suspected fraudulent activity, or have insufficient funds. Please refer the buyer to their issuing bank.',
  unavailable_payment_gateway:
    'The payment gateway used for this transaction is out of service or inaccessible at this time. The transaction may be attempted again after a few minutes.',
  refer_to_card_issuer:
    'The credit card used for this transaction could have been blocked due to suspected fraudulent activity or be expired or flagged as lost or stolen by the issuing bank. Please refer the buyer back to their issuing bank.',
  purchase_transaction_denied:
    'The credit card used to fund this transaction has been denied. Please refer the buyer to their issuing bank.',
  transaction_exceededs_limit:
    'The credit card transaction was declined due to a transaction limit imposed on the payment gateway.',
  invalid_card_details:
    'Invalid card details. Please add a new card and try again.',
  stolen_card_pick_up:
    'The credit card refund failed because the card was reported stolen. Please reach out to the cardholder.',
  account_on_hold:
    'The buyer’s account is currently held due to suspicious activity.',
  direct_payment_needs_authority:
    'The buyer has not provided a direct debit authority associated with the bank account.',
  unsupported_currency:
    'Your platform is not configured to support the currency in use.',
  direct_debit_not_allowed:
    'Your platform did not allow the direct debit payment which could have been due to a payment restriction in place which limits or disallows direct debit payments, or an underwriting threshold in place for the buyer.',
  digital_wallet_not_allowed:
    'Your platform did not allow the digital wallet payment which could have been due to a payment restriction in place which limits or disallows digital wallet payments, or an underwriting threshold in place for the buyer.',
  pay_by_wire_transfer_not_allowed:
    'Your platform did not allow the wire transfer payment which could have been due to a payment restriction in place which limits or disallows wire transfer payments, or an underwriting threshold in place for the buyer.',
  error_processing_credit_card:
    'The credit card used to fund this transaction does not have enough available funds.',
  refer_to_cust:
    'An issue with direct debit processing has occurred. Please reach out to the customer.',
  payment_stop:
    'The customer or their bank has stopped the transaction from occurring.',
};

const GENERIC_ZAI_ERROR_MESSAGE =
  'Error processing credit card. If this error persists, please contact our support team.';

const GENERIC_OM_ERROR_MESSAGE =
  'The payment has been declined by the payment processor. Please contact the card owner.';

export const SYSTEM_ERROR = new ProviderError(
  'Error processing payment, please try again later',
  'system_error',
  500
);

export const resolveErrorMessage = (
  message: string
): { code?: string; message?: string } => {
  if (!message) return {};
  const parsed = Array.isArray(message) ? message[0] ?? '' : message;
  const trimmed = parsed
    .replace(/[^a-zA-Z\s]/g, '')
    .trim()
    .toLowerCase();
  if (trimmed in declineCodes) {
    return {
      code: trimmed,
      message: declineCodes[trimmed],
    };
  }
  if (parsed === GENERIC_ZAI_ERROR_MESSAGE) {
    return {
      code: 'generic_decline',
      message: GENERIC_OM_ERROR_MESSAGE,
    };
  }
  return {
    message: parsed,
  };
};

/**
 * Errors from zai are axios errors (See: https://github.com/ordermentum/zai-node)
 * This function will attempt to resolve the error into a ProviderError(MESSAGE, CODE)
 * Zai errors are in the following format:
 * {
 *  errors: {
 *     base: string[],
 *     provider_response_code: string; // https://support.hellozai.com/new-http-error-codes
 *     provider_response_message: string;
 *  }
 * }
 *
 * Note that error.message will be something like:
 *  "Credit card payment failed: Insufficient funds"
 *
 *  vs provider_response_message:
 *  "Insufficient funds"
 */
export const getProviderErrorDetails = (error: any) => {
  const errors = error.response?.data?.errors ?? {};
  const fallback = resolveErrorMessage(errors.base?.join('') ?? error.message);

  // TODO: This is obviously hacky and dumb but can be removed as soon as we have a clear view of the error format
  if (fallback.message === GENERIC_OM_ERROR_MESSAGE) {
    logger.error(`DEBUG: errors object - ${JSON.stringify(errors)}`);
    logger.error(`DEBUG: error.message - ${error.message}`);
  }
  const code = fallback.code ?? errors.provider_response_code;
  // TODO: Provider response should be integrated into the lookup
  const message = fallback.message ?? errors.provider_response_message;
  const statusCode = error.response?.status ?? error.response?.statusCode;
  const parsedCode = Array.isArray(code) ? code[0] : code;
  const parsedMessage = Array.isArray(message) ? message[0] : message;

  return {
    code: parsedCode,
    message: truncateDeclineReason(parsedMessage),
    statusCode,
  };
};

export const providerErrorResolver = (error: any) => {
  const { code, message, statusCode } = getProviderErrorDetails(error);

  throw new ProviderError(message, code, statusCode);
};
