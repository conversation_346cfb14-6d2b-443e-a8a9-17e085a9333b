import get from 'lodash.get';
import moment from 'moment';
import client from './client';

export const convertUser = user => ({
  email: user.email,
  first_name: user.first_name,
  last_name: user.last_name,
  address_line1: user.address_line_1,
  city: user.city,
  state: user.state,
  zip: user.postcode,
  country: user.country,
  mobile: user.mobile_number,
  dob: user.dob ? moment(user.dob).format('DD/MM/YYYY') : undefined, // <PERSON><PERSON> explicitly requires the DD/MM/YYYY format - https://developer.hellozai.com/reference/createuser
  id: user.external_id,
});

export const extractCompany = user => ({
  name: user.company_name,
  legal_name: user.company_legal_name,
  tax_number: user.company_tax_number,
  address_line1: user.company_address_line_1,
  address_line2: user.company_address_line_2,
  city: user.company_city,
  state: user.company_state,
  zip: user.company_postcode,
  country: user.company_country,
  phone: user.mobile_number,
});

export const getUser = id => client.users.showUser(id);

export const updateUser = async user => {
  let company;
  if (user.company_name) {
    company = extractCompany(user);
  }
  const payload = convertUser(user);

  const resp = await client.users.updateUser(payload.id, payload);

  const companyId = get(resp, 'users.related.companies', undefined);
  if (company && companyId) {
    company.user_id = payload.id;
    await client.companies.updateCompany(companyId, company);
  }

  return user;
};

export const createUser = async user => {
  let company;
  if (user.company_name) {
    company = extractCompany(user);
  }

  const payload = convertUser(user);

  await client.users.createUser(payload);

  if (company) {
    company.user_id = payload.id;
    await client.companies.createCompany(company);
  }

  return user;
};

export default {
  updateUser,
  createUser,
  getUser,
};
