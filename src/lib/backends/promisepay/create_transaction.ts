/* eslint-disable camelcase */
import { getTemporalClient } from '@ordermentum/temporal';
import {
  AccountIdRequestBody,
  AsyncMakePaymentBody,
  SingleItem,
} from 'zai-payments';
import Logger from 'bunyan';
import { Job, Transaction, User } from '../../../models';
import {
  Backend,
  states as TransactionStates,
  truncateDeclineReason,
} from '../../../models/transaction';
import { logger } from '../../logger';
import { count } from '../../stats';
import { client } from './client';
import getPaymentMethod from './get_payment_method';
import { ESCROW, ESCROW_TYPES, EXPRESS, PAYMENT_TYPES } from './types';
import { userWalletAccount } from './user_wallet_account';
import { ProviderError } from '../error';
import { providerErrorResolver } from './errors';
import {
  BackendTransactionAttributes,
  CreateTransactionResult,
} from '../../../types/payment_types';
import {
  canUseWallet,
  replenishWalletIfRequiredForTransaction,
} from '../../../helpers/wallets';
import { normalize, sanitize } from '../../../helpers/string';
import { WorkflowFactory } from '../../../ng/workflow/workflow.factory';

const sanitizeDescriptor = (descriptor: string) => {
  // Normalize diacritics strings (e.g., "ö" becomes "o")
  const normalizedDescriptor = normalize(descriptor);

  // and sanitize the rest
  return sanitize(
    normalizedDescriptor,
    /[^A-Za-z0-9^_[\]',?;:=#/.()*&%!$ @+-]/g
  );
};

/**
 * Creates a new Transaction db record to track the interaction
 * with the payment gateway. This is not to be confused with
 * any journal action as that is managed separately.
 */
const createItem = async (
  transaction: BackendTransactionAttributes,
  due: Date,
  type = PAYMENT_TYPES.ESCROW,
  log = logger
) => {
  // FIXME: ItemRequestBody from assembly-payments has amount incorrectly typed
  const params = {
    amount: transaction.amount,
    buyer_id: transaction.buyer_id,
    seller_id: transaction.seller_id,
    description: transaction.description,
    custom_descriptor: sanitizeDescriptor(`OM ${transaction.description}`),
    name: transaction.name,
    // FIXME: due_date does not exist exist in ItemRequestBody https://developer.hellozai.com/reference/createitem
    due_date: `${due.getDate()}/${due.getMonth()}/${due.getFullYear()}`,
    id: transaction.id,
    payment_type: type,
  };
  log.info({ params }, 'Params for item create');

  const item = await client.items
    .createItem(params)
    .catch(providerErrorResolver);

  log.debug(`Created item successfully ${transaction.id}`);

  return item;
};

/**
 * Returns true if the given argument is a Zai SingleItem type
 */
const isSingleItem = (
  item: SingleItem | AsyncMakePaymentBody
): item is SingleItem => (item as SingleItem).items !== undefined;

/**
 * Tests the validity of the given item
 */
const isValid = (
  payment: SingleItem | AsyncMakePaymentBody,
  type = PAYMENT_TYPES.ESCROW
) => {
  const states = ESCROW_TYPES.includes(type) ? ESCROW : EXPRESS;

  if (isSingleItem(payment)) {
    return states.includes(payment?.items?.state ?? '');
  }

  return !!payment.last_updated;
};

/**
 * Calls the Zai client to make a payment
 */
const callGateway = async ({
  id,
  requestBody,
  isAsyncPayment,
  log,
}: {
  id: string;
  requestBody: AccountIdRequestBody;
  isAsyncPayment: boolean;
  log: Logger;
}) => {
  if (isAsyncPayment) {
    // NOTE: This would normally be injected via a dependency injection container, this
    // is a temporary step to enable this new code architecture.
    const workflowFactory = new WorkflowFactory(await getTemporalClient());

    return workflowFactory.startMakeZaiPayment(
      id,
      requestBody,
      log.child({
        isAsyncPayment,
      })
    );
  }

  return client.items.makePayment(id, requestBody);
};

// TODO ARCHITECTURE:
// the abstraction is entirely wrong here
// the wallet concept should be above the backend level
// fund movement should not be at the gateway level as it currently is.
export const createTransaction = async (
  transaction: BackendTransactionAttributes,
  parentLogger = logger
): Promise<CreateTransactionResult> => {
  const log = parentLogger.child({
    backend: 'zai',
    transactionBackend: transaction.backend, // note: as wallets are held in zai, upfront finstro orders using wallets can re-use this zai logic
    id: transaction.id,
  });

  log.info({ transaction }, 'Creating zai transaction');

  const buyer = await User.findByPk(transaction.buyer_id);
  const seller = await User.findByPk(transaction.seller_id);

  if (!buyer) {
    throw new Error(`Unknown buyer ${buyer} for Transaction`);
  }

  if (!seller) {
    throw new Error(`Unknown seller ${seller} for Transaction`);
  }

  const wallet = await userWalletAccount(buyer.external_id);
  let walletBalance = wallet?.balance ?? 0;
  let fundedTxId: string | undefined;
  let fundedCents = 0;

  log.info(
    `Wallet balance: ${walletBalance} ${transaction.amount}, ${wallet?.id} wallet funds: ${buyer?.configuration?.walletFunds}}`
  );

  const method = await getPaymentMethod(transaction, wallet);

  if (!method?.backend_id) {
    log.error(`Invalid payment method for transaction ${transaction.id}`);
    throw new Error('Invalid Payment Method');
  }

  const type = PAYMENT_TYPES.EXPRESS;
  const now = new Date();
  const payload = {
    ...transaction,
    buyer_id: buyer.external_id,
    seller_id: seller.external_id,
  };

  log.info({ payload }, 'Create item payload');

  // create the item, then we determine how we are going to pay for it
  try {
    await createItem(payload, now, type, log);
  } catch (e: any) {
    log.error({ err: e, payload }, 'failed to create item');
    throw e;
  }

  const body: AccountIdRequestBody = {
    account_id: method.backend_id,
    merchant_phone: seller.mobile_number,
  };

  let payment: SingleItem | AsyncMakePaymentBody | undefined;
  let usingWallet: boolean;

  try {
    usingWallet = canUseWallet(transaction, buyer, wallet);

    // finstro transactions will re-use the zai transaction logic for handling moving money in wallets,
    // which live in zai. if we are trying to use zai from finstro, but for some reason can't use a wallet,
    // then we've got a problem - don't try and continue
    if (!usingWallet && transaction.backend === Backend.FINSTRO) {
      throw new Error(
        'Ineligible to use wallet for this trade account payment'
      );
    }

    // TODO SMELL: split if/else function for the sake of cognitive load

    // at this point we know we can use the wallet, so we should check if we need to replenish it
    if (usingWallet) {
      log.info(`using wallet for transaction ${transaction.id}`);

      // TODO JOURNAL_V3: This should be up a level or more, not at the point of calling the payment gateway! See comments above `CreateTransactionResult`
      // firstly we fund the wallet if the balance is too low to fund the transaction
      const replenishResult = await replenishWalletIfRequiredForTransaction(
        transaction,
        buyer,
        seller,
        false, // we make this a sync payment, otherwise we are juggling too many balls
        wallet
      );

      walletBalance = replenishResult.walletBalance;
      fundedCents = replenishResult.fundedCents;
      fundedTxId = replenishResult.fundedTxId;

      // this implies that we failed to fund the wallet for the transaction value
      // FIXME: we should be returning a valid error here...
      if (walletBalance < transaction.amount || !wallet?.id) {
        throw new Error(
          `Insufficient funds in wallet for transaction ${transaction.id}`
        );
      }

      // the transaction is funded by the wallet and can be async
      const isAsyncPayment = Boolean(transaction.tryAsync);

      log.info(
        { isAsyncPayment },
        'Wallet balance is greater than transaction amount - using wallet to fund transaction'
      );

      // ok, we have enough funds in the wallet to pay for the transaction
      log.info({ payment }, 'Make gateway wallet payment');

      payment = await callGateway({
        id: transaction.id,
        requestBody: {
          account_id: wallet.id,
          merchant_phone: seller.mobile_number,
        },
        isAsyncPayment,
        log,
      }).catch(providerErrorResolver);

      // triple check the state of the item from the make payment response
      const valid = isValid(payment, type);

      if (!valid) {
        log.info({ payment }, 'Payment was not valid');

        // FIXME: we should be returning a valid error here...
        throw new Error('Payment unsuccessful');
      }
    } else {
      const isAsyncPayment =
        Boolean(transaction.tryAsync) &&
        Boolean(transaction.card_payment_method_id);

      log.info({ isAsyncPayment }, `Attempting payment on ${transaction.id}`);

      payment = await callGateway({
        id: transaction.id,
        requestBody: body,
        isAsyncPayment,
        log,
      }).catch(providerErrorResolver);

      count('payments_payment_failure');
      log.info({ payment }, 'trigger payment');

      const valid = isValid(payment, type);

      if (!valid) {
        log.error({ payment }, 'Cannot understand payment response');
        throw new Error('Payment Unsuccessful');
      }
    }
  } catch (err) {
    log.error({ err }, 'Failed Payment');

    await Transaction.update(
      {
        state: Transaction.states.failed,
        declineCode: (err as ProviderError)?.code,
        declineReason: truncateDeclineReason((err as ProviderError)?.message),
      },
      {
        where: {
          id: transaction.id,
        },
      }
    );

    throw err;
  }

  if (isSingleItem(payment)) {
    if (
      type !== PAYMENT_TYPES.EXPRESS &&
      payment?.items?.state === Transaction.states.completed
    ) {
      await Job.create({
        name: 'trigger pp disbursement',
        data: {
          transaction,
        },

        nextRunAt: new Date().toISOString(),
      });
    }

    // We rely on the payload shape to determine if the payment is async
    // TODO ARCHITECTURE: Move this state update to the payment orchestrator method
    // OR better yet move all this to a temporal workflow
    if (payment?.items?.state) {
      await Transaction.update(
        {
          state: payment.items.state as TransactionStates,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
    }
  }

  return {
    fundedCents,
    fundedTxId,
  };
};

export default createTransaction;
