import { updateUser, getUser, createUser } from './user';
import createTransaction from './create_transaction';
import updateTransaction from './update_transaction';
import refundTransaction from './refund_transaction';
import { deleteTransaction } from './delete_transaction';
import { makePayment } from './make_payment';
import { handleSettlement as upsertSettlement } from './settlement';
import { createCard, createCard as updateCard } from './card';
import { createBank, createBank as updateBank } from './bank';
import { getItem, handlePPItemChange, handlePPItemError } from './item';
import { handleDisbursement } from './disbursement';
import { disburseFunds } from './disburse_funds';
import { getNextClearingTime } from './times';
import refund, { walletRefund } from './refund';
import { userWalletAccount } from './user_wallet_account';
import { getItemBatchTransactions } from './get_batch_transactions';
import client from './client';

// TODO ARCHITECTURE: Create gateway abstraction and combine these into a class
export {
  client,
  updateUser,
  createUser,
  getUser,
  createTransaction,
  updateTransaction,
  refundTransaction,
  disburseFunds,
  deleteTransaction,
  upsertSettlement,
  createCard,
  updateCard,
  createBank,
  updateBank,
  handlePPItemChange,
  handlePPItemError,
  handleDisbursement,
  getNextClearingTime,
  refund,
  makePayment,
  userWalletAccount,
  getItemBatchTransactions,
  walletRefund,
  getItem,
};
