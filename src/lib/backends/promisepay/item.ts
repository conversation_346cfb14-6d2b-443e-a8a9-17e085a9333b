import httpErrors from 'httperrors';
import { Item } from 'assembly-payments';
import Logger from 'bunyan';
import {
  TransactionInstance,
  truncateDeclineReason,
} from '../../../models/transaction';
import { Transaction, User, UserSetting } from '../../../models';
import steveo from '../../steveo/steveo';
import { logger } from '../../logger';
import { calculateRelease } from '../../calculations';
import { failedBatchDebit } from '../../../helpers/state_helpers';
import { statuses } from './mappings';
import { addItemErrorLog, addItemLog } from './add_item_log';
import { JournalTransaction } from '../../journal_v2/journal_v2';
import { resolveErrorMessage } from './errors';
import { client } from './client';
import { zaiCallbackHandlers } from '../../../utils/transaction_context_handlers';
import { taskLocker } from '../../task_locker';

/**
 * TODO ARCHITECTURE: Remove context handling once src/routes/upfront_payment.route.ts is rolled out
 */
async function handleCallbacksInContext(
  transaction: TransactionInstance,
  log: Logger
) {
  const { context } = transaction;
  if (context?.isZaiAsync && context?.workflow) {
    log.info('Zai async transaction - calling handler', {
      transaction,
      context,
    });

    try {
      await zaiCallbackHandlers[context.workflow](transaction, log);
    } catch (err) {
      log.error('Error calling zai callback handler', err);
    }
  }
}

export const handleItem = async (item: Item) => {
  const log = logger.child({ itemId: item.id });
  log.info(`handling callback for item ${item.id}`);

  await addItemLog(item);

  // Zai will potentially call us back multiple times for the same transaction
  // and state (it support "at least once" deliverly of webhooks).
  // Using the task locker will ensure that multiple Payments service instances
  // (on different pods) will not compete to process the callback from Zai and
  // prevent a race condition if Zai sends the webhook in a small enough window.
  return taskLocker(`transaction:${item.id}`, async () => {
    const transaction = await Transaction.findByPk(item.id);
    log.info('Handle transaction', transaction);

    if (!transaction) {
      log.error(`Zai webhook for nonexistent Item ${item.id} (transaction)`);
      throw httpErrors.NotFound();
    }

    // Has this event from Zai already been processed?
    if (transaction.state === 'completed' || transaction.state === 'failed') {
      logger.warn(
        `Item received from Zai targeted already complete transaction`,
        { zai: item }
      );

      return Transaction.toBody(transaction.get());
    }

    const seller = await User.findByPk(transaction.seller_id, {
      include: [{ model: UserSetting, as: 'userSetting' }],
    });

    if (!seller) {
      log.error('Seller not found');
      throw httpErrors.NotFound();
    }

    /**
     * if item is pending we will ignore as
     * this is the default state for the item
     */
    if (item.state === 'pending') {
      log.error(`Zai webhook for pending Item ${item.id} ignoring`);
      throw httpErrors.NotFound();
    }

    const newState = statuses[item.state];

    if (!newState) {
      log.info(`${item.state} not found - unknown transaction state`);
      return Transaction.toBody(transaction.get());
    }

    if (newState.internalState === Transaction.states.failed) {
      const failure = resolveErrorMessage(
        newState.description || 'Failed with provider'
      );
      transaction.declineReason = truncateDeclineReason(failure.message);
      transaction.declineCode = failure.code;
    }

    const batchState = item.batch_state;
    if (failedBatchDebit(batchState ?? '')) {
      log.info(
        `Failed batch for item ${batchState}. expect batch transaction callback`
      );
    }

    const completedStates: string[] = [
      Transaction.states.payment_deposited,
      Transaction.states.completed,
    ];

    const refundedStatus: string[] = [
      Transaction.states.refund_flagged,
      Transaction.states.refunded,
    ];

    if (refundedStatus.includes(transaction.state)) {
      log.info('Transaction refunded - no action');
      return Transaction.toBody(transaction.get());
    }

    if (
      transaction.is_reversal === false &&
      completedStates.includes(newState.internalState) &&
      !completedStates.includes(transaction.state)
    ) {
      log.info(`setting release at for transaction`);
      const receivedAt = new Date().toISOString();
      const releaseAt = calculateRelease(seller, receivedAt, 0);
      transaction.received_at = receivedAt;
      transaction.release_at = releaseAt;
    }

    if (newState.internalState !== Transaction.states.noop) {
      transaction.state = newState.internalState;
    }

    await handleCallbacksInContext(transaction, log);

    await transaction.save();

    if (transaction.context?.isZaiAsync) {
      log.info(
        `Zai async transaction - calling transaction-completed ${transaction.id}`
      );
      await steveo.publish('transaction-completed', {
        transactionId: transaction.id,
      });
    }

    await steveo.publish('transaction-changed', {
      transactionId: transaction.id,
    });

    const journal = new JournalTransaction(transaction.id);
    if (transaction.declineReason) await journal.markEntriesArchived();
    else if (transaction.received_at) await journal.markEntriesSettled();

    return Transaction.toBody(transaction.get());
  });
};

export const handleItemError = async (id: string, errors: any) => {
  const log = logger.child({ itemId: id });
  log.info(`handling error callback for item ${id}`, { errors });

  // There are 2 different zai error types
  const message =
    errors?.errors?.base?.[0] ?? errors?.base?.[0] ?? 'Unknown error from Zai';
  const declineReason = truncateDeclineReason(message);
  await addItemErrorLog(id, declineReason);
  const transaction = await Transaction.findByPk(id);

  if (!transaction) {
    log.error(`Zai webhook for nonexistent Item ${id} (transaction)`);
    throw httpErrors.NotFound();
  }

  await transaction.update({
    state: Transaction.states.failed,
    declineReason,
    declineCode: '422',
  });

  await handleCallbacksInContext(transaction, log);

  if (transaction.context?.isZaiAsync) {
    await steveo.publish('transaction-completed', {
      transactionId: transaction.id,
    });
  }

  await steveo.publish('transaction-changed', {
    transactionId: transaction.id,
  });

  const journal = new JournalTransaction(transaction.id);
  await journal.markEntriesArchived();

  return Transaction.toBody(transaction.get());
};

export const handlePPItemChange = handleItem;
export const handlePPItemError = handleItemError;

export const getItem = async (id: string) =>
  client.items.showItem(id).then(res => res.items ?? null);
