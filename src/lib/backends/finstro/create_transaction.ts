/**
 * Finstro is B2B payment platform that gives their users a trade account to transact with.
 * However, Ordermentum works differently with Finstro.
 * The payment methods we store for finstro is actually the customer identification info
 * In order to create a transaction, we:
 * - Fetch the customer info using the payment method
 * - We check if the balance is enough to fulfill the transaction amount
 * - We then send an invoice to Finstro with the transaction amount
 * - Send a message to a queue that will process the transaction via Zai
 *  - Will move the transaction amount into the seller's wallet
 */

/**
 * Monetary flow
 *
 * Request Transaction = Buyer -> Seller
 *
 * Internally, it's a 2 step transaction (Sync + Async)
 * (Sync) Buyer -> Finstro Wallet(Zai) (not immediate, floated by OM, reconciled later with Finstro)
 * (Async) Finstro Wallet -> Seller Wallet (Zai) (immediate)
 */
import httperrors from 'httperrors';
import BigNumber from 'bignumber.js';
import moment from 'moment';
import Logger from '../../logger';
import { validatePaymentMethod } from './utils';
import { finstroClient } from './client';
import { SendInvoiceResponse } from './types';
import { Transaction } from '../../../models';
import {
  BackendTransactionAttributes,
  CreateTransactionResult,
} from '../../../types/payment_types';
import { systemConfig } from '../../../system_config';
import { createTransaction as createTransactionZai } from '../promisepay/create_transaction';
import { finstroInvoicePaymentTask } from '../../../tasks/finstro_invoice_payment_task';

const failTransaction = async (id: string, reason: string) =>
  Transaction.update(
    {
      state: Transaction.states.failed,
      declineCode: '-1',
      declineReason: reason,
    },
    {
      where: {
        id,
      },
    }
  );

const succeedTransaction = async (id: string) =>
  Transaction.update(
    {
      state: Transaction.states.completed,
    },
    {
      where: {
        id,
      },
    }
  );

export const createTransaction = async (
  transaction: BackendTransactionAttributes,
  logger = Logger
): Promise<CreateTransactionResult> => {
  const log = logger.child({
    backend: transaction.backend,
    transactionId: transaction.id,
    amount: transaction.amount,
    paymentMethodId: transaction.paymentMethodId,
    requestSellerId: transaction.requestSellerId,
    sellerId: transaction.seller_id,
  });

  if (!transaction.paymentMethodId) {
    const message = 'Transaction must have an associated payment method';
    log.error(message);
    await failTransaction(transaction.id, message);
    throw httperrors.BadRequest(message);
  }

  const { customer, paymentMethod } = await validatePaymentMethod(
    transaction.paymentMethodId
  ).catch(async err => {
    await failTransaction(transaction.id, err.message);
    throw err;
  });

  log.fields.customerId = customer?.customer_id;
  log.fields.balanceOwing = customer?.accountInfo?.balance;

  log.info('Validated finstro payment method');

  // TODO ARCHITECTURE: This decision should be made by the calling code, not here
  if (transaction.useWalletAccount) {
    if (!systemConfig.FINSTRO_UPFRONT_ENABLED) {
      throw new Error('Finstro upfront payments disabled');
    }
    // note: wallets are held in zai/promisepay, even when we are using finstro to pay, so we will re-use the zai create_transaction, with the transaction.backend set to finstro
    return createTransactionZai(transaction, logger); // if this doesn't succeed, it should throw an error
  }

  try {
    // In Dollars, convert to cents
    // amounts are stored in cents in this system
    const accountBalance = new BigNumber(customer.accountInfo.limit)
      .minus(customer.accountInfo.balance)
      .times(100);
    if (accountBalance.lt(transaction.amount)) {
      const message = 'Insufficient funds';
      log.error(message);
      await failTransaction(transaction.id, message);
      throw httperrors.BadRequest(message);
    }

    log.info('Sending invoice');

    const payload = {
      InvoiceNumber: transaction.id,
      InvoiceAmount: new BigNumber(transaction.amount)
        .dividedBy(100)
        .toNumber(),
      InvoiceDate: moment().format('YYYY-MM-DD'),
      CustomerSubId: customer.customer_sub_id,
      Description: transaction.description,
    };

    const { data: resp } = await finstroClient.post<SendInvoiceResponse>(
      '/SendInvoice',
      payload
    );
    const invoice = resp.response;
    // Finstro returns 200 for all requests, even if the request is invalid
    if (invoice?.invoice_status !== 'APPROVED') {
      const message = invoice?.remarks || 'Invoice not approved';
      log.error(message);
      await failTransaction(transaction.id, message);
      throw httperrors.BadRequest(message);
    }

    await succeedTransaction(transaction.id);
    // Pre emptively update the balance on the payment method
    paymentMethod
      .update({
        details: {
          ...paymentMethod.details,
          balance: accountBalance.minus(transaction.amount).div(100).toFixed(2),
        },
      })
      // Don't throw error if we can't update the balance
      // src/tasks/cache_payment_method_balance_task.ts will update balance every 10 minutes anyway
      .catch(() => {});

    if (transaction.transactionType !== 'escrow') {
      await finstroInvoicePaymentTask.publish({
        transactionId: transaction.id,
      });
    }

    log.info('Invoice sent');

    return {
      fundedTxId: undefined,
      fundedCents: 0,
    };
  } catch (err) {
    const message = 'Payment Unsuccessful';
    log.error(err, message);
    await failTransaction(transaction.id, message);
    throw httperrors.InternalServerError(message);
  }
};
