import { Transaction } from 'sequelize';
import Logger from 'bunyan';
import {
  createJournal,
  getJournalByGatewayTxId,
  getJournalById,
  updateJournalEntryState,
  updateJournalState,
} from './journal_repo';
import {
  JournaledExecutorFn,
  OperationStates,
} from '../../types/journal_types';
import { updateLedgerBalance } from './ledger_repo';
import { systemConfig } from '../../system_config';
import { JournalV3, JournalV3Props } from '../../models/journal_v3_model';
import { JournalEntryV3Props } from '../../models/journal_v3_entry_model';
import { validJournalBalance } from './journal_validators';

/**
 * Returns true if all entries have completed successfully
 * @private
 */
function areEntriesSuccessful(entries: JournalEntryV3Props[]): boolean {
  return entries.every(e => e.state === OperationStates.Succeeded);
}

/**
 * Returns true if eny entryies failed
 * @private
 */
function areEntriesFailed(entries: JournalEntryV3Props[]): boolean {
  return entries.some(e => e.state === OperationStates.Failed);
}

/**
 * Returns an entry found by matching the gateway transaction id.
 * @private
 * @param transactionId Gateway transaction record id
 * @returns
 */
function getTransactionEntries(
  entries: JournalEntryV3Props[],
  transactionId: string
) {
  return entries.filter(j => j.transactionId === transactionId);
}

/**
 * This does not handle any payment gateway interactions and only handles the
 * update of the journal & ledger.
 * @private
 */
async function completeSuccess(
  journal: JournalV3,
  gatewayTxId: string | undefined,
  tx: Transaction | undefined,
  logger: Logger
) {
  const log = logger.child({ journalId: journal.id, gatewayTxId });

  log.info('Journal succeeded');

  if (!journal.entries.every(e => e.state === OperationStates.Succeeded)) {
    logger.warn(
      { entries: journal.entries },
      'Journal was completed successfully before all async entries had completed'
    );
  }

  // Update journal state
  const affectedRecords = await updateJournalState(
    journal.id,
    OperationStates.Succeeded,
    undefined,
    tx,
    log
  );

  if (affectedRecords !== 1) {
    log.error(
      { affectedRecords },
      'Journal success update incorrect matched record count'
    );
  }

  await updateJournalEntryState(
    gatewayTxId,
    OperationStates.Succeeded,
    tx,
    log
  );

  // Initiate incremental ledger updates based on the journal entry amounts.
  await Promise.all(journal.entries.map(e => updateLedgerBalance(e, tx, log)));
}

/**
 * This does not handle any payment gateway interactions and only handles the
 * update of the journal & ledger.
 * @private
 */
async function completeFailed(
  journalId: string,
  gatewayTxId: string | undefined,
  error: unknown,
  tx: Transaction | undefined,
  logger: Logger
) {
  const log = logger.child({ journalId, gatewayTxId, error });

  const affectedRecords = await updateJournalState(
    journalId,
    OperationStates.Failed,
    JSON.stringify(error),
    tx,
    log
  );

  if (affectedRecords !== 1) {
    log.error(
      { affectedRecords },
      'Journal success update incorrect matched record count'
    );
  }

  await updateJournalEntryState(gatewayTxId, OperationStates.Failed, tx, log);
}

/**
 * Extremely paranoid
 */
function safeCompleteFailed(
  journalId: string,
  gatewayTxId: string | undefined,
  callerError: unknown,
  tx: Transaction | undefined,
  logger: Logger
) {
  const log = logger.child({ journalId, gatewayTxId, callerError });

  try {
    log.error('Error executing journaled code');

    return completeFailed(journalId, gatewayTxId, callerError, tx, log);
  } catch (err) {
    log.error({ err }, 'Error trying to update journal to failed state');

    return Promise.resolve();
  }
}

/**
 * Given a journal instance this will return the current
 * expected state of the journal entry i.e. the state it will
 * transition to on update.
 * @private
 */
function calcCurrentState(journal: JournalV3) {
  if (areEntriesSuccessful(journal.entries)) {
    return OperationStates.Succeeded;
  }

  if (areEntriesFailed(journal.entries)) {
    return OperationStates.Failed;
  }

  return OperationStates.Pending;
}

/**
 * This will note the completetion of a gateway transaction either success
 * or failure. A call to this may trigger the completion of a journal record
 * and the updating of the associated ledger entries.
 * @param gatewayTxId Gateway transaction id
 * @param tx Postgres transaction object reference (optional)
 */
export async function journalGatewayTx(
  gatewayTxId: string,
  error: string | undefined,
  tx: Transaction | undefined,
  logger: Logger
) {
  if (!systemConfig.JOURNAL_V3) {
    return;
  }

  const log = logger.child({ gatewayTxId, error });
  const journal = await getJournalByGatewayTxId(gatewayTxId, tx);

  // NOTE:
  // We don't want to be hard-nosed about matching journals at the moment and
  // break existing flows, but we'll still raise the alarm that something was
  // wrong and needs to be looked at.
  // TEMPORARY: This is probably due to a transaction being initiated outside
  // of the main createTransaction flow, which is still to be implemented.
  if (!journal) {
    log.warn('Journal entry not found with transaction id');
    return;
  }

  log.debug({ journalId: journal.id }, 'Journal entry update');

  const entries = getTransactionEntries(journal.entries, gatewayTxId);

  if (entries.some(e => e.state !== OperationStates.Pending)) {
    log.error(
      { entries, journalId: journal.id },
      'All journal entries expected to be pending'
    );
  }

  if (journal.state !== OperationStates.Pending) {
    log.error(
      { state: journal.state },
      'Journal record expected to be pending'
    );
  }

  entries.forEach(e => {
    e.state = error ? OperationStates.Failed : OperationStates.Succeeded;
  });

  const journalState = calcCurrentState(journal);

  switch (journalState) {
    case OperationStates.Succeeded: {
      await completeSuccess(journal, gatewayTxId, tx, log);
      break;
    }
    case OperationStates.Failed: {
      await completeFailed(journal.id, gatewayTxId, error, tx, log);
      break;
    }

    default:
      // This will occur when some entries are still waiting for the gateway to return
      break;
  }

  logger.debug(
    { entries },
    `Journal gateway transaction processed: ${journalState}`
  );
}

/**
 * Journalled executors provide a safe method to execute
 * code that is to have an associated journal record to
 * track the movement of funds between users and OM
 * as a business.
 *
 * This style of "wrapped execution" is used to ensure the
 * sometimes numerous code paths complete without leaving
 * a journal record orphaned, or having to sometimes duplicate
 * the logic to complete a journal entry and update the
 * ledger with the final balance adjustments.
 */
export async function journaledExecutor<Returned>(
  fn: JournaledExecutorFn<Returned, JournalV3Props, JournalEntryV3Props>,
  tx: Transaction | undefined,
  logger: Logger
): Promise<{
  journalId: string | undefined;
  returned: Returned;
}> {
  if (!systemConfig.JOURNAL_V3) {
    const result = await fn();

    return {
      journalId: undefined,
      returned: result.returned,
    };
  }

  // Call the inner protected function
  const result = await fn();
  const { props, entries } = result.record;
  const { journalId } = await createJournal(props, entries, tx, logger);
  const log = logger.child({ journalId });

  if (result.overrides?.skipJournal) {
    log.info('Journal overrides: skipping journal');
    return {
      journalId: undefined,
      returned: result.returned,
    };
  }

  try {
    // Ensure the journal record balances correctly according to the
    // accounting equation.
    if (!validJournalBalance(entries)) {
      log.error({ entries }, 'Invalid journal balance detected');
    }

    // If there are no pending entries then we can wrap
    // this journal entry up and commit the ledger updates.
    if (areEntriesSuccessful(entries)) {
      const journal = await getJournalById(journalId, tx, log);

      await completeSuccess(journal, undefined, tx, log);
    }
  } catch (err) {
    await safeCompleteFailed(journalId, undefined, err, tx, log);

    throw err;
  }

  return {
    journalId,
    returned: result.returned,
  };
}
