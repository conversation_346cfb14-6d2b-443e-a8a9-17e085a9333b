import util from 'util';
import axios from 'axios';
import bunyan, { LogLevel } from 'bunyan';
import config from 'config';

const NODE_ENV = config.get<string>('NODE_ENV');

util.inspect.defaultOptions.breakLength = Infinity; // This is to avoid util format adding new line characters
util.inspect.defaultOptions.maxArrayLength = Infinity; // Do not truncate arrays

const getLogLevel = (): LogLevel => {
  const LOG_LEVEL = config.get<string>('LOG_LEVEL');
  switch (LOG_LEVEL) {
    case 'trace':
      return 'trace';
    case 'debug':
      return 'debug';
    case 'info':
      return 'info';
    case 'warn':
      return 'warn';
    case 'error':
      return 'error';
    case 'fatal':
      return 'fatal';
    default:
      return 'info';
  }
};

function serializeRequest(req) {
  return {
    url: req.originalUrl || req.url,
    reqId: req.reqId,
    method: req.method,
    query: req.query,
  };
}

function serializeResponse(res) {
  if (!res) {
    return {};
  }
  return {
    statusCode: res.statusCode,
  };
}

function serializeError(err) {
  if (axios.isAxiosError(err)) {
    return {
      message: err.message,
      name: err.name,
      stack: err.stack,
      response: serializeResponse(err.response),
    };
  }
  return bunyan.stdSerializers.err(err);
}

export const logger = bunyan.createLogger({
  name: 'Payments',
  environment: NODE_ENV,
  level: getLogLevel(),
  streams: [
    {
      stream: process.stdout,
    },
  ],
  serializers: {
    err: serializeError,
    error: serializeError,
    e: serializeError,
    req: serializeRequest,
    res: serializeResponse,
  },
});

export default logger;
