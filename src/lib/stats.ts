import { randomUUID } from 'crypto';
import type { StatsD as StatsDT } from 'hot-shots';
import StatsD from 'hot-shots';
import logger from './logger';

const nodeName = process.env.K8S_NODE_NAME || 'unknown';
const podName = process.env.K8S_POD_NAME || `unknown-pod-${randomUUID()}`;

// Fallback: no-op proxy handler
const noopHandler: ProxyHandler<StatsDT> = {
  get: () => () => {},
};

const noopInstance = new Proxy({} as StatsDT, noopHandler);

// Real or fallback statsd instance
export const statsd: StatsDT = process.env.STATSD_HOST
  ? new StatsD({
      host: process.env.STATSD_HOST,
      port: Number(process.env.STATSD_PORT) || 8125,
      globalTags: {
        env: process.env.NODE_ENV ?? 'unknown',
        nodeName,
        podName,
      },
      errorHandler: error => {
        logger.error('StatsD error:', error);
      },
    })
  : noopInstance;

/**
 * Increments a counter metric with optional tags and other arguments.
 */
export const increment: StatsDT['increment'] = (...args) =>
  statsd.increment(...(args as Parameters<StatsDT['increment']>));

/**
 * Decrements a counter metric.
 */
export const decrement: StatsDT['decrement'] = (...args) =>
  statsd.decrement(...(args as Parameters<StatsDT['decrement']>));

/**
 * Records a histogram value (for timing, sizes, etc).
 */
export const histogram: StatsDT['histogram'] = (...args) =>
  statsd.histogram(...(args as Parameters<StatsDT['histogram']>));

/**
 * Wraps a function to measure and report execution time.
 */
export const histogramFn = async <T>(label: string, fn: () => Promise<T>) => {
  const startTime = Date.now();

  try {
    return await fn();
  } finally {
    const duration = Date.now() - startTime;
    histogram(label, duration);
  }
};

/**
 * Records a gauge value (for current state measurement).
 */
export const gauge: StatsDT['gauge'] = (...args) =>
  statsd.gauge(...(args as Parameters<StatsDT['gauge']>));

/**
 * Records a set value (for counting unique elements).
 */
export const set: StatsDT['set'] = (...args) =>
  statsd.set(...(args as Parameters<StatsDT['set']>));

/**
 * Records a unique value.
 */
export const unique: StatsDT['unique'] = (...args) =>
  statsd.unique(...(args as Parameters<StatsDT['unique']>));

/**
 * Wraps an async function to measure and report execution time.
 */
export const asyncTimer: StatsDT['asyncTimer'] = (...args) =>
  statsd.asyncTimer(...(args as Parameters<StatsDT['asyncTimer']>));

/**
 * Automatically adds `_count` suffix and increments metric.
 */
export const count = (eventName: string) => {
  const metricName = eventName.toLowerCase().endsWith('_count')
    ? eventName
    : `${eventName}_count`;
  statsd.increment(metricName);
};

/**
 * Tag keys for common metrics.
 */
export enum LabelNames {
  ErrorType = 'error_type',
  Name = 'name',
  StartedAt = 'started_at',
  Type = 'type',
}
