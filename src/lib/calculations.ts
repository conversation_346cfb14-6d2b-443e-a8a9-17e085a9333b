import business from 'moment-business';
import moment, { MomentInput } from 'moment-timezone';
import { UserInstance } from '../models/user';
import { UserSettingInstance } from '../models/user_setting';

export const daysHours = hours => {
  const day = 24;
  const days = Math.floor(hours / day);
  const rest = hours % day;
  return {
    days,
    hours: rest,
  };
};

const CUTOFF_HOUR = 13;
const CUTOFF_MINUTE = 30;

export const isAfterCutOff = (receivedAt: MomentInput) =>
  moment(receivedAt)
    .clone()
    .set({ hour: CUTOFF_HOUR, minute: CUTOFF_MINUTE })
    .isBefore(receivedAt);

// Get the next business day and ensure release is in window
// FIXME(JD): Weekend Credit Card should be at 7:30am on Monday
// TODO ARCHITECTURE
// -- This should work off a policy -- immediate or delayed
// -- applyCardSettlementDelay should be removed, callers shouldn't need to know about it
export const calculateRelease = (
  user: UserInstance & { userSetting?: UserSettingInstance },
  receivedAt: MomentInput,
  time = 48,
  applyCardSettlementDelay = false
): string => {
  const received = moment(receivedAt).tz('Australia/Sydney');
  const { days } = daysHours(time);

  if (days === 0 && isAfterCutOff(received)) {
    return calculateRelease(user, receivedAt, 24, applyCardSettlementDelay);
  }

  let releaseAt = business.addWeekDays(received, days);

  // Weekly settlement and settlement delay days are mutually exclusive options:
  // - weeklySettlement: Releases at end of week + 3 days
  // - settlementDelayDays: Adds fixed number of business days to normal release date
  // These options are controlled by different parts of the system and won't be enabled simultaneously.
  // Settlement delay is checked first as it has higher usage across more user cohorts than lite
  if (applyCardSettlementDelay && user.userSetting?.settlementDelayDays) {
    // Adds to the release date if a settlement delay is set
    releaseAt = business.addWeekDays(
      moment(releaseAt).tz('Australia/Sydney'),
      user.userSetting.settlementDelayDays
    );
  } else if (user.configuration?.weeklySettlement) {
    const end = moment(received).endOf('week');
    releaseAt = business.addWeekDays(end, 3);
  }

  releaseAt.set({
    hour: CUTOFF_HOUR,
    minute: CUTOFF_MINUTE,
    second: 0,
    milliseconds: 0,
  });

  return releaseAt.toISOString();
};
