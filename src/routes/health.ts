import { Request, Response } from 'express';
import { <PERSON>Checker } from '@ordermentum/health';
import { logger } from '../lib/logger';
import { sequelize } from '../models';

// TODO: Add more checks
const healthChecker = new HealthChecker({
  maxConsecutiveFailures: 5,
  checkIntervalMs: 1000,
  logger,
})
  .register('postgresql', () => sequelize.query('select current_timestamp'))
  .start();

export default {
  health: (_req: Request, res: Response) => res.sendStatus(200),
  liveness: healthChecker.makeExpressHandler(),
};
