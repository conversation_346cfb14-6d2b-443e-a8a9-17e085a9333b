import { Request, Response, Router } from 'express';
import async<PERSON>and<PERSON> from 'express-async-handler';
import {
  UpfrontPaymentSchema,
  UpfrontPaymentCompleteSchema,
  CancelUpfrontPaymentSchema,
} from '../ng/common/requests/upfront_payments.request';
import { UpfrontPaymentController } from '../controllers/upfront_payment.controller';
import logger from '../lib/logger';

const router = Router();

router.post(
  '/',
  asyncHandler(async (req: Request, res: Response) => {
    logger.info({ req: req.body }, 'Create upfront payment');

    const body = UpfrontPaymentSchema.parse(req.body);

    const result = await UpfrontPaymentController.createUpfrontPayment(
      body,
      req.log
    );

    res.json(result);
  })
);

router.post(
  '/finalise',
  asyncHandler(async (req: Request, res: Response) => {
    logger.info({ req: req.body }, 'Finalise upfront payment');

    const body = UpfrontPaymentCompleteSchema.parse(req.body);

    await UpfrontPaymentController.finaliseUpfrontPayment(
      {
        ...body,
      },
      req.log
    );

    res.status(200).send();
  })
);

router.get(
  '/status/:orderId',
  asyncHandler(async (req: Request, res: Response) => {
    logger.info({ req: req.params }, 'Get upfront payment status');

    const result = await UpfrontPaymentController.getUpfrontPaymentStatus(
      req.params.orderId
    );

    res.json(result);
  })
);

router.post(
  '/cancel-and-revert',
  asyncHandler(async (req: Request, res: Response) => {
    logger.info({ req: req.body }, 'Cancel and revert upfront payment');

    const body = CancelUpfrontPaymentSchema.parse(req.body);

    await UpfrontPaymentController.cancelAndRevertUpfrontPayment({
      ...body,
    });

    res.json();
  })
);

export default router;
