import { Request, Response, Router } from 'express';
import asyncHand<PERSON> from 'express-async-handler';
import logger from '../lib/logger';
import { PromotionalPaymentController } from '../controllers/promotional_payment.controller';
import { PromotionalPaymentRequestSchema } from '../ng/workflow/types/promotional_payment.types';

const promotionalPaymentRouter = Router();

promotionalPaymentRouter.post(
  '/',
  asyncHandler(async (req: Request, res: Response) => {
    logger.info({ req: req.body }, 'Make promotional payment');

    const body = PromotionalPaymentRequestSchema.parse(req.body);

    const result = await PromotionalPaymentController.makePromotionalPayment(
      body,
      req.log
    );

    res.json(result);
  })
);

export { promotionalPaymentRouter };
