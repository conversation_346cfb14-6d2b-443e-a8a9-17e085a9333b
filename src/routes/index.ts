import { Router } from 'express';
import dummy from './dummy';
import users from './users';
import { retailers } from './retailers';
import paymentMethods from './payment_methods';
import paymentRuns from './payment_runs';
import paymentRunCharges from './payment_run_charges';
import cards from './cards';
import bankAccounts from './bank_accounts';
import transactions from './transactions';
import transactionsV2 from './transactions_v2';
import settlements from './settlements';
import remittance from './remittance';
import batch from './batch';
import subscriptions from './subscriptions';
import asap from '../middleware/asap_middleware';
import {
  validateAuth,
  validateAuthWithAsap,
} from '../middleware/auth_middleware';
import { auth } from '../middleware';
import { systemConfig } from '../system_config';
import validate from './validate';
import maintenance from './maintenance';
import usersV2 from './users_v2';
import upfrontPayments from './upfront_payment.route';
import { promotionalPaymentRouter } from './promotional_payment.route';

// NOTE: the order routes & middleware declared is important
const v1 = Router();

// This is defined explicity because the order of
// middleware is important. ASAP must be first, which sets the ASAP
// claims in the request locals. The validateAuth middleware then
// skips validation if the ASAP claims are present
const authWithAsap = [asap, ...validateAuthWithAsap];

// routes that will declare their own auth middleware(s) to use go here
if (systemConfig.RETAILER_ROUTES_ENABLED) {
  v1.use('/retailers', validateAuth, retailers);
  v1.use('/validate', authWithAsap, validate);
}

// all V1 routes declared after this use ASAP and basic auth
v1.use([asap, auth])
  .use('/dummy', dummy)
  .use('/users', users)
  .use('/payment-methods', paymentMethods)
  .use('/payment-runs', paymentRuns)
  .use('/payment-run-charges', paymentRunCharges)
  .use('/cards', cards)
  .use('/bank-accounts', bankAccounts)
  .use('/transactions', transactions)
  .use('/settlements', settlements)
  .use('/remittance', remittance)
  .use('/batch', batch)
  .use('/subscriptions', subscriptions)
  .use('/maintenance', maintenance);

const v2 = Router();
v2.use([asap, auth]);
v2.use('/transactions', transactionsV2);
v2.use('/users', usersV2);

const v3 = Router();
v3.use([asap, auth]);
v3.use('/upfront-payments', upfrontPayments);
v3.use('/promotional-payments', promotionalPaymentRouter);

const router = Router();
router.use('/v1', v1);
router.use('/v2', v2);
router.use('/v3', v3);

// TODO JOURNAL_V3: Add new /:retailerId/journal-entries endpoint for /v3

export default router;
