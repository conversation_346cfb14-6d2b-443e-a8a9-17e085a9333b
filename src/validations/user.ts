import Joi from 'joi';

import { Backend } from '../models/transaction';
import addressSchema from './address';

export const companySchema = Joi.object({
  name: Joi.string().required(),
  legal_name: Joi.string().required(),
  tax_number: Joi.string().optional().empty([null, '']),
  address: addressSchema,
});

const nameSchema = Joi.object({
  first: Joi.string().required(),
  last: Joi.string().required(),
});

export const configurationSchema = Joi.object({
  netSettlement: Joi.boolean().optional(),
  settlementRate: Joi.number().optional(),
  manualDisbursement: Joi.boolean().optional(),
  stopCredit: Joi.boolean().optional(),
  settlementPrefix: Joi.string().optional(),
  walletFunds: Joi.boolean().optional(),
  disbursementLimit: Joi.alternatives()
    .try(Joi.number(), Joi.string(), Joi.allow(null))
    .optional(),
  fallback: Joi.boolean().optional(),
  weeklySettlement: Joi.boolean().optional(),
  rates: Joi.object({
    amex: Joi.number().optional(),
    mastercard: Joi.number().optional(),
    visa: Joi.number().optional(),
    direct: Joi.number().optional(),
  }).optional(),
  reference: Joi.number().optional(),
  paymentRunStatus: Joi.string()
    .valid('paysOnInvoice', 'paysUnderSeparateSupplier')
    .optional(),
  supplier: Joi.boolean().optional(),
  companyDescriptor: Joi.string().optional(),
  walletRefunds: Joi.boolean().optional(),
  journals: Joi.boolean().optional(),
  funding: Joi.object({
    purchaserIds: Joi.array().items(Joi.string()).optional(),
    enabled: Joi.boolean().optional(),
  }).optional(),
}).options({ allowUnknown: true });

export const user = Joi.object<any>({
  id: Joi.string().guid().required(),
  created_at: Joi.string().isoDate().optional().allow(null),
  updated_at: Joi.string().isoDate().optional().allow(null),
  mobile_number: Joi.string().optional().allow(null),
  dob: Joi.string()
    .isoDate()
    .pattern(/.*T00:00:00(\.000)?(Z|([+-]00:00)|([+-]0000))$/) // Ensures this is a date at midnight UTC
    .allow(null)
    .optional(),
  settlement_webhook: Joi.string().uri().empty([null, '']).optional(),
  company: companySchema.allow(null),
  email: Joi.string().email(),
  name: nameSchema,
  address: addressSchema,
  external_id: Joi.string().guid().allow(null).optional(),
  payment_method_id: Joi.string().allow(null).optional(),
  payment_method_type: Joi.string().allow(null).optional(),
  configuration: configurationSchema.optional(),
  ordermentum_id: Joi.string().guid().required(),
  backend: Joi.string()
    .allow(null, Backend.STRIPE, Backend.PROMISEPAY)
    .only()
    .empty([null, ''])
    .default(Backend.PROMISEPAY),
  dd_settlement_delay_hours: Joi.number().optional(),
}).options({ allowUnknown: true });

export default user;
