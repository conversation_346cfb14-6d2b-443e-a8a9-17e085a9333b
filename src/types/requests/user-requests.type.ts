import { Backend } from '../../models/transaction';

export type CreateUserRequest = {
  id: string;
  created_at?: string;
  updated_at?: string;
  mobile_number?: string;
  dob?: string;
  settlement_webhook?: string;
  company?: {
    name: string;
    tax_number: string;
    legal_name: string;
    address: string;
  };
  email: string;
  name: {
    first: string;
    last: string;
  };
  address: {
    line_1: string;
    line_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  external_id?: string;
  ordermentum_id: string;
  backend: Backend;
  dd_settlement_delay_hours?: number;
};
