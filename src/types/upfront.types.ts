/**
 *
 */
export enum UpfrontFlowStep {
  // Waiting on the holding payment to be
  WaitingForHoldingPayment = 'waiting-for-holding-payment',

  // Waiting on the finalise signal to be fired from the controller (which
  // is called from the OM service)
  WaitingForFinaliseSignal = 'waiting-for-finalise-signal',

  // Waiting on the finalise payment confirmation that the payment has been taken
  // from the gateway (Zai)
  WaitingForFinalisePayment = 'waiting-for-finalise-payment',

  // The upfront payment has been cancelled
  Cancelled = 'cancelled',

  // The upfront payment has been completed
  Finished = 'finished',
}
