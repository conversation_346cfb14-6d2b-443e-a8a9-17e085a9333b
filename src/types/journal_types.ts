/**
 * Terminology definitions around our journal are kept in:
 * https://ordermentum.atlassian.net/wiki/spaces/OD/pages/**********/Payment+Journal+v3#Terminology-standardisation
 *
 * TODO After Journal V3 initial development...
 * The current modeling of money flow focuses on the resultant action i.e. "move money from A to B", however
 * the process of *accounting* for that money flow is more complex, requiring a more nuanced set of journal
 * entries that extends beyond the scope of the payments service.
 * This could be exposed through the REST API and offer the journal as service along with payments
 */
import { v7 as uuid } from 'uuid';
import { JournalEntryV3Props } from '../models/journal_v3_entry_model';

/**
 * Declaration of the 'side' of the journal entry being referred to
 * i.e. is this entry a 'credit' or a 'debit'
 */
export enum EntrySides {
  Credit = 'credit',
  Debit = 'debit',
}

/**
 *
 */
export enum OperationStates {
  Pending = 'pending',
  Succeeded = 'succeeded',
  Failed = 'failed',
}

/**
 * Account types allow us to decalare who controls/owns a given logical account
 */
export enum AccountType {
  System,
  User,
}

/**
 * Numerically encoded account numbers.
 * Accounts are logical holding places for money to be credited or debited.
 *
 * For space efficiency in the database, the account codes are numerical and help
 * us understand how money moves through the business.
 */
export type AccountCodes = SystemCodes | HolderCodes;

/**
 * !IMPORTANT! Once account numbers have been defined their value cannot change
 */
export enum SystemCodes {
  AssetHolding = 1100,
  ExpenseLosses = 3010,
  RevenueFees = 4100,
}

/**
 * !IMPORTANT! Once account numbers have been defined their value cannot change
 */
export enum HolderCodes {
  AssetHoldersBank = 1210,
  LiabilityWallet = 2010,
}

/**
 * Predefined types of the account codes
 */
export const accountCodeTypes: Record<SystemCodes | HolderCodes, AccountType> =
  {
    // Assets
    [SystemCodes.AssetHolding]: AccountType.System,
    [HolderCodes.AssetHoldersBank]: AccountType.User,

    // Liabilities
    [HolderCodes.LiabilityWallet]: AccountType.User,

    // Expenses
    [SystemCodes.ExpenseLosses]: AccountType.System,

    // Revenue
    [SystemCodes.RevenueFees]: AccountType.System,
  };

/**
 * Type of an internal protected piece of code that a
 * journal will be wrapped around.
 */
export type JournalFn = () => Promise<void>;

type UserEntryFn = (
  holderId: string,
  amountCents: number,
  side: EntrySides,
  gatewayTxId: string | undefined,
  state: OperationStates
) => JournalEntryV3Props;

type SystemEntryFn = (
  amountCents: number,
  side: EntrySides,
  gatewayTxId: string | undefined,
  state: OperationStates
) => JournalEntryV3Props;

/**
 * Private utility method to declare a new system account type for the chart
 * of accounts
 * @private
 */
function systemAccount(code: SystemCodes): SystemEntryFn {
  return (
    amountCents: number,
    side: EntrySides,
    gatewayTxId: string | undefined,
    state: OperationStates
  ): JournalEntryV3Props => ({
    id: uuid(),
    account: code,
    amountCents,
    side,
    state,
    transactionId: gatewayTxId,
    completedAt: state !== OperationStates.Pending ? new Date() : undefined,
    holderId: undefined,
  });
}

/**
 * Private utility method to declare a new user account type for the chart
 * of accounts
 * @private
 */
function userAccount(account: HolderCodes): UserEntryFn {
  return (
    holderId: string,
    amountCents: number,
    side: EntrySides,
    gatewayTxId: string | undefined,
    state: OperationStates
  ): JournalEntryV3Props => ({
    id: uuid(),
    account,
    holderId,
    amountCents,
    side,
    state,
    transactionId: gatewayTxId,
    completedAt: state !== OperationStates.Pending ? new Date() : undefined,
  });
}

export type JournaledExecutorResult<Returned, Props, EntryProps> = {
  returned: Returned;
  record: {
    props: Props;
    entries: EntryProps[];
  };
  overrides?: {
    /**
     * Allows the journalling to be skipped. This is useful for cases
     * where idempotency checks have short circuited their logical flow
     * and journal entries are deemed unnecessary.
     */
    skipJournal?: boolean;
  };
};

export type JournaledExecutorFn<Returned, Props, EntryProps> = () => Promise<
  JournaledExecutorResult<Returned, Props, EntryProps>
>;

/**
 * Chart of accounts
 * Reference to building a chart of accounts: https://www.moderntreasury.com/journal/accounting-for-developers-part-ii
 */
export interface Accounts {
  /**
   * Assets are cleared monetary value owned and held by the business
   */
  readonly asset: {
    readonly om: {
      /**
       * Money that is under the business's holdings
       */
      readonly holding: SystemEntryFn;
    };

    /**
     * Funds in a holder's bank account accessed through a payment gateway
     * This money is *not* held by Ordermentum
     */
    readonly holder: {
      /**
       * Money that has been transferred from a holder's bank account
       */
      readonly bank: UserEntryFn;
    };
  };

  /**
   * Liabilities represent money that we owe others.
   */
  readonly liability: {
    /**
     * Money held on the behalf of a platform 'holder'.
     * This could be in the form of credit that has been issued or funds that
     * were transferred from their bank via a payment gateway.
     */
    readonly wallet: UserEntryFn;

    /**
     * Money that has been refunded to a user
     */
    readonly refund: UserEntryFn;
  };

  /**
   * Money that has been lost as a cost of doing business
   */
  readonly expenses: {
    /**
     * Money that has been lost as a cost of doing business
     */
    readonly losses: SystemEntryFn;
  };

  /**
   * Money earned by the company
   */
  readonly revenue: {
    /**
     * Fees from sales of goods through the platform
     */
    readonly fees: SystemEntryFn;
  };
}

/**
 * This is the concrete definition of the chart of accounts
 * that provides numerical values for the account types.
 */
export const entry: Accounts = {
  asset: {
    om: {
      holding: systemAccount(SystemCodes.AssetHolding),
    },
    holder: {
      bank: userAccount(HolderCodes.AssetHoldersBank),
    },
  },

  liability: {
    wallet: userAccount(HolderCodes.LiabilityWallet),
    refund: userAccount(HolderCodes.LiabilityWallet),
  },

  expenses: {
    losses: systemAccount(SystemCodes.ExpenseLosses),
  },

  revenue: {
    fees: systemAccount(SystemCodes.RevenueFees),
  },
} as const;
