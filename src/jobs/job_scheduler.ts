import config from 'config';
import { <PERSON>Scheduler, SequelizeAdapter } from '@ordermentum/scheduler';
import { refundCheckTask } from '../tasks/refund_check_task';
import { batchCheckTask } from '../tasks/batch_check_task';
import { bulkTransactionCheckTask } from '../tasks/bulk_transaction_check_task';
import { transactionSyncTask } from '../tasks/transaction_sync_task';
import { disbursementsTask } from '../tasks/disbursement_task';
import { batchDisbursementsTask } from '../tasks/batch_disbursement_task';
import { batchNotificationTask } from '../tasks/batch_notification_task';
import { transactionCheckTask } from '../tasks/transaction_check_task';
import { logger } from '../lib/logger';
import batchDownload from '../actions/batch_download';
import { walletCheckTask } from '../tasks/wallet_check_task';
import { fundingAllowListTask } from '../tasks/funding_allowlist_task';
import { transactionCreator } from './common';
import webhookTask from '../tasks/webhook_task';
import { notificationTask } from '../tasks/notification_task';
import {
  itemChecker as ppItemHandler,
  disbursementChecker as ppDisbursementChecker,
  disbursementTrigger as ppDisbursementTrigger,
  finaliseDisbursement as ppFinaliseDisbursement,
} from './promisepay';
import { batchFundInvoicesTask } from '../tasks/batch_fund_invoices_task';
import { journalDiscrepancyTask } from '../tasks/journal_discrepancy_task';
import { fundsHolderBalanceTask } from '../tasks/funds_holder_balance_task';
import { reverseHeldFundsTask } from '../tasks/reverse_held_funds_task';
import { disbursementCheckTask } from '../tasks/disbursement_check_task';
import { finstroInvoicePaymentTask } from '../tasks/finstro_invoice_payment_task';
import { cachePaymentMethodBalanceTask } from '../tasks/cache_payment_method_balance_task';
import { statsd } from '../lib/stats';

const DEFAULT_JOB_LAG = config.get<number>('DEFAULT_JOB_LAG');
const DATABASE_URI = config.get<string>('DATABASE_URI');

const jobsSafeToRestart = [
  'check pp items',
  'check pp disbursements',
  'trigger pp disbursement',
  'spawn pp dd failure checks',
  'background batch',
  'pp check for dd failure',
  'webhook',
  'finalise pp batched disbursement',
  'create transaction',
  'notification',
  'transaction-check',
  'batch-notification',
  'disbursement',
  'transaction-sync',
  'bulk-transaction-check',
  'batch-check',
  'wallet-check',
  'refund-check',
  'batch-fund-invoices',
  'journal-discrepancy',
  'funds-holder-balance-check',
  'revert-held-funds',
  'funding-allowlist',
  'disbursement-check',
  'cache-payment-method-balance',
];

const log = logger.child({ action: 'job-scheduler' });

const adapter = new SequelizeAdapter(DATABASE_URI);

export const jobScheduler = new JobScheduler({
  logger: log,
  defaultRunInterval: DEFAULT_JOB_LAG,
  jobsRiskyToRestart: ['batch-disbursement'],
  jobsSafeToRestart,
  jobsCustomRestart: {},
  tasks: {
    'check pp items': ppItemHandler,
    'check pp disbursements': ppDisbursementChecker,
    'trigger pp disbursement': ppDisbursementTrigger,
    'background batch': batchDownload,
    webhook: async data => webhookTask.publish(data),
    'finalise pp batched disbursement': ppFinaliseDisbursement,
    'create transaction': transactionCreator,
    notification: async data => notificationTask.publish(data),
    'transaction-check': async data => transactionCheckTask.publish(data),
    'batch-notification': async data => batchNotificationTask.publish(data),
    disbursement: async data => disbursementsTask.publish(data),
    'transactions-sync': async data => transactionSyncTask.publish(data),
    'bulk-transaction-check': async data =>
      bulkTransactionCheckTask.publish(data),
    'batch-check': async data => batchCheckTask.publish(data),
    'wallet-check': async data => walletCheckTask.publish(data),
    'refund-check': async data => refundCheckTask.publish(data),
    'transaction-sync': async data => transactionSyncTask.publish(data),
    'batch-disbursement': async data => batchDisbursementsTask.publish(data),
    'disbursement-check': async data => disbursementCheckTask.publish(data),
    'batch-fund-invoices': batchFundInvoicesTask,
    'journal-discrepancy': journalDiscrepancyTask,
    'funds-holder-balance-check': fundsHolderBalanceTask,
    'revert-held-funds': async data => reverseHeldFundsTask.publish(data),
    'finstro-invoice-payment-task': async data =>
      finstroInvoicePaymentTask.publish(data),
    'cache-payment-method-balance': async data =>
      cachePaymentMethodBalanceTask.publish(data),
    'funding-allowlist': async data => fundingAllowListTask.publish(data),
  },
  adapter,
  metrics: statsd,
  service: 'payments',
});

export default jobScheduler;
