/* eslint-disable camelcase */
import { Model, Sequelize, DataTypes } from 'sequelize';
import { associable } from './types';
import { UserInstance } from './user';

export interface UserSettingAttributes {
  id?: string;
  settlementDelayDays?: number | null;
  manualDisbursement?: boolean | null;
  isSupplier?: boolean | null;
  netSettlement?: boolean | null;
  reference?: number | null;
  settlementRate?: string | null;
  weeklySettlement?: boolean | null;
  companyDescriptor?: string | null;
  userId: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  user?: UserInstance;
}

// skipcq: JS-0322 - no properties are being defined in the model
export interface UserSettingModel extends UserSettingAttributes {}

export interface UserSettingInstance
  extends Model<UserSettingModel, UserSettingAttributes>,
    UserSettingModel {}

export default (sequelize: Sequelize) => {
  const fields: SequelizeAttributes<UserSettingModel> = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
      allowNull: false,
    },
    settlementDelayDays: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'settlement_delay_days',
    },
    manualDisbursement: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      field: 'manual_disbursement',
    },
    isSupplier: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      field: 'is_supplier',
    },
    netSettlement: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
      field: 'net_settlement',
    },
    reference: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    settlementRate: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'settlement_rate',
    },
    weeklySettlement: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      field: 'weekly_settlement',
    },
    companyDescriptor: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'company_descriptor',
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
      references: { model: 'users', key: 'id' },
      onUpdate: 'cascade',
      onDelete: 'cascade',
    },
    createdAt: {
      type: DataTypes.DATE,
      field: 'created_at',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updatedAt: {
      type: DataTypes.DATE,
      field: 'updated_at',
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    deletedAt: {
      type: DataTypes.DATE,
      field: 'deleted_at',
    },
  };

  const UserSetting: associable<UserSettingInstance> = sequelize.define<
    UserSettingInstance,
    UserSettingAttributes
  >('UserSetting', fields, {
    paranoid: false,
    tableName: 'user_settings',
    underscored: true,
  });

  UserSetting.associate = models => {
    UserSetting.belongsTo(models.User, {
      foreignKey: 'user_id',
    });
  };

  return UserSetting;
};
