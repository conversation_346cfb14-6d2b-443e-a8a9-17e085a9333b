import { Model, Sequelize, DataTypes } from 'sequelize';
import { Backend } from './transaction';
import { SubscriptionInstance } from './subscription';
import { UserSettingInstance } from './user_setting';
import { associable } from './types';
/* eslint-disable camelcase */

export type UserConfiguration = {
  netSettlement?: boolean;
  settlementRate?: number;
  manualDisbursement?: boolean;
  stopCredit?: boolean;
  settlementPrefix?: string;
  walletFunds?: boolean;
  disbursementLimit?: number | string | null;
  fallback?: boolean;
  weeklySettlement?: boolean;
  settlementDelay?: number;
  rates?: {
    amex?: number;
    mastercard?: number;
    visa?: number;
    direct?: number;
  };
  reference?: number;
  paymentRunStatus?: 'paysOnInvoice' | 'paysUnderSeparateSupplier';
  supplier?: boolean;
  companyDescriptor?: string;
  walletRefunds?: boolean;
  /**
   * An account level setting for turning on journals for the user
   */
  journals?: boolean;
  /**
   * Config for funding invoices to a supplier user
   * For more info: https://ordermentum.atlassian.net/wiki/spaces/OD/pages/**********/Accelerated+payments+trial
   */
  funding?: {
    /**
     * Allow list for venues for whom invoices will funded
     */
    purchaserIds?: string[];
    enabled?: boolean;
  };
  stripeSyncedAt?: string;
};
export interface UserAttributes {
  id?: string;
  first_name: string;
  last_name: string;
  email: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  state: string;
  postcode: string;
  country: string;
  backend: Backend;
  mobile_number?: string;
  dob?: string;
  settlement_webhook: string;
  company_name?: string;
  company_tax_number: string;
  company_address_line_1: string;
  company_address_line_2: string;
  company_city: string;
  company_state: string;
  company_postcode: string;
  company_country: string;
  dd_settlement_delay_hours: number;
  batch_settlement_payments: boolean;
  company_legal_name: string;
  ordermentum_id: string;
  configuration: UserConfiguration;
  external_id: string;
  stripe_customer_id: string;
  status: 'active' | 'pending' | 'archived';
  payment_method_id?: string;
  payment_method_type?: 'bank' | 'card';
  created_at?: string;
  updated_at?: string;
  deleted_at?: string;
  subscriptions?: SubscriptionInstance[];
  userSetting?: UserSettingInstance;
}

export type Address = {
  line_1: string;
  line_2: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
};

export type SerializeAttributes = {
  id: string;
  email: string;
  mobile_number: string;
  dob: string;
  settlement_webhook: string;
  dd_settlement_delay_hours;
  batch_settlement_payments;
  ordermentum_id: string;
  external_id: string;
  stripe_customer_id: string;
  payment_method_id?: string;
  payment_method_type?: 'bank' | 'card';
  configuration?: UserConfiguration;
  status?: 'active' | 'pending' | 'archived';
  name: {
    first: string;
    last: string;
  };
  company: {
    name: string;
    tax_number: string;
    legal_name: string;
    address: Address;
  };
  address: Address;
  backend: Backend;
};

export interface UserModel extends UserAttributes {
  id: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  dob: string;
  mobile_number: string;
}

export interface UserInstance
  extends Model<UserModel, UserAttributes>,
    UserModel {}

type UserPrototype = associable<UserInstance> & {
  fromBody: (body: SerializeAttributes) => UserAttributes;
  toBody: (body: UserInstance) => SerializeAttributes;
};

export default (sequelize: Sequelize) => {
  const fields: SequelizeAttributes<UserModel> = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    first_name: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    last_name: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    email: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    address_line_1: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    address_line_2: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    city: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    state: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    postcode: {
      allowNull: false,
      type: DataTypes.STRING,
    },
    country: {
      allowNull: false,
      type: DataTypes.STRING(3),
    },
    backend: {
      allowNull: false,
      type: DataTypes.ENUM('promisepay', 'stripe'),
    },
    mobile_number: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    dob: {
      allowNull: true,
      type: DataTypes.STRING(10),
    },
    settlement_webhook: {
      allowNull: true,
      type: DataTypes.STRING,
      defaultValue: '',
    },
    company_name: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_tax_number: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_address_line_1: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_address_line_2: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_city: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_state: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_postcode: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    company_country: {
      allowNull: true,
      type: DataTypes.STRING(3),
    },
    dd_settlement_delay_hours: {
      allowNull: false,
      defaultValue: 0,
      type: DataTypes.INTEGER,
    },
    batch_settlement_payments: {
      allowNull: false,
      defaultValue: true,
      type: DataTypes.BOOLEAN,
    },
    company_legal_name: {
      allowNull: true,
      type: DataTypes.STRING,
    },
    ordermentum_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    configuration: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    external_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stripe_customer_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('archived', 'active'),
      allowNull: false,
      defaultValue: 'active',
    },
    payment_method_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    payment_method_type: {
      type: DataTypes.ENUM('bank', 'card'),
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  };

  const opts = {
    paranoid: true,
    tableName: 'users',
    underscored: true,
  };

  const User = <UserPrototype>(
    sequelize.define<UserInstance, UserAttributes>('User', fields, opts)
  );

  User.associate = models => {
    User.hasMany(models.Subscription, {
      foreignKey: 'user_id',
      as: 'subscriptions',
    });
    User.hasMany(models.BankPaymentMethod, {
      foreignKey: 'user_id',
      as: 'bank_payment_method',
    });
    User.hasMany(models.CardPaymentMethod, {
      foreignKey: 'user_id',
      as: 'card_payment_method',
    });
    User.hasOne(models.UserSetting, {
      foreignKey: 'user_id',
      as: 'userSetting',
      onDelete: 'CASCADE',
    });
  };

  User.toBody = i => ({
    id: i.id,
    created_at: i.created_at,
    updated_at: i.updated_at,
    name: {
      first: i.first_name,
      last: i.last_name,
    },
    email: i.email,
    address: {
      line_1: i.address_line_1,
      line_2: i.address_line_2,
      city: i.city,
      state: i.state,
      postcode: i.postcode,
      country: i.country,
    },
    mobile_number: i.mobile_number,
    dob: i.dob,
    settlement_webhook: i.settlement_webhook,
    company: {
      name: i.company_name ?? '',
      tax_number: i.company_tax_number,
      legal_name: i.company_legal_name,
      address: {
        line_1: i.company_address_line_1,
        line_2: i.company_address_line_2,
        city: i.company_city,
        state: i.company_state,
        postcode: i.company_postcode,
        country: i.company_country,
      },
    },
    dd_settlement_delay_hours: i.dd_settlement_delay_hours,
    batch_settlement_payments: i.batch_settlement_payments,
    ordermentum_id: i.ordermentum_id,
    external_id: i.external_id,
    stripe_customer_id: i.stripe_customer_id,
    payment_method_id: i.payment_method_id,
    payment_method_type: i.payment_method_type,
    status: i.status,
    backend: i.backend,
    userSetting: i.userSetting,
  });

  User.fromBody = body => {
    const user = { ...body };
    if (!user.company)
      user.company = {
        name: '',
        legal_name: '',
        tax_number: '',
        address: {
          line_1: '',
          line_2: '',
          city: '',
          state: '',
          postcode: '',
          country: '',
        },
      };
    return {
      id: user.id,
      first_name: user.name.first,
      last_name: user.name.last,
      email: user.email,
      address_line_1: user.address.line_1,
      address_line_2: user.address.line_2,
      city: user.address.city,
      state: user.address.state,
      backend: user.backend,
      postcode: user.address.postcode,
      country: user.address.country,
      mobile_number: user.mobile_number,
      dob: user.dob,
      settlement_webhook: user.settlement_webhook,
      company_name: user.company.name,
      company_legal_name: user.company.legal_name,
      company_tax_number: user.company.tax_number,
      company_address_line_1: user.company.address.line_1,
      company_address_line_2: user.company.address.line_2,
      company_city: user.company.address.city,
      company_state: user.company.address.state,
      company_postcode: user.company.address.postcode,
      company_country: user.company.address.country,
      dd_settlement_delay_hours: user.dd_settlement_delay_hours,
      batch_settlement_payments: user.batch_settlement_payments,
      ordermentum_id: user.ordermentum_id,
      external_id: user.external_id,
      stripe_customer_id: user.stripe_customer_id,
      payment_method_id: user.payment_method_id,
      payment_method_type: user.payment_method_type,
      status: user.status || 'active',
      configuration: user.configuration ?? {},
    };
  };

  return User;
};
