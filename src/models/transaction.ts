/* eslint-disable camelcase */
import { Model, Sequelize, DataTypes } from 'sequelize';
import { CurrencyCodes } from '../types/currency_codes';
import { UserInstance } from './user';
import { PaymentMethod } from './payment_methods';
import { BankPaymentMethodInstance } from './bank_payment_method';
import { CardPaymentMethodInstance } from './card_payment_method';
import { associable } from './types';
import { OperationStates } from '../types/journal_types';
import { CreateTransactionAttrs } from '../types/payment_types';

const DECLINE_REASON_LIMIT = 255;

export enum Backend {
  PROMISEPAY = 'promisepay',
  STRIPE = 'stripe',
  FOODBOMB = 'foodbomb',
  FINSTRO = 'finstro',
}

export function truncateDeclineReason(declineReason?: string | null): string {
  return (declineReason ?? '').substring(0, DECLINE_REASON_LIMIT);
}

export enum states {
  pending = 'pending',
  payment_pending = 'payment_pending',
  settlement_pending = 'settlement_pending',
  /**
   * Completed state is set after we receive the funds from the buyer
   */
  completed = 'completed',
  failed = 'failed',
  /**
   * Settled state is set after we disburse the funds to the recipient
   */
  settled = 'settled',
  settlement_failed = 'settlement_failed',
  noop = 'noop',
  refunded = 'refunded',
  partial_refund = 'partial_refund',
  refund_flagged = 'refund_flagged',
  payment_deposited = 'payment_deposited',
  batched = 'batched',
}

// TODO Architecture: Convert this to a state machine
// pending -> processing -> finalised
// pending -> finalised
// finalised -> X (no more changes)
export const TransactionStates = {
  paid: [states.completed, states.payment_deposited, states.payment_pending],
  completed: [states.completed],
  failed: [states.failed],
  finalized: [
    states.completed,
    states.payment_deposited,
    states.failed,
    states.refunded,
    states.partial_refund,
    states.refund_flagged,
  ],
  pending: [states.pending],
};

/**
 * Returns true if the transaction has completed successfully
 */
export function toOperationState(state: states): OperationStates {
  switch (state) {
    case states.completed:
    case states.refunded:
    case states.settled:
    case states.noop:
      return OperationStates.Succeeded;

    case states.failed:
    case states.settlement_failed:
      return OperationStates.Failed;

    default:
      return OperationStates.Pending;
  }
}

/**
 * Async transactions are currently only supported by Zai/Promisepay
 * https://developer.hellozai.com/reference/patch_items-id-make-payment
 */
export function isTransactionAsync(payload: CreateTransactionAttrs): boolean {
  return (
    Boolean(payload.tryAsync) &&
    payload.backend === Backend.PROMISEPAY &&
    (payload.useWalletAccount ||
      (Boolean(payload.card_payment_method_id) &&
        !payload.bank_payment_method_id))
  );
}

// TODO JOURNAL_V3: The use of the word transaction is ambiguous and should be renamed 'action' as in PaymentGatewayAction (and actions should record events next to them i.e. webhook callbacks)

// https://developer.assemblypayments.com/docs/statuses#batches
export enum BatchTransactionState {
  successful = 'successful',
  pending_successful = 'pending_successful',
  pending = 'pending',
  bpay_pending = 'bpay_pending',
  batched = 'batched',
  invalid_account_details = 'invalid_account_details',
  failed_direct_debit = 'failed_direct_debit',
  bank_processing = 'bank_processing',
  errored = 'errored',
}

export type BatchTransactionResponse = {
  batch_transactions?: BatchTransaction;
};

export type BatchTransaction = {
  created_at?: string;
  updated_at?: string;
  status: number;
  id: string;
  disbursement_bank: string | null;
  processing_bank: string | null;
  type: BatchTransactionType;
  type_method: BatchTransactionMethodType;
  batch_id: number;
  cuscal_payment_transaction_id: string | null;
  reference: string;
  state: 'batched' | 'successful' | 'failed_direct_debit';
  deposit_reference: string | null;
  user_id: string;
  account_id: string;
  from_user_name: string;
  from_user_id: string;
  refund_state: string | null;
  account_type: 'bank_account' | 'wallet_account' | 'card_account';
  amount: number;
  currency: string;
  country: string | null;
  debit_credit: 'debit' | 'credit';
  item: {
    id: 'string';
    item_number: 'string';
  };
  description: string;
  related: {
    account_to: {
      id: string;
      account_type: string;
      user_id: string;
    };
  };
};

export type PaymentType =
  | PaymentMethod
  | 'visa'
  | 'mastercard'
  | 'amex'
  | 'direct'
  | 'unknown'
  | 'foodbomb'
  | 'wallet';

/**
 * Capture: Charging a card or bank account (Sender to Recipient)
 * Refund: Refunding to a card or bank account (Recipient to Sender)
 * Accounting: Have a transaction that doesn't actually move funds. Added for compatibility with FoodBomb.
 * Escrow: The now defunct flows like upfront v1 that used a holding wallet under our control to temporarily hold funds.
 *
 * FUTURE: There is a new gateway feature called "pre-auth" that instructs the institution to hold the funds for a period of time.
 *         This is not currently supported by the gateway, but will be in the future.
 *         This will simplify the new upfront redesign flows even more.
 *         https://support.hellozai.com/card-pre-authentication
 */
export type TransactionType = 'capture' | 'refund' | 'accounting' | 'escrow';

export enum BatchTransactionType {
  payment = 'payment',
  refund = 'refund',
  disbursement = 'disbursement',
  fee = 'fee',
  deposit = 'deposit',
  withdrawal = 'withdrawal',
  payment_funding = 'payment_funding',
}

export enum BatchTransactionMethodType {
  direct_debit = 'direct_debit',
  credit_card = 'credit_card',
  npp_payin = 'npp_payin',
  bpay = 'bpay',
  wallet_account_transfer = 'wallet_account_transfer',
  direct_credit = 'direct_credit',
  wire_transfer = 'wire_transfer',
  new_payments_platform = 'new_payments_platform',
  misc = 'misc',
}

/**
 * @deprecated This style of workflow is deprecated in favour of Temporal Workflows
 */
export const Workflow = {
  HoldFundsInWallet: 'hold_funds_in_wallet',
} as const;
type ObjectValues<T> = T[keyof T];

/**
 * @deprecated This style of workflow is deprecated in favour of Temporal Workflows
 */
export type WorkflowType = ObjectValues<typeof Workflow>;

// Please ensure change to this is backward compatible
export interface TransactionContext {
  isZaiAsync?: boolean;
  workflow?: WorkflowType;

  /**
   * Used in HoldFundsInWallet, false means up front payment
   */
  immediate?: boolean;

  /**
   * Used in HoldFundsInWallet, for reimbursing to different backend
   */
  backend?: Backend;

  /**
   * Zai give us back for async payment, saved for debugging
   */
  statusLink?: string;
  callbacksLink?: string;

  walletPayment?: boolean;

  [key: string]: any;
}

/**
 * Attributes of a Payment Gateway transaction
 * This is not to be confused with a Journal Transaction
 *
 * TODO: Rename to something like PaymentGatewayActionAttributes
 */
export interface TransactionAttributes {
  id?: string;
  seller_id: string;
  buyer_id: string;
  amount: number;
  log?: any[];
  currency?: CurrencyCodes;
  description: string;
  name: string;
  release_at?: string;
  received_at?: string;
  state: states;
  declineReason?: string;
  declineCode?: string | null;
  backend: Backend;
  webhook?: string;
  reversal_webhook?: string;
  payment_method?: any;
  settlementAmount?: number;
  settlement_delay_hours?: number;
  settlement_id?: string | null;
  is_reversal?: boolean;
  batchId?: string | null;
  userId?: string | null;
  refundedById?: string | null;
  relatedTransactions?: string[];
  bank_payment_method_id?: string | null;
  card_payment_method_id?: string | null;
  wallet_payment_method_id?: string | null;
  type?: PaymentType;
  lockedAt?: string | null;
  fee?: number;
  settledAt?: string | null;
  sentAt?: string | null;
  reference?: string;
  discount?: string;
  invoiceIds?: string[];
  created_at?: string;
  settled_at?: string;
  updated_at?: string;
  refunded_at?: string | null;
  refund_amount?: number;
  refund_data?: {
    [key: string]: string | number;
  } | null;
  fundedAt?: string | null;
  orderIds?: string[];
  transactionType?: TransactionType;
  externalChargeId?: string;
  paymentMethodId?: string | null;
  context?: TransactionContext | null;
}

/**
 * Payment Gateway transaction model
 * This is not to be confused with a Journal Transaction
 */
export type TransactionModel = TransactionAttributes & {
  id: string;
  reference: string;
  settlementAmount: number;
  invoiceIds: string[];
  orderIds: string[];
  type: PaymentType;
  transactionType: TransactionType;
  seller?: UserInstance;
  buyer?: UserInstance;
  bankPaymentMethod?: BankPaymentMethodInstance;
  cardPaymentMethod?: CardPaymentMethodInstance;
};

export interface TransactionInstance
  extends Model<TransactionModel, TransactionAttributes>,
    TransactionModel {}

export type SerializeAttributes = {
  id?: string;
  seller_id: string;
  buyer_id: string;
  amount: number;
  log?: any[];
  currency: CurrencyCodes;
  payment_method: any;
  description: string;
  name: string;
  release_at: string;
  received_at: string;
  state: states;
  declineReason: string;
  declineCode: string | null;
  backend: `${Backend}`;
  webhook: string;
  reversal_webhook: string;
  settlementAmount?: string;
  settlement_delay_hours: number;
  settlement_id: string;
  is_reversal: boolean;
  batchId: string;
  userId?: string;
  refundedById?: string;
  relatedTransactions: string[];
  invoiceIds?: string[];
  orderIds?: string[];
  reference: string;
  created_at?: string;
  updated_at?: string;
  bank_payment_method_id?: string;
  card_payment_method_id?: string;
  wallet_payment_method_id?: string;
  discount: string;
  refunded_at?: string | null;
  refund_amount?: number;
  refund_data?: {
    [key: string]: string | number;
  };
  fundedAt?: string;
  transactionType?: TransactionType;
  externalChargeId?: string;
  paymentMethodId?: string;
  type?: PaymentType;
  context?: TransactionContext | null;
};

type TransactionPrototype = associable<TransactionInstance> & {
  states: typeof states;
  fromBody: (body: SerializeAttributes) => TransactionAttributes;
  toBody: (body: TransactionAttributes) => SerializeAttributes;
  toEvent: (body: any) => any;
};

export const builder = (sequelize: Sequelize) => {
  const fields: SequelizeAttributes<TransactionModel> = {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    seller_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    buyer_id: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    log: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
    },
    currency: {
      type: DataTypes.STRING(3),
      allowNull: false,
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    release_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    received_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    declineReason: {
      type: DataTypes.STRING,
      field: 'decline_reason',
    },
    declineCode: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'decline_code',
    },
    backend: {
      allowNull: false,
      type: DataTypes.ENUM('promisepay', 'foodbomb'),
    },
    webhook: {
      type: DataTypes.STRING,
    },
    reversal_webhook: {
      type: DataTypes.STRING,
    },
    settlement_delay_hours: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    fee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0,
    },
    settlementAmount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      field: 'settlement_amount',
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'unknown',
    },
    lockedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
      field: 'locked_at',
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
      field: 'sent_at',
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: '',
      field: 'reference',
    },
    settledAt: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null,
      field: 'settled_at',
    },
    wallet_payment_method_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    settlement_id: {
      type: DataTypes.UUID,
      allowNull: true,
    },
    is_reversal: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    batchId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'batch_id',
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'user_id',
    },
    bank_payment_method_id: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'bank_payment_method_id',
    },
    card_payment_method_id: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'card_payment_method_id',
    },
    refundedById: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'refunded_by_id',
    },
    relatedTransactions: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
      field: 'related_transactions',
    },
    invoiceIds: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
      field: 'invoice_ids',
    },
    refunded_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    refund_amount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    refund_data: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
    },
    fundedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'funded_at',
    },
    created_at: {
      type: DataTypes.DATE,
      field: 'created_at',
    },
    updated_at: {
      type: DataTypes.DATE,
      field: 'updated_at',
    },
    orderIds: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: [],
      field: 'order_ids',
    },
    transactionType: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'capture',
      field: 'transaction_type',
    },
    externalChargeId: {
      type: DataTypes.TEXT,
      field: 'external_charge_id',
    },
    paymentMethodId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'payment_method_id',
    },
    context: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
    },
  };

  const Transaction = <TransactionPrototype>sequelize.define<
    TransactionInstance,
    TransactionModel
  >('Transaction', fields, {
    paranoid: true,
    tableName: 'transactions',
    underscored: true,
  });

  Transaction.associate = models => {
    Transaction.belongsTo(models.BankPaymentMethod, {
      as: 'bankPaymentMethod',
    });
    Transaction.belongsTo(models.CardPaymentMethod, {
      as: 'cardPaymentMethod',
    });
    Transaction.belongsTo(models.PaymentMethod);
    Transaction.belongsTo(models.Settlement, {
      foreignKey: { name: 'settlement_id', allowNull: true },
    });
    Transaction.belongsTo(models.User, {
      as: 'seller',
      foreignKey: {
        name: 'seller_id',
        allowNull: false,
      },
    });
    Transaction.belongsTo(models.User, {
      as: 'buyer',
      foreignKey: {
        name: 'buyer_id',
        allowNull: false,
      },
    });
  };

  Transaction.states = states;

  Transaction.fromBody = body => {
    const res: TransactionAttributes = {
      amount: body.amount,
      currency: body.currency,
      state: Transaction.states.pending,
      description: body.description,
      id: body.id,
      name: body.name,
      seller_id: body.seller_id,
      backend: (body.backend as Backend) || Backend.PROMISEPAY,
      buyer_id: body.buyer_id,
      settlementAmount: body.amount,
      declineReason: '',
      declineCode: null,
      invoiceIds: body.invoiceIds,
      orderIds: body.orderIds,
      webhook: body.webhook,
      is_reversal: body.is_reversal,
      refunded_at: body.refunded_at,
      refund_amount: body.refund_amount,
      refund_data: body.refund_data,
      fundedAt: body.fundedAt,
      transactionType: body.transactionType,
      externalChargeId: body.externalChargeId,
      release_at: body.release_at,
      received_at: body.received_at,
      type: body?.type,
      context: body.context,
    };

    if (body.payment_method?.type === 'bank') {
      res.bank_payment_method_id = body.payment_method.id;
    }

    if (body.payment_method?.type === 'card') {
      res.card_payment_method_id = body.payment_method.id;
    }

    if (body.payment_method?.type === 'wallet') {
      res.wallet_payment_method_id = body.payment_method.id;
    }

    if (!['bank', 'card', 'wallet'].includes(body.payment_method?.type))
      res.paymentMethodId = body.payment_method?.id;

    res.discount = body.discount;

    return res;
  };

  Transaction.toBody = transaction => {
    const res: SerializeAttributes = Object.keys(
      transaction
    ).reduce<SerializeAttributes>((r, k) => {
      const v = transaction[k];
      if (v === null) return r;
      let s;
      if (v instanceof Date) s = v.toISOString();
      r[k] = s || v;
      return r;
    }, {} as SerializeAttributes);
    if (transaction.bank_payment_method_id) {
      res.payment_method = {
        id: transaction.bank_payment_method_id,
        type: 'bank',
      };
    }
    if (transaction.card_payment_method_id) {
      res.payment_method = {
        id: transaction.card_payment_method_id,
        type: 'card',
      };
    }
    if (transaction.wallet_payment_method_id) {
      res.payment_method = {
        id: transaction.wallet_payment_method_id,
        type: 'wallet',
      };
    }
    if (transaction.paymentMethodId) {
      res.payment_method = {
        id: transaction.paymentMethodId,
      };
    }
    return res;
  };

  Transaction.toEvent = body => {
    const model: any = {
      id: body.id,
      sellerId: body.seller_id,
      buyerId: body.buyer_id,
      amount: body.amount,
      log: body.log ?? [],
      currency: body.currency,
      state: Transaction.states.pending,
      description: body.description,
      name: body.name,
      backend: Backend.PROMISEPAY,
      settlementAmount: body.settlementAmount,
      settlementDelayHours: body.settlement_delay_hours,
      settlementId: body.settlement_id,
      declineReason: body.decline_reason ?? '',
      declineCode: body.decline_code ?? null,
      invoiceIds: body.invoiceIds,
      orderIds: body.orderIds,
      webhook: body.webhook,
      isReversal: body.is_reversal,
      refundedAt: body.refunded_at,
      refundAmount: body.refund_amount,
      refundData: body.refund_data,
      fundedAt: body.fundedAt,
      paymentMethod: body.payment_method,
      discount: body.discount,
      releaseAt: body.release_at,
      receivedAt: body.received_at,
      reversalWebhook: body.reversal_webhook,
      batchId: body.batch_id,
      userId: body.user_id,
      relatedTransactions: body.relatedTransactions ?? [],
      type: body.type,
      lockedAt: body.locked_at,
      fee: body.fee,
      settledAt: body.settledAt,
      sentAt: body.sentAt,
      reference: body.reference,
      createdAt: body.created_at,
      updatedAt: body.updated_at,
      refundedById: body.refundedById,
      paymentMethodId: body.paymentMethodId,
      context: body.context,
    };

    if (body.payment_method) {
      if (body.payment_method.type === 'bank') {
        model.bankPaymentMethodId = body.payment_method.id;
      }

      if (body.payment_method.type === 'card') {
        model.cardPaymentMethodId = body.payment_method.id;
      }

      if (body.payment_method.type === 'wallet') {
        model.walletPaymentMethodId = body.payment_method.id;
      }
    }

    return model;
  };

  return Transaction;
};

export default builder;
