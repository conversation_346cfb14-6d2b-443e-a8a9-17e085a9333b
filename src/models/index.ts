import { Sequelize } from 'sequelize';
import config from 'config';
import logger from '../lib/logger';
import bankDetailFactory from './bank_detail';
import bankPaymentMethodFactory from './bank_payment_method';
import batchTransactionFactory from './batch_transaction';
import cardPaymentMethodFactory from './card_payment_method';
import disbursementFactory from './disbursement';
import userFactory from './user';
import jobFactory from './job';
import ledgerFactory from './ledger_v2_model';
import paymentRunFactory from './payment_run';
import paymentRunChargesFactory from './payment_run_charges';
import settlementFactory from './settlement';
import transactionFactory from './transaction';
import batchFactory from './batch';
import chargeFactory from './charge';
import subscriptionFactory from './subscription';
import subscriptionInvoiceFactory from './subscription_invoice';
import subscriptionUsageFactory from './subscription_usage';
import journalFactory from './journal_v2';
import journalEntryFactory from './journal_v2_entry';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import paymentMethodFactory from './payment_methods';
import { JournalV3, journalV3Init } from './journal_v3_model';
import { ledgerV3ModelInit } from './ledger_v3_model';
import { JournalEntryV3, journalV3EntryInit } from './journal_v3_entry_model';
import userSettingFactory from './user_setting';

const DATABASE_URI = config.get<string>('DATABASE_URI');
const DB_POOL_SIZE = config.get<number>('DB_POOL_SIZE');

const safeParseInt = (concurrency, fallback = 1) => {
  if (!concurrency) {
    return fallback;
  }

  const result = parseInt(concurrency, 10);
  if (Number.isNaN(result)) {
    return fallback;
  }

  return result;
};

const sequelize = new Sequelize(DATABASE_URI, {
  logging: l => logger.debug(l),
  pool: {
    max: safeParseInt(DB_POOL_SIZE, 5),
    min: 0,
    idle: 1000 * 60,
    acquire: 1000 * 60,
  },
  retry: {
    match: [/concurrent update/],
    max: 5,
  },
});

const Batch = batchFactory(sequelize);
const BankDetail = bankDetailFactory(sequelize);
const BankPaymentMethod = bankPaymentMethodFactory(sequelize);
const BatchTransaction = batchTransactionFactory(sequelize);
const CardPaymentMethod = cardPaymentMethodFactory(sequelize);
const Disbursement = disbursementFactory(sequelize);
const Job = jobFactory(sequelize);
const Ledger = ledgerFactory(sequelize);
const PaymentRun = paymentRunFactory(sequelize);
const PaymentRunCharge = paymentRunChargesFactory(sequelize);
const Settlement = settlementFactory(sequelize);
const Transaction = transactionFactory(sequelize);
const User = userFactory(sequelize);
const Charge = chargeFactory(sequelize);
const Subscription = subscriptionFactory(sequelize);
const SubscriptionInvoice = subscriptionInvoiceFactory(sequelize);
const SubscriptionUsage = subscriptionUsageFactory(sequelize);
const Journal = journalFactory(sequelize);
const JournalEntry = journalEntryFactory(sequelize);
const PaymentMethod = paymentMethodFactory(sequelize);
const UserSetting = userSettingFactory(sequelize);

journalV3EntryInit(sequelize);
journalV3Init(sequelize);
ledgerV3ModelInit(sequelize);

const db = {
  Batch,
  BankDetail,
  BankPaymentMethod,
  BatchTransaction,
  CardPaymentMethod,
  Disbursement,
  Job,
  /**
   * @deprecated In favour of Journals (Use src/lib/journal_transaction.ts)
   */
  Ledger,
  PaymentRun,
  PaymentRunCharge,
  Settlement,
  Transaction,
  User,
  Charge,
  Subscription,
  SubscriptionInvoice,
  SubscriptionUsage,
  Journal,
  JournalEntry,
  PaymentMethod,
  UserSetting,
};

// TODO: This pattern is not compatible with the typescript way of declaring sequelize definitions (https://sequelize.org/docs/v6/other-topics/typescript/)
// TODO: Eventually change declarations to typescript from JS decls and retire.
// NOTE: Any new table definitions should use the typescript syntax
Object.keys(db).forEach(modelName => {
  if ('associate' in db[modelName]) {
    db[modelName].associate(db);
  }
});

JournalV3.hasMany(JournalEntryV3, {
  as: 'entries',
  foreignKey: 'journal_id',
});

JournalEntryV3.belongsTo(JournalV3, {
  as: 'journal',
  foreignKey: 'journal_id',
});

// Journal Transaction is an abstraction that uses the journal and journal entry models
// to facilitate a double entry accounting ledger
JournalTransaction.setDbModels(Journal, JournalEntry);

export {
  BankDetail,
  BankPaymentMethod,
  BatchTransaction,
  CardPaymentMethod,
  Disbursement,
  Job,
  PaymentRun,
  PaymentRunCharge,
  Settlement,
  Batch,
  Transaction,
  User,
  Charge,
  Subscription,
  SubscriptionInvoice,
  SubscriptionUsage,
  sequelize,
  Sequelize,
  PaymentMethod,
  UserSetting,
};
