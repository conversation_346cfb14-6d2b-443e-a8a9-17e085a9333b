/* eslint-disable camelcase */
import BigNumber from 'bignumber.js';
import moment from 'moment-timezone';
import { Transaction as SequelizeTransaction } from 'sequelize';
import Logger from 'bunyan';
import { sequelize } from '../models';
import { TransactionInstance, states } from '../models/transaction';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { systemConfig } from '../system_config';
import { JournalName } from '../models/journal_v2';
import { CurrencyCodes } from '../types/currency_codes';
import { changeEvent } from './transaction_change_event';
import { RefundActionData } from '../actions/refund';

// created using logic extracted from src/lib/backends/promisepay/refund_transaction.ts to re-use between promisepay and finstro

export type RefundTransactionParams = {
  transaction: TransactionInstance;
  data: RefundActionData;
  log: Logger;
};

export type RefundInfo = {
  state: states.refunded | states.refund_flagged | states.partial_refund;
  refundedById: string;
  settlementAmount: string;
  refundAmount: number;
  refundData: {
    [invoiceId: string]: string | number;
  };
};

export const calculateSettlementTotal = (
  amount: number, // cents
  refundAmount: number // cents
) => {
  const total = new BigNumber(amount).dividedBy(100);
  const refund = new BigNumber(refundAmount).dividedBy(100);
  return total.minus(refund).multipliedBy(100).toFixed();
};

/**
 * Makes pending entries for refunds before it tries to process refunds
 * @param transactionId
 * @param {
            amount: number;
            seller: string;
            buyer: string;
          }
 */
export const addJournalV2 = async (
  transaction: TransactionInstance,
  {
    amount,
    seller,
    buyer,
  }: {
    amount: number;
    seller: string;
    buyer: string;
  },
  log: Logger,
  dbTransaction?: SequelizeTransaction
) => {
  const journal = new JournalTransaction(transaction.id);
  try {
    if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
      // Create pending journal entries between the seller's and buyer's wallet account
      await journal.credit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer,
        amount,
      });
      await journal.debit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller,
        amount,
      });
      await journal.commit(dbTransaction);
    }
    return journal;
  } catch (err) {
    log.error(err, 'Failed to add pending journal entries');
    throw err;
  }
};

export const markTransactionRefunded = async (
  transaction: TransactionInstance,
  {
    state,
    refundedById,
    settlementAmount,
    refundAmount,
    refundData,
    dbTransaction,
  }: RefundInfo & {
    dbTransaction?: SequelizeTransaction;
  }
) =>
  transaction.update(
    {
      state,
      refunded_at: moment().toISOString(),
      refundedById,
      settlementAmount: Number(settlementAmount),
      refund_amount: refundAmount,
      declineReason: '', // this was rename from failure_message
      refund_data: refundData,
      sentAt: null,
      settledAt: null,
    },
    {
      transaction: dbTransaction,
    }
  );

export const finaliseRefund = async (
  transaction: TransactionInstance,
  {
    state,
    refundedById,
    settlementAmount,
    refundAmount,
    refundData,
  }: RefundInfo,
  log: Logger
) => {
  const previousEventModel = transaction;
  const txn = await sequelize.transaction();
  const journal = new JournalTransaction(transaction.id);
  try {
    await journal.markEntriesSettled(txn);
    await markTransactionRefunded(transaction, {
      state,
      refundedById,
      settlementAmount,
      refundAmount,
      refundData,
      dbTransaction: txn,
    });
    await txn.commit();
    const currentEventModel = transaction;
    await changeEvent(currentEventModel, previousEventModel);
  } catch (err) {
    await journal.markEntriesArchived();
    const message = `Failed to mark transaction ${state}`;
    log.error(err, message);
    throw new Error(message);
  }
  return true;
};
