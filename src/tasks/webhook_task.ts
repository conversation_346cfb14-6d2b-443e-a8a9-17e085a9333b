import axios from 'axios';
import moment from 'moment-timezone';
import { JobContext } from '@ordermentum/scheduler';
import { logger } from '../lib/logger';
import { Job } from '../models';
import { send } from '../lib/slack';
import steveo from '../lib/steveo/steveo';
import { canRetryAxiosError } from '../lib/util';

const MAX_RETRIES = 5;

type Message = {
  uri: string;
  body: any;
  transactionId: string;
  retries?: number;
};

/**
 * The main entrypoint for the task handler
 */
export async function webhookHandler(data: Message, context?: JobContext) {
  const { body, uri, transactionId, retries = 0 } = data;
  const job = context?.job;
  const log = logger.child({
    jobId: job?.id,
    action: 'webhook_job',
    transactionId,
    retries,
  });

  // TODO JOURNAL_V3: How to attribute this to a journal record?

  if (retries >= MAX_RETRIES) {
    const msg = `<!here> Payment webhook retries reached maximum limit. Job Id - ${job?.id}`;
    await send(msg).catch(err =>
      log.error('Could not send message to slack channel', err)
    );
    log.info(`Max retries reached`);
    return false;
  }

  log.info('Calling webhook', uri, body);

  if (!uri) {
    log.error('no uri found. exiting');
    return false;
  }

  try {
    await axios.post(uri, body);
  } catch (e: any) {
    log.error(e);

    if (job && canRetryAxiosError(e)) {
      await Job.create({
        name: 'webhook',
        nextRunAt: moment()
          .add(1 * retries, 'minutes')
          .toISOString(),
        data: {
          ...data,
          retries: retries + 1,
        },
      });
    }

    return false;
  }

  log.info(`successfully triggered webhook`);
  return true;
}

export const webhookTask = steveo.task('webhook', webhookHandler);
export default webhookTask;
