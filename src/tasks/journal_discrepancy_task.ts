import moment from 'moment-timezone';
import { JobContext } from '@ordermentum/scheduler';
import logger from '../lib/logger';
import { send } from '../lib/slack';
import { Job, Transaction } from '../models';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { JournalEntryStatus } from '../models/journal_v2_entry';
import backends from '../lib/backends';
import { count } from '../lib/stats';
import { Backend } from '../models/transaction';

const providerFetchItem = {
  [Backend.PROMISEPAY]: backends[Backend.PROMISEPAY].getItem,
};

const MAX_RETRIES = 5;
/**
 * This task is responsible for verifying if journal entries linked with a payment transaction
 * are in sync with the payment transaction itself.
 * Means:
 * - If the transaction has failed, the journal entries should be archived
 * - If the transaction has succeeded, the journal entries should be settled
 *
 * If this is not the case, the task will archive or settle the journal entries accordingly.
 * Retries are done in case of failure with an exponential backoff delay.
 */
export const journalDiscrepancyTask = async (
  data: {
    transactionId?: string;
    retries?: number;
  },
  context: JobContext
) => {
  const { transactionId, retries = 1 } = data;
  const jobId = context.job?.id;
  if (!transactionId || !jobId) return false;

  const log = logger.child({
    task: 'journal-discrepancy',
    retries,
    jobId,
    transactionId,
  });

  try {
    const transaction = await Transaction.findByPk(transactionId);

    if (!transaction) {
      log.error(`Transaction not found ${transactionId}`);
      return false;
    }

    // TODO JOURNAL_V3: Review this and see how it affects the journals v3. It possibly doesn't apply to
    // TODO JOURNAL_V3: the v3 journal because it's a forward moving only journal.
    // TODO JOURNAL_V3: Possibly the approach is to perform a different validation for v3 🤷

    const journalEntries = await JournalTransaction.JournalEntry.findAll({
      where: {
        transactionId,
      },
      include: [
        {
          model: JournalTransaction.Journal,
          as: 'journal',
        },
      ],
    });

    if (!journalEntries.length) {
      log.error(`No journal entries found for transaction ${transactionId}`);
      return false;
    }

    const isPending = journalEntries.some(
      journalEntry => journalEntry.status === JournalEntryStatus.PENDING
    );

    if (!isPending) {
      return true;
    }

    const journal = new JournalTransaction(transactionId);

    const fetchItem = providerFetchItem[transaction.backend];

    if (!fetchItem) {
      log.warn(
        `Backend ${transaction.backend} is missing implementation for fetching transaction details`
      );
    } else {
      const providerData = await fetchItem(transactionId);

      // FIXME I think the bigger question here is how did it become orphaned? Logic issue on our side or missing API call from Zai? How can we either fix or overcome this?
      // If the transaction is not found in the provider, we should archive the journal entries
      // This is the case when a transaction is orphaned in our system
      if (!providerData) {
        await journal.markEntriesArchived();
        return true;
      }
    }

    // If a transaction is completed, the journal entries should be settled
    if (
      [
        Transaction.states.completed,
        Transaction.states.payment_deposited,
      ].includes(transaction.state)
    )
      await journal.markEntriesSettled();
    // If a transaction is failed, the journal entries should be archived
    else if (transaction.state === Transaction.states.failed)
      await journal.markEntriesArchived();
    return true;
  } catch (e) {
    log.error(e);

    if (retries >= MAX_RETRIES) {
      count('payments_failed_journal');
      const msg = `<!here> Journal entry discrepancy found for transaction ${transactionId}`;
      await send(msg).catch(err =>
        log.error('Could not send message to slack channel', err)
      );
      log.error('Max retries reached');
      return false;
    }

    await Job.create({
      name: 'journal-discrepancy',
      nextRunAt: moment()
        .add(2 ** retries, 'minutes')
        .toISOString(),
      data: {
        ...data,
        retries: retries + 1,
      },
    });
    return false;
  }
};
