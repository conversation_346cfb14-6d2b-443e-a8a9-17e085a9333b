import moment from 'moment-timezone';
import business from 'moment-business';
import Logger from 'bunyan';
import { randomUUID } from 'crypto';
import BigNumber from 'bignumber.js';
import config from 'config';
import { Job, Transaction } from '../models';
import steveo from '../lib/steveo/steveo';
import cache from '../lib/cache';
import { logger } from '../lib/logger';
import { client } from '../lib/backends/promisepay';
import { send } from '../lib/slack';
import backends from '../lib/backends';
import {
  TransactionInstance,
  Backend,
  truncateDeclineReason,
} from '../models/transaction';
import { createPaymentTransaction } from '../actions/create_payment_transaction';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { systemConfig } from '../system_config';
import { JournalName } from '../models/journal_v2';
import { CurrencyCodes } from '../types/currency_codes';
import { ProviderError } from '../lib/backends/error';
import { changeEvent } from '../helpers/transaction_change_event';

const OM_REFUND_USER_ID = config.get<string>('OM_REFUND_USER_ID');

/**
 * @summary
 * send slack message to support_alerts notifying invalid refund transaction
 *
 * @param {string} transactionId
 */
const sendFailureMessage = async (
  transactionId: string,
  log: Logger,
  message?: string
) => {
  const msg =
    message ??
    `
    <!here> Failed to get info for refund transaction ${transactionId}.
  (Refund transaction info not available) - https://dashboard.assemblypay.com/payments/items/${transactionId}
`;
  await send(msg).catch(err =>
    log.error('Could not send message to slack channel', err)
  );
};

/**
 * @summary
 * Reschedules this task again after 1 business day
 *
 * @param {JobData} payload
 */
const rescheduleJob = async (data: JobData) => {
  await Job.create({
    name: 'refund-check',
    nextRunAt: business
      .addWeekDays(moment.tz('Australia/Sydney'), '2')
      .toISOString(),
    data,
  });
};

/**
 * @summary
 * returns true -> if money is returned to buyer (supplier right now)
 * returns false -> if ^ fails, and then sends a slack message, updates original transaction, deletes the refund revert transaction and reschedules job to run after 1 business day
 *
 * @param {TransactionInstance} refundTransaction
 * @param {TransactionInstance} originalTransaction
 * @param {Logger} log
 * @returns {Promise<boolean>}
 */
const sendMoneyToBuyerWallet = async (
  refundTransaction: TransactionInstance,
  originalTransaction: TransactionInstance,
  log: Logger
): Promise<void> => {
  const refundReversalId = randomUUID();
  /**
   * A wallet to wallet transaction of the money we were holding (transferred from the supplier's bank account) in our refund wallet
   * back to the supplier's wallet
   * The motive behind the holding transaction and giving the money back is for de-risking ourselves.
   */
  const refundReversal = {
    seller_id: refundTransaction.buyer_id,
    buyer_id: OM_REFUND_USER_ID,
    amount: refundTransaction.amount,
    currency: refundTransaction.currency,
    description: `Refund Flagged Revert - ${refundTransaction.name}`,
    name: refundTransaction.id,
    state: Transaction.states.pending,
    backend: Backend.PROMISEPAY,
    relatedTransactions: [refundTransaction.id, originalTransaction.id],
    invoiceIds: [],
    id: refundReversalId,
    useWalletAccount: true,
    sentAt: new Date().toISOString(),
  };
  try {
    // TODO JOURNAL_V3: Add call with db transaction
    // await journal.create.refundReversal();

    await createPaymentTransaction(refundReversal, log);
  } catch (err: any) {
    const previousEventModel = originalTransaction;

    // TODO JOURNAL_V3: Handle failed call
    // await journal.complete.failed(transaction.id);

    await originalTransaction.update({
      state: Transaction.states.refund_flagged,
      declineCode: (err as ProviderError).code,
      declineReason: truncateDeclineReason((err as ProviderError).message),
    });

    const message = `
      <!here> Could not revert money back to supplier, ${refundReversalId} failed.
      Details - https://dashboard.assemblypay.com/payments/items/${refundReversalId}
    `;
    await sendFailureMessage(refundReversalId, log, message);
    log.error({ err }, 'Failed to send money back to supplier wallet');

    await originalTransaction.reload();
    const currentEventModel = originalTransaction;
    await changeEvent(currentEventModel, previousEventModel);
    throw err;
  }
};

/**
 * @summary
 * Refunds a transaction
 * on Failure, sends a slack message and does not reschedule job because now support intervention is needed
 *
 * @param {TransactionInstance} refundTransaction
 * @param {TransactionInstance} originalTransaction
 * @param {Logger} log
 * @param {string} message
 */
// TODO ARCHITECTURE: Should be moved to an action?
export const refundTransaction = async (
  transaction: TransactionInstance,
  log: Logger,
  message: string,
  amount = transaction.amount
) => {
  const journal = new JournalTransaction(transaction.id);
  const previousEventModel = transaction;
  try {
    if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
      // Create pending journal entries between the seller's and buyer's wallet account
      await journal.credit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: transaction.buyer_id,
        amount,
      });
      await journal.debit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: transaction.seller_id,
        amount,
      });
      await journal.commit();
    }
    const state = new BigNumber(amount).lt(transaction.amount)
      ? Transaction.states.partial_refund
      : Transaction.states.refunded;

    const currentEventModel = await transaction.update({
      state,
      refunded_at: moment().toISOString(),
      declineReason: '',
    });

    await backends[transaction.backend].refund(transaction, message, amount);

    await journal.markEntriesSettled();
    changeEvent(currentEventModel, previousEventModel);
  } catch (err: any) {
    await journal.markEntriesArchived();
    const currentEventModel = await transaction.update({
      state: Transaction.states.refund_flagged,
      declineCode: (err as ProviderError).code,
      declineReason: truncateDeclineReason((err as ProviderError).message),
    });

    const msg = `
      <!here> Could not refund money to buyer, ${transaction.id} failed.
      Details - https://dashboard.assemblypay.com/payments/items/${transaction.id}
    `;
    await sendFailureMessage(transaction.id, log, msg);
    log.error({ err }, 'Refund transaction to buyer failed');
    await changeEvent(currentEventModel, previousEventModel);
    throw err;
  }
};

/**
 * @summary
 * 1. Completed Refund Transaction should be reversed to the buyer
 * 2. On success -> Refund the original transaction
 * 3. On error -> send slack message & reschedule in 1 business day
 * 4. Update originalTransaction to refunded state
 * 5. send webhook for success on success
 *
 * @param {TransactionInstance} refundTransaction
 * @param {Logger} log
 * @param {string} message
 * @returns {Promise<boolean>}
 */
const initiateRefund = async (
  transaction: TransactionInstance,
  log: Logger,
  payload: JobData
): Promise<boolean> => {
  const { message } = payload;
  const originalTransaction = await Transaction.findByPk(
    transaction?.relatedTransactions?.[0]
  );
  if (!originalTransaction) {
    log.error(`Original transaction not found`);
    return false;
  }
  if (originalTransaction.state === Transaction.states.refunded) {
    log.error('Original transaction already refunded', originalTransaction.id);
    return false;
  }

  // TODO ARCHITECTURE: This needs to use Steveo Workflows to better manage stateful retries
  try {
    await sendMoneyToBuyerWallet(transaction, originalTransaction, log);
  } catch (err) {
    await rescheduleJob(payload);
    return false;
  }
  try {
    await refundTransaction(
      originalTransaction,
      log,
      message,
      transaction.amount
    );
  } catch (err) {
    return false;
  }
  await Job.create({
    name: 'notification',

    nextRunAt: new Date().toISOString(),
    data: {
      transactionId: originalTransaction.id,
    },
  });
  return true;
};

type JobData = {
  transactionId: string;
  partial: boolean;
  message: string;
  userId: string;
  invoices: {
    [id: string]: string | number;
  };
};

const markJournalEntriesSettled = async (
  transactionId: string,
  log: Logger
) => {
  const journal = new JournalTransaction(transactionId);
  if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
    log.error('Settling journal entries');
    await journal.markEntriesSettled();
  }
};

const markJournalEntriesArchived = async (
  transactionId: string,
  log: Logger
) => {
  const journal = new JournalTransaction(transactionId);
  if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
    log.error('Archiving journal entries');
    await journal.markEntriesArchived();
  }
};

/**
 * This is the main entrypoint for the Task execution
 * @param data
 * @returns
 */
const refundCheckHandler = async (data: JobData) => {
  const { transactionId } = data;
  const log = logger.child({ transactionId, task: 'refund-check' });

  if (!transactionId) {
    log.error(`transactionId required`);
    return false;
  }

  // Faux locking mechanism to handle duplicate refunds
  // Rate limits to 10 minutes
  const rateLimited = await cache.rateLimit(
    `refund-check-task:${transactionId}`,
    1,
    60 * 10
  );
  if (rateLimited) {
    log.info('Refund task rate limited');
    return false;
  }

  const transaction = await Transaction.findByPk(transactionId);

  if (!transaction) {
    await markJournalEntriesArchived(transactionId, log);
    log.error(`failed to load transaction ${transactionId}`);
    return false;
  }
  if (!transaction?.relatedTransactions?.length) {
    log.error(
      `Original transaction not linked with this refund transaction ${transactionId}`
    );
    await markJournalEntriesArchived(transactionId, log);
    return false;
  }

  log.info(`checking item for ${transaction.id}`);

  let res;

  try {
    res = await client.items.showItem(transaction.id);
  } catch (err) {
    log.error({ err });
    await markJournalEntriesArchived(transactionId, log);
    sendFailureMessage(transactionId, log);
    return false;
  }

  if (!res || !res.items) {
    // TODO JOURNAL_V3: What does this mean in terms of tx resolution?

    log.info(`failed to load item ${transaction.id}`);
    await markJournalEntriesArchived(transactionId, log);
    sendFailureMessage(transactionId, log);
    return false;
  }

  log.info(`item found`, res);

  if (
    [
      Transaction.states.payment_deposited,
      Transaction.states.completed,
    ].indexOf(res.items.state) === -1
  ) {
    log.error('Refund transaction delayed');

    // TODO JOURNAL_V3: Add call to update journal tx

    const msg = `
      <!here> Refund transaction ${transactionId} delayed.
      (Transaction Created > 3 days) - https://dashboard.assemblypay.com/payments/items/${transactionId}
    `;
    await sendFailureMessage(transactionId, log, msg);
    await rescheduleJob(data);
    log.info(`Will check status again in 1 business day`);
    return true;
  }
  if (res.items.state === Transaction.states.payment_deposited) {
    try {
      log.info(`Releasing funds to OM wallet ${OM_REFUND_USER_ID}`);
      const payment = await client.items.releasePayment(transaction.id, {
        release_amount: transaction.amount,
      });
      await markJournalEntriesSettled(transactionId, log);

      // TODO JOURNAL_V3: Add call to update journal tx

      log.info('Completed release payment', payment);
    } catch (err) {
      log.error({ err }, 'Could not release payment');

      // TODO JOURNAL_V3: Add call update journal tx

      const msg = `
      <!here> Refund transaction ${transactionId} release funds failed.
      (Funds release failed) - https://dashboard.assemblypay.com/payments/items/${transactionId}
      `;
      await sendFailureMessage(transactionId, log, msg);
      await rescheduleJob(data);
      log.info(`Will try to release funds again in 1 business day`);
      return true;
    }
  }
  log.info('Initiating refund');
  const isSuccess = await initiateRefund(transaction, log, data);
  log.info(`Refund ${isSuccess ? 'successfull' : 'failed'}`);
  return isSuccess;
};

/**
 * @summary
 * see https://ordermentum.atlassian.net/wiki/spaces/OD/pages/1073152096/Partial+Refunds+Support for full flow
 * 1. Check transaction if completed -> execute refund to the retailer, add to ledger, send webhook to OM
 * 2. If transaction is payment_deposited -> we release funds first and then continue with 1
 * 3. If not completed or payment_deposited -> send slack message and try in 1 business day.
 *
 * @param {JobData} payload
 */
export const refundCheckTask = steveo.task<JobData>(
  'refund-check',
  refundCheckHandler
);
