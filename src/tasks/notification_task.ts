import { JobContext } from '@ordermentum/scheduler';
import { Transaction, Job } from '../models';
import steveo from '../lib/steveo/steveo';
import { logger } from '../lib/logger';
import { send } from '../lib/slack';

const MAX_RETRIES = 5;
type Message = { transactionId: string; trigger?: string; retries?: number };

/**
 * The main entrypoint for the task handler
 */
export const notificationHandler = async (
  payload: Message,
  context?: JobContext
) => {
  const { transactionId, retries = 0 } = payload;
  const job = context?.job;
  const log = logger.child({
    action: 'notification',
    retries,
    jobId: job?.id,
    transactionId,
  });

  const source = payload?.trigger ?? 'unknown source';

  log.info(`starting notification task for ${transactionId} (${source})`);

  if (retries >= MAX_RETRIES) {
    const msg = `<!here> Notification retries reached maximum limit. Job Id - ${job?.id}`;
    await send(msg).catch(err =>
      log.error('Could not send message to slack channel', err)
    );
    log.info(`Max retries reached`);
    return false;
  }

  const transaction = await Transaction.findByPk(transactionId);

  if (!transaction) {
    log.error(`transaction not found ${transactionId}`);
    return false;
  }

  try {
    await steveo.publish('transaction-changed', {
      transactionId,
    });

    if (transaction.webhook) {
      await Job.create({
        name: 'webhook',
        nextRunAt: new Date().toISOString(),
        data: {
          uri: transaction.webhook,
          transactionId,
          source,
          body: transaction.toJSON(),
        },
      });
    }
  } catch (e) {
    log.error(e);
    await Job.create({
      name: 'notification',
      nextRunAt: new Date().toISOString(),
      data: {
        ...payload,
        retries: retries + 1,
      },
    });
  }

  log.info(`successfully triggered notification`);
  return true;
};
export const notificationTask = steveo.task(
  'notification',
  notificationHandler
);

export default notificationTask;
