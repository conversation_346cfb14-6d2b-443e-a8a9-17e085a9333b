import { randomUUID } from 'crypto';
import { Op } from 'sequelize';
import config from 'config';
import logger from '../lib/logger';
import { Transaction, User } from '../models';
import {
  Backend,
  states,
  TransactionInstance,
  Workflow,
} from '../models/transaction';
import { CurrencyCodes } from '../types/currency_codes';
import { createPaymentTransaction } from '../actions/create_payment_transaction';
import backends from '../lib/backends';
import { userWalletAccount } from '../lib/backends/promisepay';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import steveo from '../lib/steveo/steveo';
import { CreateTransactionAttrs } from '../types/payment_types';
import * as stats from '../lib/stats';

const OM_WALLET_USER_ID = config.get<string>('OM_WALLET_USER_ID');

// TODO JOURNAL_V3: This can be moved to an action
// NOTE! "reverse" here means finishing a payment, in a two-step process (from OM wallet into the buyer's wallet)
export const reverseTransaction = (
  transaction: TransactionInstance,
  log = logger
) => {
  log.info(
    `creating funding transaction for ${
      transaction.id
    } for orders ${transaction.orderIds.join(',')}`
  );
  const walletTransactionId = randomUUID();
  const amountCents = transaction.amount;

  // transfer from the wallet user to the buyer
  const walletTransaction: CreateTransactionAttrs = {
    amount: amountCents,
    id: walletTransactionId,
    buyer_id: transaction.seller_id,
    seller_id: transaction.buyer_id,
    card_payment_method_id: null,
    bank_payment_method_id: null,
    useWalletAccount: true,
    currency: CurrencyCodes.AUD,
    description: `Funding ${transaction.description}`,
    state: states.pending,
    backend: transaction?.context?.backend ?? transaction.backend,
    name: `Reverse ${transaction.description}`,
    // the holding transaction is not related to any orders or invoices, logic on the OM side would result in this being listened to on changes
    orderIds: [],
    invoiceIds: [],
    relatedTransactions: [transaction.id],
    type: 'wallet' as const,
    tryAsync: false, // Wallet transactions are currently synchronous
    context: {
      invoiceIds: transaction.invoiceIds,
      workflow: Workflow.HoldFundsInWallet,
      orderIds: transaction.orderIds,
    },
  };

  log.debug(
    `Moving ${amountCents} cents to user ${transaction.buyer_id} wallet account, id: ${walletTransactionId}`
  );

  return createPaymentTransaction(walletTransaction, log);
};

/**
 * The main entrypoint for the Task execution
 * Reverts money held for a user in a wallet account to their wallet account
 * Note: Only supports promisepay as a backend
 */
export const reverseHeldFundsHandler = async (data: {
  holdingTransactionId: string;
}) => {
  logger.debug('Reverting held funds', { data });
  const { holdingTransactionId } = data;

  if (!holdingTransactionId) {
    logger.warn('Reverting held funds: Missing required fields', {
      holdingTransactionId,
      data,
    });
    return false;
  }

  const log = logger.child({
    task: 'revert-held-funds',
    holdingTransactionId,
  });

  try {
    const holdingTransaction = await Transaction.findByPk(
      holdingTransactionId,
      {
        include: [
          {
            model: User,
            as: 'seller',
            required: true,
          },
        ],
      }
    );

    if (
      !holdingTransaction ||
      // TODO SMELL: simplify to a named variable
      !['completed', 'settled'].includes(holdingTransaction.state)
    ) {
      log.warn('Transaction not found or not in a valid state');
      return false;
    }

    const existingWalletTransaction = await Transaction.findOne({
      where: {
        buyer_id: holdingTransaction.seller_id,
        seller_id: holdingTransaction.buyer_id,
        state: states.pending,
        backend: Backend.PROMISEPAY,
        relatedTransactions: {
          [Op.contains]: [holdingTransactionId],
        },
        type: 'wallet',
      },
    });

    if (existingWalletTransaction) {
      log.fields.walletTransactionId = existingWalletTransaction.id;
      log.debug('Found existing transaction, making payment');
      // We normally have external ids on users that are provider ids
      // but in this case, since this is a system user, the ids are the same
      const walletAccount = await userWalletAccount(OM_WALLET_USER_ID);

      if (!walletAccount?.id) {
        throw new Error('Missing wallet account');
      }

      // TODO ARCHITECTURE: Payment gateways need abstracting and only an interface reference to be used in places like this
      // note; even for finstro payments, the wallet is held in promisepay so use promisepay backend
      const payment = await backends[
        Backend.PROMISEPAY
      ].client.items.makePayment(existingWalletTransaction.id, {
        account_id: walletAccount.id,
        merchant_phone: holdingTransaction.seller?.mobile_number,
      });

      if (payment.items?.state !== states.completed) {
        throw new Error('Failed to make payment');
      }

      // TODO JOURNAL_V3: Add journal transaction update

      const journal = new JournalTransaction(existingWalletTransaction.id);
      await Promise.all([
        existingWalletTransaction.update({
          state: states.completed,
        }),
        journal.markEntriesSettled(),
      ]);

      log.info(
        { reverseTransactionId: existingWalletTransaction.id },
        'Existing reverse transaction was found unpaid, payment made'
      );

      stats.increment('reverse_held_funds_success', 1, {
        holdingTransactionId: holdingTransaction.id,
        reverseTransactionId: existingWalletTransaction.id,
      });
    } else {
      // TODO JOURNAL_V3: Add journaled executor operator wrapper

      log.debug('Creating wallet transaction');
      const result = await reverseTransaction(holdingTransaction, log);
      log.info(
        { reverseTransactionId: result.transaction.id },
        'Reverse transaction completed'
      );

      stats.increment('reverse_held_funds_success', 1, {
        holdingTransactionId: holdingTransaction.id,
        reverseTransactionId: result.transaction.id,
      });
    }

    return true;
  } catch (e) {
    log.error(e);
    throw e;
  }
};

export const reverseHeldFundsTask = steveo.task(
  'reverse-held-funds',
  reverseHeldFundsHandler
);
