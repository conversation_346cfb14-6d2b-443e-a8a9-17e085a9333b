/* eslint-disable no-await-in-loop */
import { SingleItem } from 'zai-payments';
import steveo from '../lib/steveo/steveo';
import { Transaction } from '../models';
import { logger } from '../lib/logger';
import { client } from '../lib/backends/promisepay';
import { handleItemChange } from '../lib/webhooks/promisepay';
import { Backend } from '../models/transaction';

/**
 * The main entrypoint for the task handler
 */
// TODO ARCHITECTURE: Move this to a temporal workflow as part of making payment
const transactionCheckHandler = async (data: { transactionId: string }) => {
  const { transactionId } = data;
  const log = logger.child({ transactionId, task: 'transaction-check' });

  if (!transactionId) {
    log.error(`transactionId required. returning`);
    return false;
  }
  const transaction = await Transaction.findByPk(transactionId);

  if (!transaction) {
    log.warn(`failed to load transaction ${transactionId}`);
    return false;
  }

  if (transaction.backend !== Backend.PROMISEPAY) {
    log.error(`transaction backend is not promisepay`);
    return false;
  }

  log.debug(`checking item for ${transaction.id}`);

  let res: SingleItem | null;

  try {
    res = await client.items.showItem(transaction.id);
  } catch (e) {
    return false;
  }

  if (!res) {
    log.info('failed to load item');
    return false;
  }

  const item = res.items;

  if (!item) {
    log.error(`item not found`);
    return false;
  }

  if (item.state === transaction.state) {
    log.debug('state matches between transaction and item');
    return false;
  }

  log.info('state drift between transaction and item');
  await handleItemChange(item);

  return true;
};

export const transactionCheckTask = steveo.task(
  'transaction-check',
  transactionCheckHandler
);

export default transactionCheckTask;
