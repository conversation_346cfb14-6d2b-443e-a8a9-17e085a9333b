/* eslint-disable no-param-reassign */
import { randomUUID } from 'crypto';
import HttpError from 'httperrors';
import moment from 'moment';
import { Job, Transaction, sequelize } from '../models';
import Logger from '../lib/logger';
import { JournalError, JournalTransaction } from '../lib/journal_v2/journal_v2';
import { JournalName } from '../models/journal_v2';
import { CurrencyCodes } from '../types/currency_codes';
import { getBackend } from '../lib/backends';
import { systemConfig } from '../system_config';
import { ProviderError } from '../lib/backends/error';
import { changeEvent } from '../helpers/transaction_change_event';
import { count } from '../lib/stats';
import {
  Backend,
  isTransactionAsync,
  TransactionInstance,
} from '../models/transaction';
import {
  CreateTransactionAttrs,
  CreateTransactionResult,
} from '../types/payment_types';
/**
 * <PERSON>reate records to track the payment gateway request in our
 * database.
 */
async function recordDbRecords(
  payload: CreateTransactionAttrs,
  paymentTransactionId: string,
  journal: JournalTransaction,
  log: typeof Logger
) {
  const txn = await sequelize.transaction();

  try {
    const paymentTransaction = await Transaction.create(
      {
        ...payload,
        id: paymentTransactionId,
        context: {
          ...payload.context,
          isZaiAsync: isTransactionAsync(payload),
        },
      },
      {
        transaction: txn,
      }
    );

    if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
      // Create pending ledger entries between the seller's and buyer's wallet account
      await journal.credit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: payload.seller_id,
        amount: payload.amount,
      });
      await journal.debit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: payload.buyer_id,
        amount: payload.amount,
      });
      await journal.commit(txn);
    }

    if (
      payload.backend === Backend.PROMISEPAY &&
      Boolean(payload.card_payment_method_id)
    ) {
      log.info('Scheduling transaction check');
      await Job.create(
        {
          name: 'transaction-check',
          data: { transactionId: paymentTransaction.id },
          nextRunAt: moment().add(2, 'minute').toISOString(),
        },
        {
          transaction: txn,
        }
      );
    }

    await txn.commit();

    log.info(`paymentTransaction committed with id ${paymentTransactionId}`, {
      payload, // the payload contains more info than actually gets written to DB, so useful to log
    });

    return paymentTransaction;
  } catch (err) {
    log.error(err, 'Failed to create a transaction record in database');
    await txn.rollback();
    throw HttpError({
      statusCode: err instanceof JournalError ? 422 : 500,
      data: {
        message: 'Failed to process payment',
      },
    });
  }
}

/**
 * Calls the actual backend implementation to move the funds.
 */
async function makeBackendPayment(
  payload: CreateTransactionAttrs,
  paymentTransactionId: string,
  paymentTransaction: TransactionInstance,
  journal: JournalTransaction,
  backend: Backend,
  log: typeof Logger
): Promise<CreateTransactionResult> {
  if (
    systemConfig.SKIP_BACKENDS ||
    paymentTransaction.transactionType === 'accounting'
  ) {
    log.info('Skipping backend create transaction');

    return {
      fundedCents: 0,
      fundedTxId: undefined,
    };
  }

  try {
    return await getBackend(backend).createTransaction(
      {
        ...payload,
        id: paymentTransactionId,
      },
      log
    );
  } catch (e) {
    const message =
      (e as ProviderError)?.message ?? 'Failed to process payment';
    if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
      log.debug('Archiving journal entries');
      await journal.markEntriesArchived();
    }
    count('payments_failed_transaction');
    log.error(e, message, payload);
    const statusCode = (e as ProviderError)?.statusCode ?? 422;
    await paymentTransaction.reload();
    await changeEvent(paymentTransaction, { ...paymentTransaction });
    throw HttpError({
      statusCode,
      data: {
        message,
      },
    });
  }
}

/**
 * TODO: Add comment
 */
async function journalV2SettleTransaction(
  payload: CreateTransactionAttrs,
  paymentTransaction: TransactionInstance,
  journal: JournalTransaction,
  backend: Backend,
  log: typeof Logger
) {
  try {
    await paymentTransaction.reload();

    /**
     * If this transaction is an instant transfer, we settle the journal entries associated with it
     * An instant transfer transaction is a transaction that only uses the wallet accounts with our payment provider Zai
     */
    const isPendingAsync =
      payload.tryAsync &&
      backend === Backend.PROMISEPAY &&
      paymentTransaction.state === Transaction.states.pending;

    if (
      systemConfig.DOUBLE_ENTRY_JOURNAL &&
      !isPendingAsync &&
      (!payload.bank_payment_method_id ||
        paymentTransaction.transactionType === 'accounting')
    ) {
      await journal.markEntriesSettled();
    }
  } catch (err) {
    count('payments_failed_journal');
    log.error(err, 'Failed to load updated payment transaction record');
    // This is still a valid state because we have charged with our payment provider
  } finally {
    count('payments_transaction_created_count');
    await changeEvent(paymentTransaction, { ...paymentTransaction });
  }

  return paymentTransaction;
}

/**
 * 1. Create a transaction {Transaction model} record and create corresponding credit and debit entries [Wrapped in a database transaction]
 * 2. Create a transaction in the payment provider, if it fails, archive ledger entries
 * 3. Reload the transaction created in step 1 to have updated data and return, if it fails to reload, return still cause we charged the customer successfully
 */
export async function createPaymentTransaction(
  payload: CreateTransactionAttrs,
  logger = Logger
): Promise<
  CreateTransactionResult & {
    transaction: TransactionInstance;
  }
> {
  const backend = payload.backend || Backend.PROMISEPAY;
  const paymentTransactionId = payload.id ?? randomUUID();
  const log = logger.child({
    action: 'createTransaction',
    transactionId: paymentTransactionId,
    backend,
    doubleEntryJournal: systemConfig.DOUBLE_ENTRY_JOURNAL,
    skipBackends: systemConfig.SKIP_BACKENDS,
  });

  // TODO ARCHITECTURE: This is a prime candidate for temporal workflows!
  const journal = new JournalTransaction(paymentTransactionId);

  const paymentTransaction = await recordDbRecords(
    payload,
    paymentTransactionId,
    journal,
    log
  );

  const makePaymentResult = await makeBackendPayment(
    payload,
    paymentTransactionId,
    paymentTransaction,
    journal,
    backend,
    log
  );

  await journalV2SettleTransaction(
    payload,
    paymentTransaction,
    journal,
    backend,
    log
  );

  return {
    ...makePaymentResult,
    transaction: paymentTransaction,
  };
}
