import { randomUUID } from 'crypto';
import httperrors from 'httperrors';
import BigNumber from 'bignumber.js';
import config from 'config';
import { HoldFunds } from '../controllers/users';
import Logger from '../lib/logger';
import { createPaymentTransaction } from './create_payment_transaction';
import { CurrencyCodes } from '../types/currency_codes';
import {
  Backend,
  states,
  TransactionContext,
  Workflow,
} from '../models/transaction';
import { CardPaymentMethod, PaymentMethod, User } from '../models';
import { validatePaymentMethod } from '../controllers/transactions';
import { PaymentMethodInstance } from '../models/payment_methods';
import { postHoldFundsInWalletAction } from './post_hold_funds_in_wallet_action';
import { systemConfig } from '../system_config';
import { CreateTransactionAttrs } from '../types/payment_types';
import * as stats from '../lib/stats';

const OM_WALLET_USER_ID = config.get<string>('OM_WALLET_USER_ID');
const OM_FINSTRO_USER_ID = config.get<string>('OM_FINSTRO_USER_ID');

// TODO ARCHITECTURE: This should be in the wallet abstraction library to more clearly delineate the boundaries of responsibility
/*
 * This action stores funds in the wallet account of the user
 * - Money is moved into a wallet user's account (OM holding user)
 * - Job is created to move money back to the user's wallet account
 *
 * Note: Only supports PromisePay as a backend
 * Promisepay does not support funding a user's wallet by their own payment method
 * hence the need to move money into a wallet user's account and then back to the user's wallet account
 * Note: Promisepay has been renamed to Zai but the codebase doesn't reflect that yet
 */
export const holdFundsInWalletAction = async (
  {
    amount,
    description,
    userId,
    paymentMethodId,
    immediate = false,
    orderIds = [],
    invoiceIds = [],
  }: HoldFunds,
  tryAsync: boolean,
  logger = Logger
) => {
  const user = await User.findByPk(userId);

  if (!user) throw httperrors.BadRequest('User not found');

  // note: currently finstro users will still have users.backend="promisepay", their PaymentMethod (payment_methods) record will have the different backend/type
  if (user.backend !== Backend.PROMISEPAY)
    throw httperrors.BadRequest(`User's payment provider is not supported`);

  const amountCents = new BigNumber(amount)
    .multipliedBy(100)
    .decimalPlaces(0)
    .toNumber();

  const holdingTransactionId: string = randomUUID();

  const log = logger.child({
    action: 'holdFundsInWallet',
    userId,
    amount: amountCents,
    description,
    holdingTransactionId,
    paymentMethodId,
    tryAsync,
    immediate,
  });

  // first check card_payment_methods matching this id + user id
  const cardPaymentMethod = await CardPaymentMethod.findOne({
    where: {
      id: paymentMethodId,
      user_id: user.id,
    },
  });

  // if no card found, try the payment_methods table
  let paymentMethod: PaymentMethodInstance | null = null;
  if (!cardPaymentMethod && systemConfig.FINSTRO_UPFRONT_ENABLED) {
    paymentMethod = await PaymentMethod.findOne({
      where: {
        id: paymentMethodId,
        accountId: user.id,
      },
    });
  }

  if (!cardPaymentMethod && !paymentMethod) {
    log.warn(
      `hold funds no matching payment method found for userId=${userId}, paymentMethodId=${paymentMethodId}`
    );
    throw httperrors.BadRequest('Payment method not found');
  }

  const paymentMethodType = cardPaymentMethod ? 'card' : paymentMethod?.type;
  log.info(`hold funds using paymentMethodType=${paymentMethodType}`);

  const context: TransactionContext = {
    isZaiAsync: tryAsync,
    workflow: Workflow.HoldFundsInWallet,
    immediate,
    backend: user.backend,
  };

  // credit card -> holding wallet user
  // holding wallet user -> user wallet
  // OR
  // finstro -> finstro wallet user
  // finstro wallet user -> user wallet
  const sellerId =
    paymentMethod?.backend === 'finstro'
      ? OM_FINSTRO_USER_ID
      : OM_WALLET_USER_ID;

  const holdingTransaction: CreateTransactionAttrs = {
    amount: amountCents,
    id: holdingTransactionId,
    buyer_id: userId,
    seller_id: sellerId,
    card_payment_method_id: cardPaymentMethod?.id, // null if not using card_payment_methods record
    paymentMethodId: paymentMethod?.id, // null if not using payment_methods record
    currency: CurrencyCodes.AUD,
    description,
    state: states.pending,
    backend: paymentMethod?.backend ?? user.backend,
    name: description,
    transactionType: 'escrow' as const,
    orderIds,
    invoiceIds,
    context,
  };

  try {
    const { type } = await validatePaymentMethod(holdingTransaction);
    log.info(
      {
        sellerId,
        buyerId: userId,
      },
      `holding funds using payment method ${type} for ${amountCents} cents`
    );

    // TODO JOURNAL_V3: No action required as the calling code is responsible for maintaining its journal record

    const result = await createPaymentTransaction(
      {
        ...holdingTransaction,
        tryAsync,
        type,
      },
      log
    );

    stats.increment('hold_funds_in_wallet_success', 1, {
      holdingTransactionId,
    });

    if (!tryAsync) {
      await postHoldFundsInWalletAction(result.transaction, log);
    }
  } catch (err: any) {
    log.error(err, `Failed to charge payment method ${amountCents} cents`);
    if (err instanceof httperrors) throw err;
    throw httperrors.InternalServerError(err.message);
  }

  return {
    fundedAmountCents: amountCents,
    holdingTransactionId,
  };
};
