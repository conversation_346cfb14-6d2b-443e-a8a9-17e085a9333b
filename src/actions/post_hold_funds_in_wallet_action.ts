import Logger from 'bunyan';
import { TransactionInstance } from '../models/transaction';
import {
  reverseHeldFundsTask,
  reverseTransaction,
} from '../tasks/reverse_held_funds_task';
import * as stats from '../lib/stats';
import { journalGatewayTx } from '../lib/journal_v3/journal_executor';

/**
 * Post hold funds implementation
 */
export async function postHoldFundsInWalletAction(
  transaction: TransactionInstance,
  log: Logger
) {
  log.info(
    { transaction: transaction.id },
    `postHoldFundsInWalletAction for transaction ${transaction.id}`
  );

  const immediate = transaction.context?.immediate;

  // TODO SMELL: Is there a more reliable place to detect this further upstream?
  if (immediate === undefined || immediate === null) {
    stats.count('payments.upfront-legacy.missing_immediate');

    log.warn('Missing argument "immediate" for postHoldFundsInWalletAction');
  }

  if (!immediate) {
    log.debug(
      { transactionId: transaction.id },
      'Queuing reverse-held-funds job'
    );

    await reverseHeldFundsTask.publish({
      holdingTransactionId: transaction.id,
    });
    return;
  }

  const result = await reverseTransaction(transaction, log);

  stats.increment('reverse_held_funds_success', 1, {
    holdingTransactionId: transaction.id,
    reverseTransactionId: result.transaction.id,
  });
}

/**
 * Webhook callback handler entrypoint
 */
export async function holdFundsInWalletCallbackHandler(
  transaction: TransactionInstance,
  log: Logger
): Promise<void> {
  if (transaction.state === 'completed') {
    await journalGatewayTx(transaction.id, undefined, undefined, log);

    await postHoldFundsInWalletAction(transaction, log);
  } else if (transaction.state === 'failed') {
    await journalGatewayTx(
      transaction.id,
      transaction.declineReason ?? 'Transaction failed at gateway',
      undefined,
      log
    );

    log.error(`Failed to charge payment method ${transaction.amount} cents`);
  }
}
