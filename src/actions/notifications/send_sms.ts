import Logger from 'bunyan';
import { AxiosError } from 'axios';
import { notificationsClient } from '../../lib/notifications_client';
import logger from '../../lib/logger';
import { MessageTemplateRequest } from '../../types/notification';

export const sendTemplateMessage = async ({
  payload,
  log = logger,
}: {
  payload: MessageTemplateRequest;
  log?: Logger;
}): Promise<void> => {
  try {
    await notificationsClient.post('/v1/messages/template', payload);
  } catch (err) {
    const axiosError = err as AxiosError;
    const statusCode = axiosError.response?.status;
    const errorMsg = axiosError.response?.data;
    log.error({ err, errorMsg, statusCode }, 'Failed to send SMS message');
  }
};
