/* eslint-disable no-param-reassign */
import BigNumber from 'bignumber.js';
import { NotFound } from 'httperrors';
import Logger from 'bunyan';
import { randomUUID } from 'crypto';
import { UserInstance } from '../models/user';
import {
  PaymentType,
  TransactionAttributes,
  TransactionInstance,
} from '../models/transaction';
import { User, UserSetting } from '../models';
import { calculateRelease } from '../lib/calculations';
import logger from '../lib/logger';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { JournalName } from '../models/journal_v2';
import { CurrencyCodes } from '../types/currency_codes';
import { JournalEntryStatus } from '../models/journal_v2_entry';
import { createPaymentTransaction } from './create_payment_transaction';
import { UserSaleOutput } from '../types/journal_operation_types';

export const journalV2Entries = async ({
  accountId,
  amount,
  transactionId,
  scheduledPayment,
  log = logger,
}: {
  accountId: string;
  amount: number;
  transactionId: string;
  scheduledPayment: boolean;
  log?: Logger;
}) => {
  try {
    const journal = new JournalTransaction(transactionId);
    await journal.credit({
      name: JournalName.FUNDS,
      currency: CurrencyCodes.AUD,
      accountId,
      amount,
      status: scheduledPayment
        ? JournalEntryStatus.PENDING
        : JournalEntryStatus.SETTLED,
    });
    await journal.debit({
      name: JournalName.LIABILITIES,
      currency: CurrencyCodes.AUD,
      amount,
      status: scheduledPayment
        ? JournalEntryStatus.PENDING
        : JournalEntryStatus.SETTLED,
    });
    await journal.commit();
  } catch (e) {
    log.error(`Failed to increase funds for account ${accountId}`, e);
  }
};

export const calculateFee = (
  seller: UserInstance,
  amount: number,
  type?: PaymentType
): number => {
  const rates = seller?.configuration?.rates ?? {};
  const percentage: number = rates?.[type ?? 'unknown'] ?? 0;
  return new BigNumber(amount)
    .dividedBy(100)
    .times(percentage)
    .dp(2)
    .toNumber();
};

const addFees = (payload: TransactionAttributes, seller: UserInstance) => {
  payload.settlement_delay_hours = seller.dd_settlement_delay_hours;
  payload.fee = calculateFee(seller, payload.amount, payload.type);
};

/**
 * Based on the payment method, calculate the release timestamp
 * received at is the money receive time
 * release at is the time the money is released to the seller
 * Cards and banks processing are provided by Promisepay
 * card ->
 *  received_at = immediate
 *  release_at = immediate if before 1:30pm, otherwise next business day
 * bank ->
 *  received_at = provider dependent, usually 2-3 business days (not set here)
 *  release_at = 72 hours after now
 * finstro/trade account ->
 *    we don't settle, as we use holding wallets to manage the funds
 *
 */
export const addPaymentReleaseTimestamps = (
  seller: UserInstance,
  payload: TransactionAttributes,
  releaseDelay = 72
) => {
  if (payload.card_payment_method_id) {
    payload.received_at = new Date().toISOString();
    payload.release_at = calculateRelease(seller, payload.received_at, 0, true);
  } else if (payload.bank_payment_method_id) {
    payload.release_at = calculateRelease(
      seller,
      new Date().toISOString(),
      releaseDelay,
      false
    );
  }
};

export type PaymentOptions = {
  /**
   * If true, the transaction will be funded early by the business to the
   * seller and recouped later from the buyer
   */
  fundedByPrinciple?: boolean;

  /**
   * If true, the buyer's wallet will be used to pay for the transaction.
   * If there are insufficient funds, the payment method will be used to pay for the shortfall
   */
  useBuyerWallet?: boolean;

  /**
   * Attempt to use the async payment workflow
   */
  tryAsync: boolean;
};

/**
 * This is the entrypoint when creating a payment.
 */
export async function transactionCreateAction(
  payload: TransactionAttributes,
  options: PaymentOptions,
  log: Logger = logger
): Promise<{
  transaction: TransactionInstance;
  details: UserSaleOutput;
}> {
  const {
    useBuyerWallet = false,
    fundedByPrinciple = false,
    tryAsync,
  } = options;
  const requestSellerId = payload.seller_id;

  const seller = await User.findByPk(payload.seller_id, {
    include: [{ model: UserSetting, as: 'userSetting' }],
  });
  const buyer = await User.findByPk(payload.buyer_id);

  if (!seller) {
    throw NotFound(`Unknown seller ${payload.seller_id}`);
  }

  if (!buyer) {
    throw NotFound(`Unknown buyer ${payload.buyer_id}`);
  }

  addFees(payload, seller);

  addPaymentReleaseTimestamps(seller, payload);

  payload.settlement_delay_hours = seller.dd_settlement_delay_hours;
  payload.fee = calculateFee(seller, payload.amount, payload.type);

  const paymentResult = await createPaymentTransaction(
    {
      ...payload,
      useWalletAccount: useBuyerWallet,
      replenishBuyerWallet: useBuyerWallet,
      requestSellerId,
      id: payload.id ?? randomUUID(),
      tryAsync,
    },
    log
  );

  // If this is a funded transaction
  // Money moves back from liability to the original seller's fund journal
  if (fundedByPrinciple) {
    await journalV2Entries({
      accountId: requestSellerId,
      transactionId: paymentResult.transaction.id,
      amount: payload.amount,
      scheduledPayment: Boolean(payload.bank_payment_method_id),
      log,
    });
  }

  const invoices = payload.invoiceIds?.join(', ');

  return {
    transaction: paymentResult.transaction,
    details: {
      sellerId: seller.id,
      buyerId: payload.buyer_id,
      totalCents: payload.amount,
      feeCents: new BigNumber(payload.fee ?? 0).times(100).toNumber(),
      fundedByPrinciple,
      walletFundedCents: paymentResult.fundedCents,
      walletFundTxId: paymentResult.fundedTxId,
      sellerText: `${payload.buyer_id} invoices: ${invoices}`,
      buyerText: `${seller.company_legal_name} invoices: ${invoices}`,
      purchaseTxId: tryAsync ? paymentResult.transaction.id : undefined,
      purchaseTxState: paymentResult.transaction.state,
    },
  };
}
