import HttpError from 'httperrors';
import { Transaction } from 'sequelize';
import { User, UserSetting } from '../../models';
import { UserConfiguration } from '../../models/user';
import { UserSettingAttributes } from '../../models/user_setting';

export const updateUserSettings = async (
  userId: string,
  configurations: UserConfiguration,
  txn?: Transaction
) => {
  const user = await User.findByPk(userId, { transaction: txn });

  if (!user) {
    throw HttpError({
      statusCode: 404,
      data: {
        message: 'User not found',
      },
    });
  }

  // Start with required userId
  const settings: UserSettingAttributes = {
    userId,
    settlementDelayDays: configurations.settlementDelay,
    manualDisbursement: configurations.manualDisbursement,
    isSupplier: configurations.supplier,
    netSettlement: configurations.netSettlement,
    reference: configurations.reference,
    settlementRate: configurations.settlementRate?.toString(),
    weeklySettlement: configurations.weeklySettlement,
    companyDescriptor: configurations.companyDescriptor,
  };

  const [userSetting, created] = await UserSetting.findOrCreate({
    where: { userId },
    defaults: settings,
    transaction: txn,
  });
  // If the setting already existed, update it
  if (!created) {
    await userSetting.update(settings, {
      transaction: txn,
    });
  }
  return userSetting;
};
