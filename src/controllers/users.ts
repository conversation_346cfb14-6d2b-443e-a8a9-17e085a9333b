/* eslint-disable no-param-reassign */
import <PERSON><PERSON> from 'joi';
import httperrors from 'httperrors';
import { v4 as uuid } from 'uuid';
import { Transaction } from 'sequelize';
import Logger from 'bunyan';
import BigNumber from 'bignumber.js';
import { composeSubscription } from '../actions/compose_subscription';
import {
  sequelize,
  User,
  CardPaymentMethod,
  Subscription,
  SubscriptionInvoice,
} from '../models';
import backends from '../lib/backends';
import { logger } from '../lib/logger';
import validator from '../validations/user';
import { Backend } from '../models/transaction';
import { ComposedSubscriptionAttributes } from '../models/subscription';
import { getSpreedlyCard } from '../actions/spreedly/get_card';
import { subscriptionIncrement } from '../actions/increment_subscription';
import { getSubscriptionUsage } from '../actions/get_subscription_usage';
import { usageTypes } from '../models/subscription_usage';
import {
  addJobs,
  postUserDeleteAction,
  postUserUpsertAction,
} from '../actions/users/post_user_update_action';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { JournalName } from '../models/journal_v2';
import { CurrencyCodes } from '../types/currency_codes';
import { holdFundsInWalletAction } from '../actions/hold_funds_in_wallet_action';
import { revertHeldFundsAction } from '../actions/revert_held_funds_action';
import { getUserBalance } from '../actions/user_balance';
import { systemConfig } from '../system_config';
import { updateUserSettings } from '../actions/users/update_user_settings';
import { CreateUserRequest } from '../types/requests/user-requests.type';

const subscriptionIncrementValidator = Joi.object({
  timestamp: Joi.date().iso().required(),
  type: Joi.string().valid('invoice-sync').required(),
  metadata: Joi.object().optional(),
}).required();

export type HoldFunds = {
  amount: string;
  description: string;
  userId: string;
  paymentMethodId: string;
  orderIds?: string[];
  invoiceIds?: string[];
  currencyCode?: CurrencyCodes;
  immediate?: boolean;
  documentId?: string[];
};

const holdFundsSchema = Joi.object<HoldFunds>({
  amount: Joi.string()
    .required()
    .description('Dollars and cents')
    .custom(value => {
      if (!new BigNumber(value).isGreaterThanOrEqualTo(1))
        throw new Error('Amount must be greater than or equal to 1');
      return value;
    }),
  description: Joi.string().required(),
  userId: Joi.string().guid().required(),
  paymentMethodId: Joi.string().guid().required(),
  currencyCode: Joi.string().default(CurrencyCodes.AUD),
  orderIds: Joi.array().items(Joi.string().guid()).optional().single(),
  invoiceIds: Joi.array().items(Joi.string().guid()).optional().single(),
}).or('orderIds', 'invoiceIds');

const validUUID = Joi.string().guid().required();

export type RevertHeldFunds = {
  amount?: string;
  description: string;
  userId: string;
  documentId: string[];
};

const revertHeldFundsSchema = Joi.object<RevertHeldFunds>({
  amount: Joi.string()
    .optional()
    .description('Dollars and cents. By default, withdraws all available funds')
    .empty(['', null])
    .custom(value => {
      if (value === undefined) return;
      if (!new BigNumber(value).isGreaterThanOrEqualTo(1))
        throw new Error('Amount must be greater than or equal to 1');
    }),
  description: Joi.string().required(),
  documentId: Joi.array().items(Joi.string()).required().single(),
  userId: Joi.string().guid().required(),
});

const updateFundingSchema = Joi.object({
  userId: Joi.string().guid().required(),
  enabled: Joi.boolean().required(),
});

const { fromBody, toBody } = User;

interface SubscriptionIncrementPayload {
  timestamp: string;
  type: usageTypes;
  metadata: object;
}

export default {
  all: async query => {
    const results = await User.findAll(query);
    return results.map(toBody);
  },
  find: async userId => {
    const result = await User.findOne({
      where: {
        id: userId,
      },
    });
    if (!result) throw httperrors.NotFound();
    return toBody(result);
  },
  subscriptions: async (userId): Promise<ComposedSubscriptionAttributes[]> => {
    const user = await User.findOne({
      where: { id: userId },
      attributes: ['id', 'stripe_customer_id'],
      include: [
        {
          model: Subscription,
          as: 'subscriptions',
          include: [
            { model: SubscriptionInvoice, as: 'subscription_invoices' },
          ],
        },
      ],
      order: [
        [{ model: Subscription, as: 'subscriptions' }, 'status', 'asc'],
        [
          { model: Subscription, as: 'subscriptions' },
          { model: SubscriptionInvoice, as: 'subscription_invoices' },
          'created_at',
          'desc',
        ],
      ],
    });

    if (!user) throw httperrors.NotFound();
    const { subscriptions } = user;
    if (!subscriptions) throw httperrors.NotFound();

    const subscriptionsPromises = subscriptions.map(subscription =>
      composeSubscription(user.stripe_customer_id, subscription, Backend.STRIPE)
    );

    const result = await Promise.all(subscriptionsPromises);
    return result;
  },

  /**
   * Creates a new user.
   * Will create a new user in the database and then create a new user in the backend.
   * @param params - An object containing the details of the user to be created. This includes fields such as name, email, and other user attributes as defined in the CreateUserRequest type.
   * @returns A promise that resolves to the newly created user object, or rejects with an error if the creation process fails.
   */
  create: async (params: CreateUserRequest) => {
    const externalId = uuid();

    let values;
    try {
      values = await validator.validateAsync({
        ...params,
        external_id: externalId,
      });
    } catch (errors) {
      logger.error(
        `Error validating user ${errors} - ${JSON.stringify(params)}`
      );
      throw httperrors.BadRequest(errors);
    }

    let payload = fromBody({
      ...values,
      external_id: externalId,
    });
    let txn;
    try {
      txn = await sequelize.transaction();
      const created = await User.create(payload, { transaction: txn });
      if (!systemConfig.SKIP_BACKENDS) {
        payload = await backends[payload.backend].createUser({
          ...payload,
          id: payload.external_id,
        });

        // Create a customer counter part in stripe.
        // Note: We default to PROMISEPAY as backend. However, if we decide
        // to support creation of user with backend of STRIPE, this call will
        // duplicate the customer in stripe. So adding a guard just in case
        // we decided to add STRIPE in the future.
        if (payload.backend !== Backend.STRIPE) {
          const stripeResult = await backends[Backend.STRIPE].createUser({
            ...payload,
            id: payload.external_id,
          });

          if (stripeResult.stripe_customer_id) {
            payload.stripe_customer_id = stripeResult.stripe_customer_id;
          }
        }
      }
      await postUserUpsertAction(created.id, txn);
      await updateUserSettings(created.id, payload.configuration, txn);

      // TODO SMELL: Move data access to repository
      const [, [updated]] = await User.update(payload, {
        where: {
          id: created.id,
        },
        transaction: txn,
        returning: true,
      });
      await txn.commit();
      return User.toBody(updated);
    } catch (ex) {
      logger.error(
        `Error creating user for backend ${payload.backend}  - ${JSON.stringify(
          values
        )}`,
        JSON.stringify(ex)
      );
      if (txn) await txn.rollback();
      throw ex;
    }
  },
  incrementSubscriptionUsage: async (
    userId,
    payload: SubscriptionIncrementPayload
  ) => {
    if (!userId || !payload) throw httperrors.BadRequest();
    const { error, value } = subscriptionIncrementValidator.validate(payload);

    if (error) {
      throw httperrors.BadRequest(error.message);
    }

    const result = await subscriptionIncrement(userId, value);
    if (!result) throw httperrors.BadRequest();
    return result;
  },
  getSubscriptionUsage: async userId => {
    if (!userId) throw httperrors.BadRequest();
    const result = await getSubscriptionUsage(userId, Backend.STRIPE);
    if (!result) throw httperrors.BadRequest();
    return result;
  },
  update: async (userId, params) => {
    let txn: Transaction | null = null;
    try {
      txn = await sequelize.transaction();
      const instance = await User.findOne({
        where: {
          id: userId,
        },
        transaction: txn,
      });

      if (!instance) {
        throw httperrors.NotFound();
      }
      const stripeCustomerId = instance.stripe_customer_id;
      let values;
      try {
        values = await validator.validateAsync(params);
      } catch (err) {
        logger.error(
          `Error validating user ${err} - ${JSON.stringify(params)}`
        );
        throw httperrors.BadRequest(err);
      }
      const updatedProperties = values?.configuration ?? {};
      const configuration = {
        ...instance.configuration,
        ...updatedProperties,
      };
      let data = {
        ...instance.get(),
        ...fromBody(values),
        external_id: instance.getDataValue('external_id'),
        configuration,
      };
      const user = await instance.update(data, { transaction: txn });

      if (!systemConfig.SKIP_BACKENDS) {
        const backendUser = await backends[data.backend]
          .getUser(data.external_id)
          .catch(() => null);

        if (backendUser) {
          data = await backends[data.backend].updateUser(data);
        } else {
          const externalId = uuid();
          // If payment_method_id and payment_method_type is included
          // in the update payload, that means we already have created
          // the user via BankPaymentMethod or CardPaymentMethod.
          // Since, we only update this fields when supplier
          // updates their invoice payment account.
          if (!data.payment_method_id && !data.payment_method_type) {
            data = await backends[data.backend].createUser({
              ...data,
              id: externalId,
            });
            await user.update(
              {
                external_id: data.external_id,
              },
              {
                transaction: txn,
              }
            );
          }
        }

        if (params.invoice_settings && stripeCustomerId) {
          const cardPaymentMethod = await CardPaymentMethod.findByPk(
            params.invoice_settings.default_payment_method
          );

          if (cardPaymentMethod) {
            const card = await getSpreedlyCard(cardPaymentMethod.spreedly_id);

            const stripePaymenyMethodId =
              card.data.payment_method.metadata.paymentMethodId;

            if (stripePaymenyMethodId) {
              await backends[Backend.STRIPE].updateUserWithStripeObject(
                stripeCustomerId,
                {
                  invoice_settings: {
                    default_payment_method: stripePaymenyMethodId,
                  },
                }
              );
            }
          }
        }
      }

      await addJobs(user, txn);
      await postUserUpsertAction(userId, txn);
      await updateUserSettings(userId, configuration, txn);

      await txn.commit();
      return toBody(user);

      // # skipcq: JS-0323
    } catch (ex: any) {
      logger.error(ex, 'EXCEPTION UPDATING USER', ex?.response?.data);
      if (txn) {
        await txn.rollback().catch(e => {
          logger.error('Rollback failed', e);
        });
      }

      if (ex instanceof httperrors) {
        throw ex;
      }
      throw httperrors.BadRequest(ex);
    }
  },
  merge: async params => {
    const { archivedRetailerIds, electedRetailer } = params;
    let txn: Transaction | null = null;
    try {
      txn = await sequelize.transaction();

      const user = await User.findOne({
        where: {
          email: electedRetailer.email,
        },
        transaction: txn,
      });

      if (!user) {
        throw httperrors.NotFound();
      }

      let data;

      // TODO SMELL: A lot of domain logic in the http controller
      const newUser = uuid();
      if (!user) {
        logger.error('Elected retailer does not exist');

        data = {
          ...fromBody(electedRetailer),
          id: newUser,
          external_id: uuid(),
          backend: 'promisepay',
        };
        await User.create(data, { transaction: txn });
      } else {
        data = {
          ...user.get(),
          ...fromBody(electedRetailer),
          external_id: user.getDataValue('external_id'),
        };

        await User.update(data, {
          where: {
            id: user.id,
          },
          transaction: txn,
        });
      }

      if (!systemConfig.SKIP_BACKENDS) {
        const backendUser = await backends[data.backend]
          .getUser(data.external_id)
          .catch(ex => {
            // error message from AP for this is '{"errors":{"id":["invalid"]}}' with a status code 422!!!
            logger.error('User does not exist in AP', ex.body);
          });

        if (backendUser) {
          logger.info('Updating exisitng retailer');
          data = await backends[data.backend]
            .updateUser(data)
            .catch(ex => logger.error('User update failed with exception', ex));
        } else {
          logger.info('Creating new retailer');
          data = await backends[data.backend]
            .createUser(data)
            .catch(ex => logger.error('User create failed with exception', ex));
        }
      }

      await User.update(
        {
          status: 'archived',
        },
        {
          where: {
            ordermentum_id: archivedRetailerIds,
          },
          transaction: txn,
        }
      );
      /*
       * Remove affected entities for archived accounts
       */
      for (const userId of archivedRetailerIds)
        await postUserDeleteAction(userId, txn);
      /**
       * Create entities for the newly created/updated account
       */
      await postUserUpsertAction(user.id, txn);
      await txn.commit();
      return data;
    } catch (ex) {
      logger.error(JSON.stringify(ex), 'EXCEPTION WHILE MERGING USERS');
      if (txn) {
        await txn.rollback().catch(e => {
          logger.error('Rollback failed', e);
        });
      }

      if (ex instanceof httperrors) {
        throw ex;
      }

      throw httperrors.BadRequest(ex);
    }
  },
  funding: async (userId: string) => {
    const user = await User.findByPk(userId);
    if (!user) throw httperrors.NotFound();

    const journal = await JournalTransaction.Journal.findOne({
      where: {
        name: JournalName.FUNDS,
        accountId: userId,
      },
    });

    if (!journal)
      return {
        enabled: false,
      };

    if (!user.configuration.funding?.enabled)
      return {
        enabled: false,
        limit:
          journal?.balanceConstraints?.available?.lte ??
          journal?.balanceConstraints?.available?.lt ??
          0,
        available: journal.balances.available.amount ?? 0,
      };

    return {
      enabled: true,
      limit:
        journal.balanceConstraints.available?.lte ??
        journal.balanceConstraints.available?.lt ??
        0,
      available: journal.balances.available.amount ?? 0,
    };
  },
  updateFunding: async (userId, params) => {
    const { error, value } = updateFundingSchema.validate({
      ...params,
      userId,
    });

    if (error) {
      throw httperrors.BadRequest(error.message);
    }

    try {
      const instance = await User.findOne({
        where: {
          id: userId,
        },
      });

      if (!instance) {
        throw httperrors.NotFound();
      }

      const data = {
        configuration: {
          ...instance.configuration,
          funding: {
            enabled: value.enabled,
          },
        },
      };
      await instance.update(data);

      return { success: true };
      // # skipcq: JS-0323
    } catch (ex: any) {
      logger.error(ex, 'Failed to update funding', ex?.response?.data);

      if (ex instanceof httperrors) {
        throw ex;
      }
      throw httperrors.BadRequest(ex);
    }
  },
  holdFunds: async (
    userId: string,
    payload: HoldFunds,
    log: Logger,
    tryAsync: boolean // Currently only do async with Zai
  ) => {
    const { error, value } = holdFundsSchema.validate({
      ...payload,
      userId,
    });

    if (error) {
      throw httperrors.BadRequest(error.message);
    }

    // TODO JOURNAL_V3

    const transactionRecord = await holdFundsInWalletAction(
      value,
      tryAsync,
      log
    );

    return transactionRecord;
  },
  revertHeldFunds: async (
    userId: string,
    payload: RevertHeldFunds,
    log: Logger
  ) => {
    const { error, value } = revertHeldFundsSchema.validate({
      ...payload,
      userId,
    });

    if (error) {
      throw httperrors.BadRequest(error.message);
    }

    // TODO JOURNAL_V3

    await revertHeldFundsAction(value, log);
    return { success: true };
  },
  balance: async (userId: string, log: Logger) => {
    const { error, value } = validUUID.validate(userId);

    if (error) {
      throw httperrors.BadRequest(error.message);
    }
    try {
      const user = await User.findByPk(value);
      if (!user) throw httperrors.NotFound();
      const balanceAmount = await getUserBalance(user);

      return { balance: balanceAmount };
    } catch (err) {
      log.error(err);
      if (err instanceof httperrors) throw err;
      throw httperrors.InternalServerError((err as Error).message);
    }
  },
};
