import { getTemporalClient } from '@ordermentum/temporal';
import Logger from 'bunyan';
import { count } from '../lib/stats';
import { PromotionalPaymentRequest } from '../ng/workflow/types/promotional_payment.types';
import { WorkflowFactory } from '../ng/workflow/workflow.factory';

/**
 * The controller for the promotional payment workflow
 */
export const PromotionalPaymentController = {
  /**
   * Create a promotional payment
   * @param request - The promotional payment request
   * @param log - The logger
   */
  async makePromotionalPayment(
    request: PromotionalPaymentRequest,
    log: Logger
  ) {
    count('payments_sponsored_promo_redesign_count');

    // NOTE: This would normally be injected via a dependency injection container, this
    // is a temporary step to enable this new code architecture.
    const workflowFactory = new WorkflowFactory(await getTemporalClient());
    const result = await workflowFactory.makePromotionalPayment(request, log);

    return result;
  },
};
