import httpErrors from 'httperrors';
import { v4 as uuid } from 'uuid';
import Logger from 'bunyan';
import { Op } from 'sequelize';
import {
  Transaction,
  BankPaymentMethod,
  CardPaymentMethod,
  Job,
  sequelize,
  PaymentMethod,
} from '../models';
import {
  TransactionAttributes,
  SerializeAttributes,
  PaymentType,
  truncateDeclineReason,
  Backend,
} from '../models/transaction';
import { logger } from '../lib/logger';
import refund from '../actions/refund';
import {
  PaymentOptions,
  transactionCreateAction,
} from '../actions/transaction_create_action';
import { discountTransactionCreateAction } from '../actions/discount_transaction_create_action';
import transactionValidator from '../validations/transaction';
import { JournalTransaction } from '../lib/journal_v2/journal_v2';
import { systemConfig } from '../system_config';
import { changeEvent } from '../helpers/transaction_change_event';
import { journalv3 } from '../lib/journal_v3/journal_operations';
import { count, histogram, increment } from '../lib/stats';

const { fromBody, toBody } = Transaction;

export const validatePaymentMethod = async (
  transaction: TransactionAttributes
): Promise<
  | { type: PaymentType; backend: Backend }
  | { type: 'unknown' | 'foodbomb'; backend?: Backend }
> => {
  // An accounting transaction does not require a payment method
  if (transaction.transactionType === 'accounting') {
    if (transaction.type === 'foodbomb') {
      return { type: 'foodbomb' };
    }
    return { type: 'unknown' };
  }

  const paymentMethodMissing =
    !transaction.paymentMethodId &&
    !transaction.wallet_payment_method_id &&
    !transaction.card_payment_method_id &&
    !transaction.bank_payment_method_id;

  if (paymentMethodMissing) {
    throw httpErrors.BadRequest(
      'Transaction must have an associated payment method'
    );
  }

  if (transaction.bank_payment_method_id) {
    const method = await BankPaymentMethod.findByPk(
      transaction.bank_payment_method_id
    );

    if (!method)
      throw httpErrors.BadRequest(
        'Transaction must have an associated payment method'
      );
    if (method.user_id !== transaction.buyer_id)
      throw httpErrors.BadRequest('Payment method must be owned by retailer');

    return { type: 'direct', backend: method.backend as Backend };
  }

  if (transaction.card_payment_method_id) {
    const method = await CardPaymentMethod.findByPk(
      transaction.card_payment_method_id
    );
    if (!method)
      throw httpErrors.BadRequest(
        'Transaction must have an associated payment method'
      );
    if (method.user_id !== transaction.buyer_id)
      throw httpErrors.BadRequest('Payment method must be owned by retailer');

    return {
      type: method.issuer as PaymentType,
      backend: method.backend as Backend,
    };
  }

  if (transaction.paymentMethodId) {
    const paymentMethod = await PaymentMethod.findByPk(
      transaction.paymentMethodId
    );

    if (!paymentMethod)
      throw httpErrors.BadRequest(
        'Transaction must have an associated payment method'
      );
    if (paymentMethod.accountId !== transaction.buyer_id)
      throw httpErrors.BadRequest('Payment method must be owned by retailer');
    return { type: paymentMethod.type, backend: paymentMethod.backend };
  }

  return { type: 'wallet', backend: Backend.PROMISEPAY };
};

export type RefundTransactionParams = {
  invoices: {
    [id: string]: number | string; // Cents
  };
  message: string;
  userId: string;
  total: number; // Cents
};

function replaceQueryWithOp(
  query: any,
  inputKey: string,
  outputKey: string,
  op: symbol
) {
  if (query?.where?.[inputKey]) {
    // eslint-disable-next-line no-param-reassign
    query.where[outputKey] = {
      [op]: query?.where?.[inputKey],
    };
    // eslint-disable-next-line no-param-reassign
    delete query.where[inputKey];
  }
}

/**
 * q in all is meant to be the parsed query from qs-to-sequelize
 * where it takes a query string parsed and maps to sequelize query (where, limit etc)
 */
export const TransactionController = {
  // TODO SMELL: `any` types
  all: async q => {
    const query = q;

    replaceQueryWithOp(query, 'invoiceId', 'invoiceIds', Op.contains);
    replaceQueryWithOp(query, 'orderId', 'orderIds', Op.contains);
    replaceQueryWithOp(query, 'stateIn', 'state', Op.in);

    const results = await Transaction.findAll(query);
    const values = results.map(r => r.get());

    return values.map(toBody);
  },

  // TODO SMELL: `any` types
  find: async (id, q) => {
    const query = q;
    const result = await Transaction.findByPk(id, query);
    if (!result) throw httpErrors.NotFound();
    return toBody(result.get());
  },

  // TODO SMELL: `any` types
  update: async params => {
    const instance = await Transaction.findByPk(params.id);
    if (!instance) throw httpErrors.NotFound();
    const previousEventModel = instance;

    if (instance.state === params.state) return toBody(instance);

    instance.set({
      state: params.state,
      declineReason: truncateDeclineReason(params.declineReason),
      declineCode: params.declineCode,
    });

    const journal = new JournalTransaction(instance.id);
    const txn = await sequelize.transaction();

    try {
      const updated = await instance.save({
        transaction: txn,
      });

      if (systemConfig.DOUBLE_ENTRY_JOURNAL) {
        if (
          [
            Transaction.states.completed,
            Transaction.states.payment_deposited,
          ].includes(params.state)
        )
          await journal.markEntriesSettled(txn);
        else if (params.state === Transaction.states.failed)
          await journal.markEntriesArchived(txn);
      }

      const body = toBody(updated.get());
      await Job.create(
        {
          name: 'notification',
          nextRunAt: new Date().toISOString(),
          data: {
            transactionId: instance.id,
          },
        },
        {
          transaction: txn,
        }
      );
      await txn.commit();
      const currentEventModel = updated;
      await changeEvent(currentEventModel, previousEventModel, {
        userId: params.user_id,
      });
      return body;
    } catch (err: any) {
      await txn.rollback();
      throw httpErrors.InternalServerError(err.message);
    }
  },

  /**
   * Create new fund transfer
   */
  create: async (
    params: SerializeAttributes,
    log: Logger,
    tryAsync: boolean // Currently only do async with Zai
  ): Promise<SerializeAttributes> => {
    const startTime = Date.now();
    logger.info('create transaction payload', { params, tryAsync });

    if (!params) {
      throw httpErrors.BadRequest('No data');
    }

    // TODO SMELL: `any` types
    let values;
    try {
      values = await transactionValidator.validateAsync(params);
    } catch (e) {
      throw httpErrors.BadRequest((e as Error).message);
    }

    const { funded, useBuyerWallet, ...rest } = values;
    const opts: PaymentOptions = {
      fundedByPrinciple: funded,
      useBuyerWallet,
      tryAsync,
    };

    const payload = fromBody(rest);

    payload.state =
      payload.transactionType === 'accounting'
        ? Transaction.states.completed
        : Transaction.states.pending;

    if (!payload.id) payload.id = uuid();

    // At this stage the transaction is defaulted to be processed by the `promisepay` backend
    // We'll need to:
    // - verify that the payment method belongs to the promisepay backend
    // - if not, update backend based on the payment method
    const { type, backend = payload.backend } = await validatePaymentMethod(
      payload
    );

    payload.type = type;
    // Note: Backend is purely payment method derivative
    // In the future, we should intersect backend from the user with the payment method
    payload.backend = backend;

    const metricTags = {
      backend: backend || 'unknown',
      transaction_type: payload.transactionType || 'unknown',
      payment_method_type: type || 'unknown',
      async: tryAsync.toString(),
    };

    try {
      const sale = await journalv3.userSale(
        async () => {
          // TODO ARCHITECTURE: Is it possible to merge these two flows when re-arching with more async operations happening in a steveo workflow?
          if (payload.discount) {
            count('payments_discount_transaction_count');
            const result = await discountTransactionCreateAction(
              payload,
              opts,
              log
            );
            return {
              returned: toBody(result.transaction.get()),
              details: result.details,
            };
          }

          const result = await transactionCreateAction(payload, opts, log);

          return {
            returned: toBody(result.transaction.get()),
            details: result.details,
          };
        },
        undefined,
        log
      );

      // Success metrics
      const duration = Date.now() - startTime;
      histogram('payments_transaction_create_duration_ms', duration, {
        ...metricTags,
        result: 'success',
      });
      count('payments_transaction_create_success_count');

      if (tryAsync) {
        increment('payments_async_transaction_count', 1, metricTags);
      } else {
        increment('payments_sync_transaction_count', 1, metricTags);
      }

      return sale.returned;
    } catch (e) {
      const duration = Date.now() - startTime;
      const errorType = (e as Error).constructor.name;

      histogram('payments_transaction_create_duration_ms', duration, {
        ...metricTags,
        result: 'error',
        error_type: errorType,
      });
      count('payments_transaction_create_failure_count');
      increment('payments_transaction_create_error_rate', 1, {
        ...metricTags,
        error_type: errorType,
      });

      throw e;
    }
  },

  /**
   * Refund previous transaction entry point
   */
  refund: async (id: string, params: RefundTransactionParams, _log: Logger) => {
    const transaction = await refund(id, params);
    return transaction;
  },
};

export default TransactionController;
