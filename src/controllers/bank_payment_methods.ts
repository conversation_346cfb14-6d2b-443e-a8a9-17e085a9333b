/* eslint-disable default-param-last, default-case-last */
import config from 'config';
import httperrors from 'httperrors';
import { cloneDeep } from 'lodash';
import {
  parsePhoneNumberWithError,
  ParseError as PhoneNumberParseError,
} from 'libphonenumber-js/mobile';
import Logger from 'bunyan';
import { sequelize, User, BankPaymentMethod } from '../models';
import backends from '../lib/backends';
import { logger } from '../lib/logger';
import { Backend } from '../models/transaction';
import { count } from '../lib/stats';
import { changeEvent } from '../helpers/payment_method_change_event';
import { BankPaymentMethodViewModel } from '../view_models/bank_payment_method_view_model';
import { systemConfig } from '../system_config';
import { sendBankAccountChangesEmail } from '../actions/notifications/send_bank_account_changes_email';
import { isBankDetailsUpdated } from '../helpers/check_bank_detail_changes';
import { BankPaymentMethodModel } from '../models/bank_payment_method';
import { sendTemplateMessage } from '../actions/notifications/send_sms';
import { UserInstance } from '../models/user';

const OM_BASE_URL = config.get<string>('OM_BASE_URL');

const sendBankAccountChangesSms = async (
  user: UserInstance,
  log: Logger
): Promise<void> => {
  if (!user.mobile_number) {
    return;
  }

  try {
    const parsedPhone = parsePhoneNumberWithError(user.mobile_number, {
      defaultCountry: 'AU',
    });

    await sendTemplateMessage({
      payload: {
        to: parsedPhone.number,
        from: 'Ordermentum',
        template: 'bank_account_changes',
        params: {
          firstName: user.first_name,
          url: `${OM_BASE_URL}/supplier/${user.ordermentum_id}/settings/general/payments`,
        },
      },
      log,
    });
  } catch (error) {
    let errorMessage = 'Unexpected error sending SMS';
    if (error instanceof PhoneNumberParseError) {
      errorMessage = `Error parsing mobile number: ${user.mobile_number} - ${error.message}`;
    } else {
      errorMessage = `Unexpected error sending SMS: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }

    log.error({ errorMessage, error }, 'Error sending SMS');
  }
};

export const checkSettlementDetails = async bank => {
  if (bank.is_settlement_account) {
    // Ensure KYC provisions are satisfied
    const user = await User.findOne({
      where: {
        id: bank.user_id,
      },
    });

    if (!user) {
      throw new Error('Unknown User');
    }

    if (!user.company_name || !user.mobile_number) {
      const ERROR_MESSAGE = `Supplier must have company information & mobile
        details configured in order to pass KYC checks for disbursement
        - ${user.company_name} ${user.mobile_number}`;
      throw new Error(ERROR_MESSAGE);
    }
  }
};

export const updateSettlementFlag = async (
  userId,
  backend = Backend.PROMISEPAY,
  txn
) => {
  // Clear other settlement accounts
  await BankPaymentMethod.update(
    {
      is_settlement_account: false,
    },
    {
      where: {
        is_settlement_account: true,
        backend,
        user_id: userId,
      },
      transaction: txn,
    }
  );
};

export default {
  all: async query => {
    const results = await BankPaymentMethod.findAll(query);
    return results.map(result =>
      BankPaymentMethodViewModel.build(result).toJSON()
    );
  },

  find: async id => {
    const result = await BankPaymentMethod.findByPk(id);
    if (!result) {
      throw httperrors.NotFound();
    }

    return BankPaymentMethodViewModel.build(result).toJSON();
  },

  create: async params => {
    const payload = { ...params, ...(params.data ?? {}) };

    const log = logger.child({
      userId: params.user_id,
      action: 'createBankPaymentMethod',
    });

    const user = await User.findOne({
      where: {
        id: params.user_id,
      },
    });

    if (!user) {
      throw httperrors.BadRequest('Invalid User');
    }

    if (params.is_settlement_account && params.backend === Backend.STRIPE) {
      payload.is_settlement_account = false;
    }

    try {
      await checkSettlementDetails(payload);
    } catch (e) {
      log.error({ error: e }, 'FAILED SETTLEMENT CHECK');
      throw httperrors.BadRequest(e);
    }

    let txn;
    try {
      txn = await sequelize.transaction();
      await updateSettlementFlag(user.id, payload.backend as Backend, txn);

      const bankPaymentMethod = await BankPaymentMethod.create(
        { ...payload, user_id: user.id },
        {
          transaction: txn,
        }
      );
      const previousEventModel = bankPaymentMethod;

      if (!systemConfig.SKIP_BACKENDS) {
        const result = await backends[payload.backend].createBank({
          ...payload,
          user: user.toJSON(),
        });

        if (result.stripeCustomerId) {
          await user.update(
            { stripe_customer_id: result.stripeCustomerId },
            {
              transaction: txn,
            }
          );
        }

        log.debug(
          { backend: payload.backend, backendId: result.backend_id },
          'Creating bank payment method'
        );

        await bankPaymentMethod.update(
          { backend_id: result.backend_id },
          {
            transaction: txn,
          }
        );
      }

      await txn.commit();

      await bankPaymentMethod.reload();
      const currentEventModel = bankPaymentMethod;
      changeEvent('bank', user, currentEventModel, previousEventModel, {
        userId: params.user_id,
      });

      // send email to supplier owners
      await sendBankAccountChangesEmail({
        supplierId: user.ordermentum_id,
        name: user.first_name,
        updatedBy: user.first_name,
      });

      // send sms notif if user has mobile number
      await sendBankAccountChangesSms(user, log);

      log.info(
        { bankMethodId: bankPaymentMethod.id },
        'Successfully created bank method'
      );
      return BankPaymentMethodViewModel.build(bankPaymentMethod).toJSON();
    } catch (ex) {
      const message = (ex as Error)?.message ?? 'Error creating bank account';
      count('payments_failed_bank');
      log.error({ error: ex }, message);
      await txn.rollback();
      throw httperrors.BadRequest(ex);
    }
  },

  update: async (id, params) => {
    const payload = { ...params, ...(params.data ?? {}) };
    let txn;

    const log = logger.child({
      userId: params.user_id,
      action: 'updateBankPaymentMethod',
    });

    const user = await User.findOne({
      where: {
        id: payload.user_id,
      },
    });

    if (!user) {
      throw httperrors.BadRequest('Invalid User');
    }

    try {
      txn = await sequelize.transaction();
      const instance = await BankPaymentMethod.findByPk(id, {
        transaction: txn,
      });

      const previousEventModel = instance;

      if (!instance) throw httperrors.NotFound();

      let data: BankPaymentMethodModel = {
        ...instance.get(),
        ...payload,
        backend_id: instance.backend_id,
        id: instance.id,
      };

      await checkSettlementDetails(data);
      await updateSettlementFlag(user.id, params.backend, txn);

      // Creating a deep copy that is not affected by reloads
      const previousAttributes: BankPaymentMethodModel = cloneDeep(
        instance.get({ plain: true })
      );

      await instance.update(
        { ...data, user_id: user.id },
        { transaction: txn }
      );

      if (!systemConfig.SKIP_BACKENDS) {
        data = await backends[data.backend].updateBank({
          ...data,
          email: user.email,
          user_id: user.external_id,
        });
      }

      await txn.commit();

      await instance.reload();
      const currentEventModel = instance;
      changeEvent('bank', user, currentEventModel, previousEventModel, {
        userId: params.user_id,
      });

      // Check if there was changes to the banking details for the user
      // and send email and sms notification to the supplier owners
      if (isBankDetailsUpdated(data, previousAttributes)) {
        // send email notif
        await sendBankAccountChangesEmail({
          supplierId: user.ordermentum_id,
          name: user.company_name || user.first_name,
          updatedBy: user.first_name,
        });

        // send sms notif if user has mobile number
        await sendBankAccountChangesSms(user, log);
      }

      log.info(
        { bankMethodId: instance.id },
        'Successfully updated bank method'
      );
      return BankPaymentMethodViewModel.build(instance).toJSON();
    } catch (ex) {
      log.error({ error: ex }, 'Error updating bank account');

      if (txn) await txn.rollback();

      if (ex instanceof httperrors) {
        throw ex;
      }
      throw httperrors.BadRequest(ex);
    }
  },

  destroy: async id => {
    const instance = await BankPaymentMethod.findByPk(id, {
      include: [{ model: User, as: 'user' }],
    });
    if (!instance) {
      throw httperrors.NotFound();
    }
    const previousEventModel = instance;
    await instance.destroy();

    const currentEventModel = instance;
    changeEvent('bank', instance.user!, currentEventModel, previousEventModel);
    return { success: true };
  },
};
