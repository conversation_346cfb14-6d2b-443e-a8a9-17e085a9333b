import config from 'config';
import { startTemporalWorkers } from '@ordermentum/temporal';
import HealthChecker from '@ordermentum/health';
import assert from 'assert';
import logger from './lib/logger';
import steveoKafka from './lib/steveo/steveo_kafka';
import steveoSQS from './lib/steveo/steveo';
import { sequelize } from './models';
import redis from './lib/redis';
import { jobScheduler } from './jobs/job_scheduler';
import { client } from './lib/backends/promisepay/client';
import { PaymentMethodService } from './ng/domain/services/domain/payment_method.service';
import { PaymentMethodRepository } from './ng/domain/repositories/payment_method.repository';
import { CardPaymentMethodRepository } from './ng/domain/repositories/card_payment_method.repository';
import { UserRepository } from './ng/domain/repositories/user.repository';
import { UserService } from './ng/domain/services/domain/user.service';
import { TransactionsRepository } from './ng/domain/repositories/transactions.repository';
import { ZaiFundTransferGateway } from './ng/domain/services/gateways/zai_fund_transfer.gateway';
import { ZaiUserManagementGateway } from './ng/domain/services/gateways/zai_user_management.gateway';
import { createActivities } from './ng/workflow/activities.factory';
import { makeZaiPaymentWorkflowDefinition } from './ng/workflow/definitions/make_zai_payment.workflow';
import { upfrontPaymentWorkflowDefinition } from './ng/workflow/definitions/upfront_payment.workflow';
import { promotionalPaymentWorkflowDefinition } from './ng/workflow/definitions/promotional_payment.workflow';
import { refundWorkflowDefinition } from './ng/workflow/definitions/refund.workflow';
import { walletTransferWorkflowDefinition } from './ng/workflow/definitions/wallet_transfer.workflow';

import { esClient } from './utils/elasticsearch';

import { TransactionService } from './ng/domain/services/domain/transaction.service';
import { ZaiWalletsGateway } from './ng/domain/services/gateways/zai_wallets.gateway';
import { BankPaymentMethodRepository } from './ng/domain/repositories/bank_payment_method.repository';

const nodeEnv = config.get<string>('NODE_ENV');
const log = logger.child({
  nodeEnv,
});
const enabledWorkers = config.get<string[]>('ENABLED_WORKERS');
const temporalQueues = config.get<string[]>('TEMPORAL_QUEUES');
const deployed = config.get<boolean>('DEPLOYED');
const port = config.get<string>('PORT');

require('./tasks');

const healthChecker = new HealthChecker({
  maxConsecutiveFailures: 5,
  checkIntervalMs: 1000,
  logger: log,
  recurring: true,
})
  .register('redis', () => redis.ping())
  .register('postgresql', () => sequelize.query('select current_timestamp'))
  .register('elasticsearch', () => esClient.ping());

/**
 * Start the database jobs.
 */
const startDatabaseJobs = async () => {
  log.info('Starting jobs scheduler');

  await jobScheduler.runScheduledJobs();
  healthChecker.register('job-scheduler', () => jobScheduler.healthCheck());
  healthChecker.on('terminate', async () => {
    log.info('Stopping job scheduler');
    await jobScheduler.terminate();
  });
  healthChecker.on('resume', () => jobScheduler.resume());
  healthChecker.on('pause', () => jobScheduler.pause());
};

const startSQSConsumer = async () => {
  log.info('Starting SQS consumer');
  await steveoSQS.start();
  healthChecker.register('sqs', () => steveoSQS.runner().healthCheck());
  healthChecker.on('terminate', async () => {
    log.info('Stopping SQS consumer');
    await steveoSQS.stop();
  });
  healthChecker.on('resume', () => steveoSQS.resume());

  healthChecker.on('pause', () => steveoSQS.pause());
};

const startKafkaConsumer = async () => {
  log.info('Starting Kafka consumer');
  await steveoKafka.start();
  healthChecker.register('kafka', () => steveoKafka.runner().healthCheck());
  healthChecker.on('terminate', async () => {
    log.info('Stopping Kafka consumer');
    await steveoKafka.stop();
  });
  healthChecker.on('resume', () => steveoKafka.resume());
  healthChecker.on('pause', () => steveoKafka.pause());
};

/**
 * Start the Temporal workers.
 */
async function startTemporal() {
  try {
    const userRepository = new UserRepository(log);
    const paymentMethodRepository = new PaymentMethodRepository(log);
    const cardPaymentMethodRepository = new CardPaymentMethodRepository(log);
    const bankPaymentMethodRepository = new BankPaymentMethodRepository(log);
    const transactionsRepository = new TransactionsRepository(log);
    const zaiFundTransferGateway = new ZaiFundTransferGateway(log, client);
    const zaiUserManagementGateway = new ZaiUserManagementGateway(log, client);
    const zaiWalletsGateway = new ZaiWalletsGateway(log, client);
    const userService = new UserService(
      userRepository,
      zaiUserManagementGateway,
      log
    );
    const paymentMethodService = new PaymentMethodService(
      paymentMethodRepository,
      bankPaymentMethodRepository,
      cardPaymentMethodRepository,
      log
    );
    const transactionService = new TransactionService(
      transactionsRepository,
      userRepository,
      log
    );

    // Create the activities
    // This uses a very simple DI approach to inject the dependencies into the activities
    // recommended by Temporal
    // https://docs.temporal.io/develop/typescript/core-application#activity-design-patterns
    const activities = createActivities(
      transactionService,
      transactionsRepository,
      zaiFundTransferGateway,
      zaiWalletsGateway,
      userService,
      userRepository,
      paymentMethodService
    );

    // Start worfklows
    const allWorkflows = [
      makeZaiPaymentWorkflowDefinition,
      upfrontPaymentWorkflowDefinition,
      promotionalPaymentWorkflowDefinition,
      refundWorkflowDefinition,
      walletTransferWorkflowDefinition,
    ];
    const workflows = temporalQueues.length
      ? allWorkflows.filter(workflow =>
          temporalQueues.includes(workflow.queueName)
        )
      : allWorkflows;

    assert(workflows.length > 0, 'No workflows to start');

    log.info('Starting Temporal workers');

    const temporalManager = await startTemporalWorkers(activities, workflows, {
      // Log level for temporal sdk
      logLevel: 'WARN',
      logger: log,
      prometheusMetricsPort: deployed ? port : undefined,
    });

    // Register health checks
    healthChecker.register('temporal', () => {
      const { state, reason } = temporalManager.getHealthStatus();
      if (state === 'healthy') {
        return Promise.resolve();
      }
      return Promise.reject(new Error(reason));
    });
    healthChecker.on('terminate', async () => {
      log.info('Stopping Temporal workers');
      await temporalManager.shutdown();
    });
  } catch (err) {
    log.error({ err }, 'Error starting Temporal workers');
    throw err;
  }
}

process.on('unhandledRejection', err => {
  log.error({ err }, 'Unhandled promise rejection!');
});

/**
 * Start the message queue consumers, database jobs, and Temporal workers.
 */
export const start = async () => {
  log.info('Starting message queue consumers');

  await Promise.all([
    enabledWorkers.includes('temporal') ? startTemporal() : Promise.resolve(),
    enabledWorkers.includes('sqs') ? startSQSConsumer() : Promise.resolve(),
    enabledWorkers.includes('kafka') ? startKafkaConsumer() : Promise.resolve(),
    enabledWorkers.includes('database')
      ? startDatabaseJobs()
      : Promise.resolve(),
  ]);

  healthChecker.on('terminate', () => process.exit(1));

  await healthChecker.start();
};
