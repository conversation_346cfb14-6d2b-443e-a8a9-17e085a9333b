import { <PERSON><PERSON> } from 'stripe';
import Logger from 'bunyan';
import { FindOptions, Op, Transaction, WhereOptions } from 'sequelize';
import moment from 'moment';
import { backoff } from 'retry-machine';
import { sequelize, User } from '../models';
import stripe from '../lib/backends/stripe/client';
import defaultLogger from '../lib/logger';
import { UserInstance } from '../models/user';
import { createUser as createStripeUser } from '../lib/backends/stripe';
import { sleep } from '../utils/timer';
import { batch } from '../utils/query';

const BASE_DELAY_MS = 1000; // 1 second
const FACTOR = 2;
const MAX_ATTEMPTS = 5;

async function withStripeRetry<T>(
  fn: () => Promise<T>,
  log: Logger,
  retryCount = 0,
  maxAttempts = MAX_ATTEMPTS,
  baseDelay = BASE_DELAY_MS,
  factor = FACTOR
): Promise<T> {
  try {
    return await fn();
  } catch (err: any) {
    // Only handle 429s
    if (err?.statusCode === 429) {
      // Check for Retry-After header first
      const retryAfter = err?.headers?.['retry-after'];
      let delay: number = backoff(baseDelay, factor, retryCount + 1);

      if (retryAfter) {
        const retryAfterMs = parseInt(retryAfter, 10) * 1000;
        if (!Number.isNaN(retryAfterMs)) {
          delay = backoff(retryAfterMs, factor, retryCount + 1);
        }
      }

      if (retryCount >= maxAttempts) {
        log.error(
          {
            retryCount,
            maxAttempts,
            error: err.message,
          },
          'Max retry attempts reached for Stripe API call'
        );
        throw err;
      }

      log.warn(
        {
          retryCount: retryCount + 1,
          maxAttempts,
          nextAttemptIn: delay,
          error: err.message,
        },
        'Stripe rate limit hit (429). Retrying with exponential backoff'
      );

      await sleep(delay);
      return withStripeRetry(
        fn,
        log,
        retryCount + 1,
        maxAttempts,
        baseDelay,
        factor
      );
    }
    throw err;
  }
}

const isStripeCustomer = (
  user: Stripe.Customer | Stripe.DeletedCustomer
): user is Stripe.Customer => !(user as Stripe.DeletedCustomer).deleted;

const updatePaymentUserDetails = (
  user: UserInstance,
  stripeCustomerId: string,
  txn: Transaction
) =>
  user.update(
    {
      stripe_customer_id: stripeCustomerId,
      configuration: {
        ...user.configuration,
        stripeSyncedAt: new Date().toISOString(),
      },
    },
    { transaction: txn }
  );

export const createAndUpdatePaymentUser = async (
  user: UserInstance,
  txn: Transaction,
  log = defaultLogger
) => {
  const customer = await createStripeUser(user);
  await updatePaymentUserDetails(user, customer.stripe_customer_id, txn);

  log.info(
    {
      customer: customer.stripe_customer_id,
    },
    `Created stripe customer for user ${user.ordermentum_id}:`
  );
};

const searchStripeCustomer = async (
  user: UserInstance,
  log = defaultLogger
): Promise<Stripe.Customer | null> => {
  log.info(
    { userId: user.id },
    'User has no existing stripe_customer_id. Searching stripe for existing ordermentumId metadata'
  );
  const result = await withStripeRetry(
    () =>
      stripe.customers.search({
        query: `metadata['ordermentumId']:'${user.ordermentum_id}'`,
        limit: 1,
      }),
    log
  );

  if (result.total_count || result.data?.length) {
    const [customer] = result.data;
    return customer;
  }

  log.info(
    `Unable to find existing customers in stripe with ordermentumId ${user.ordermentum_id}`
  );
  return null;
};

const getCustomerDefaultPaymentMethod = async (
  stripeCustomerId: string,
  log = defaultLogger
): Promise<string | null> => {
  const paymentMethods = await withStripeRetry(
    () =>
      stripe.customers.listPaymentMethods(stripeCustomerId, {
        type: 'card',
      }),
    log
  );

  if (!paymentMethods.data?.length) {
    return null;
  }

  // If the default method isn't found, return the latest (most recent one by creation)
  const latestPaymentMethod = paymentMethods.data.sort((a, b) =>
    a.created < b.created ? 1 : -1
  )[0];

  log.info(
    `Multiple payment methods found. Using the latest payment method ${latestPaymentMethod.id}`
  );

  return latestPaymentMethod.id;
};

export const updatePaymentMethodToAllowPrefill = async (
  user: UserInstance,
  stripePaymentMethodId: string
) => {
  const paymentMethod = await withStripeRetry(
    () => stripe.paymentMethods.retrieve(stripePaymentMethodId),
    defaultLogger
  );

  // The customer has a saved card. Checkout only supports prefilling card payment methods.
  // 1. The saved card has allow_redisplay set to always or you adjusted the default display setting.
  // The payment method includes billing_details and requires values for email, name, and all address fields.
  // See: https://docs.stripe.com/payments/existing-customers?platform=web&ui=stripe-hosted#prefill-payment-fields
  await withStripeRetry(
    () =>
      stripe.paymentMethods.update(stripePaymentMethodId, {
        allow_redisplay: 'always',
        billing_details: {
          address: {
            country: paymentMethod.billing_details.address?.country ?? 'AU',
          },
          email: user.email,
          name: paymentMethod.billing_details.name,
        },
      }),
    defaultLogger
  );

  return {
    id: paymentMethod.id,
    allow_redisplay: paymentMethod.allow_redisplay,
  };
};

const getUserFindOptions = ({
  entityIds,
  entityType,
}: {
  entityIds?: string[];
  entityType?: 'retailer' | 'supplier';
}): FindOptions => {
  const hasEntityIds = Boolean(entityIds?.length);
  let whereClause: WhereOptions = {
    configuration: {
      stripeSyncedAt: { [Op.is]: null },
    },
    status: 'active',
    [Op.and]: [
      // Only process users with mobile numbers less than or equal to 20 characters
      sequelize.where(
        sequelize.fn(
          'LENGTH',
          sequelize.fn('TRIM', sequelize.col('mobile_number'))
        ),
        {
          [Op.lte]: 20,
        }
      ),
      // Exclude mobile numbers containing alphabetic characters as they are likely data entry errors
      // or notes (e.g. "call after 5pm", "ask for John"). These numbers cannot be used for
      // SMS verification or communication purposes in stripes end
      sequelize.where(sequelize.fn('TRIM', sequelize.col('mobile_number')), {
        [Op.notRegexp]: '[a-zA-Z]',
      }),
    ],
  };

  if (entityType) {
    const isSupplier = entityType === 'supplier';
    whereClause.configuration.supplier = isSupplier;
  } else {
    whereClause.configuration.supplier = { [Op.is]: null };
  }

  if (hasEntityIds) {
    whereClause = {
      ...whereClause,
      ordermentum_id: { [Op.in]: entityIds },
    };
  }

  const options: FindOptions = {
    where: whereClause,
  };

  return options;
};

/**
 * Processes an individual user by either creating or updating their Stripe customer record.
 *
 * Main function:
 * 1. Logs the start of the sync process for the specified user.
 * 2. Creates a sequelize transaction
 * 3. Checks if the user already has a Stripe customer ID:
 *    - If not, searches for an existing customer in Stripe
 *    - If the Stripe customer is not found or deleted, creates a new customer in Stripe and attaches to payment user
 * 4. Verifies if the user has a default payment method in Stripe, and if not, retrieves
 * 5. Updates the customer's default payment method with the right details to allow it to be presented in checkout
 * 6. Updates the Stripe customer metadata with the sync details and sets the default payment method.
 * 7. Logs errors if any issues arise during the sync process, including Stripe-specific errors.
 *
 * When does a user process gets finished (commited and updated the stripeSyncedAt flag):
 * 1. If a payment user doesn't have stripe_customer_id and no existing records in stripe when searched
 * 2. If a payment user had stripe_customer_id but existing record in stripe is tag as DELETED
 * 3. If a payment user has an existing account in stripe but no payment methods attached that can be updated
 * 4. If a payment user has an existing account and we successfully updated their payment methods to allow prefills in checkout
 *
 * @param {UserInstance} user - The user object to process and sync with Stripe.
 * @param {typeof defaultLogger} log - The logger instance for logging process information.
 *
 * @returns {Promise<void>} - Resolves when the user sync process is complete.
 *
 */
const process = async (
  user: UserInstance,
  log: typeof defaultLogger
): Promise<void> => {
  const txn: Transaction = await sequelize.transaction();
  try {
    log.info(
      `[Processing] ${user.ordermentum_id}, ${user.company_name}, [isSupplier] ${user.configuration.supplier}, [Stripe] ${user.stripe_customer_id}`
    );

    let existingStripeCustomer: Stripe.Customer | null = null;
    if (!user.stripe_customer_id) {
      existingStripeCustomer = await searchStripeCustomer(user, log);
    } else {
      const customer = await withStripeRetry(
        () => stripe.customers.retrieve(user.stripe_customer_id),
        log
      );

      if (!isStripeCustomer(customer)) {
        log.info(
          'User was deleted from stripe. Creating a customer counterpart'
        );
        await createAndUpdatePaymentUser(user, txn, log);
        await txn.commit();
        return;
      }

      existingStripeCustomer = customer;
    }

    if (!existingStripeCustomer) {
      log.info('Unable to find user in stripe. Creating customer counterpart');
      await createAndUpdatePaymentUser(user, txn, log);
      await txn.commit();
      return;
    }

    log.info('Found customer in stripe. Updating payment method');

    let hasDefaultPaymentMethod = true;
    let paymentMethodId =
      existingStripeCustomer.invoice_settings.default_payment_method;
    if (!paymentMethodId) {
      paymentMethodId = await getCustomerDefaultPaymentMethod(
        existingStripeCustomer.id,
        log
      );
      hasDefaultPaymentMethod = false;
    }

    if (!paymentMethodId) {
      const message = `${
        hasDefaultPaymentMethod
          ? 'No default payment method in stripe'
          : 'No payment method attached'
      }`;
      log.warn(
        `${message}. Updating user configuration and marking as synced.`
      );
      await updatePaymentUserDetails(user, existingStripeCustomer.id, txn);
      await txn.commit();
      return;
    }

    const paymentMethod = await updatePaymentMethodToAllowPrefill(
      user,
      paymentMethodId as string
    );

    const customerId = existingStripeCustomer.id;
    const customerParams: Stripe.CustomerUpdateParams = {
      metadata: {
        updatedAt: moment().format('dddd, DD, MMM YYYY h:mm a'),
        updatedBy: 'payments_user_sync_script',
      },
    };

    await withStripeRetry(
      () => stripe.customers.update(customerId, customerParams),
      log
    );
    await updatePaymentUserDetails(user, existingStripeCustomer.id, txn);

    log.info(
      { paymentMethod },
      `Updated payment method for user ${user.ordermentum_id}. Updating user configuration and marking as synced`
    );

    await txn.commit();
  } catch (err) {
    await txn.rollback();
    if (err instanceof Stripe.errors.StripeError) {
      log.error(
        {
          message: err.message,
          code: err.code,
          type: err.type,
        },
        `Failed to update entity ${user.ordermentum_id}`
      );
      return;
    }
    log.error(
      { err },
      `Failed to update entity ${user.ordermentum_id}. Rolling back changes`
    );
  }
};

/**
 * Runner function responsible for fetching users from the database and synchronizing them to Stripe.
 *
 * Main function:
 * 1. Retrieves users from the database based on provided filters (entity IDs, entity type and queryLimit).
 * 2. Processes users in chunks (batches) based on the environment's batch size.
 * 3. Logs information about each user during the preview or sync.
 * 4. Calls the `process` function for each user to synchronize them with Stripe.
 * 5. Waits for a short interval between processing batches to prevent rate limiting.
 *
 * @param {string[]} [params.entityIds] - Optional array of entity IDs to limit the users to specific entities.
 * @param {'retailer' | 'supplier'} [params.entityType] - Optional entity type filter ('retailer' or 'supplier').
 * @param {number} [params.queryLimit=1000] - Maximum number of users to fetch from the database in one batch.
 * @param {boolean} params.preview - Flag to determine if the operation is a preview (logging users without syncing).
 * @param {Bunyan} [params.log=defaultLogger] - Optional logger instance for logging process details.
 *
 * @returns {Promise<void>} - Resolves when all user sync processes are complete.
 *
 */
export const runner = async ({
  entityIds,
  entityType,
  batchSize = 100,
  log = defaultLogger,
}: {
  entityIds?: string[];
  entityType?: 'retailer' | 'supplier';
  batchSize?: number;
  log?: typeof defaultLogger;
}): Promise<void> => {
  log.info(
    { entityIds, entityType },
    'Starting to process user sync to stripe'
  );

  const userFindOptions = getUserFindOptions({
    entityIds,
    entityType,
  });

  for await (const users of batch(
    User,
    {
      ...userFindOptions,
    },
    batchSize
  )) {
    log.info(`Found ${users.length} users. Syncing to stripe`);
    for (const user of users) {
      await process(user, log);
      log.info(`Sync successful for user ${user.id}\n`);
    }
  }

  log.info('Sync users to stripe task complete');
};

export default runner;
