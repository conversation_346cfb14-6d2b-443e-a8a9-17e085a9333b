import type { RetryPolicy, SignalDefinition } from '@temporalio/workflow';
import { defineSignal, setHandler, condition } from '@temporalio/workflow';

export const temporalWrapper = {
  defineSignal: <T extends unknown[]>(
    ...args: Parameters<typeof defineSignal<T>>
  ) => defineSignal<T>(...args),
  setHandler: (...args: Parameters<typeof setHandler>) => setHandler(...args),
  condition: (...args: Parameters<typeof condition>) => condition(...args),
};

// Map to store signal definitions to ensure they're only defined once
// NOTE: eslint used to bypass `any` use in temporal npm
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const signalDefinitions = new Map<string, SignalDefinition<any>>();

/**
 * Creates a signal handler and waits for the signal to be received.
 * This is a helper function to handle the common pattern of waiting for a signal
 * in a Temporal workflow.
 *
 * @param signalName - The name of the signal to wait for
 * @returns A promise that resolves with the signal arguments when the signal is received
 */
export function waitForSignal(signalName: string): Promise<void>;
export function waitForSignal<Result>(
  signalName: string
): Promise<Result | undefined>;

export function waitForSignal<Result = void>(
  signalName: string
): Promise<Result | undefined> {
  // Get or create the signal definition
  let signal = signalDefinitions.get(signalName);
  if (!signal) {
    signal = temporalWrapper.defineSignal<Result[]>(signalName);
    signalDefinitions.set(signalName, signal);
  }

  let signalReceived = false;
  let signalArgs: Result | undefined;

  // Cast the signal to any to bypass the type checking since we know the implementation is correct
  // This is safe because we're using the signal definition exactly as intended
  // NOTE: eslint used to bypass `any` use in temporal npm
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  temporalWrapper.setHandler(signal as any, (...args: Result[]) => {
    signalReceived = true;
    signalArgs = args.length > 0 ? args[0] : undefined;
  });

  return temporalWrapper.condition(() => signalReceived).then(() => signalArgs);
}

/**
 * Get the retry policy for workflows in the payments service.
 * @returns The retry policy for workflows
 */
export function getWorkflowRetryPolicy(): RetryPolicy {
  return {
    maximumAttempts: 1,
  };
}

/**
 * Get the retry policy for activities in the payments service.
 * Activities typically need more aggressive retries than workflows since they are
 * shorter-lived operations that are more likely to fail due to transient issues.
 *
 * The policy uses:
 * - Shorter initial interval (5s) to quickly retry transient failures
 * - Higher backoff coefficient (2.5) to increase intervals more aggressively
 * - More maximum attempts (8) to handle temporary issues
 * - Shorter maximum interval (1h) since activities should complete faster
 *
 * @returns The retry policy for activities
 */
export function getActivityRetryPolicy(): RetryPolicy {
  return {
    initialInterval: '10s',
    backoffCoefficient: 2,
    maximumAttempts: 3,
    maximumInterval: '1h',
  };
}
