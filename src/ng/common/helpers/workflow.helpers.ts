import { log } from '@temporalio/workflow';
import { Compensation } from '../types/workflow.types';
/**
 *
 * @param compensations
 * @param log
 */
export async function compensate(compensations: Compensation[]) {
  if (compensations.length > 0) {
    log.info('Failures encountered during workflow - compensating');

    for (const comp of compensations) {
      try {
        log.error(comp.message);

        await comp.fn();
      } catch (err) {
        log.error('Failed to compensate activity step', {
          err,
          compensation: comp.message,
        });

        // Intentionally swallow errors
      }
    }
  }
}
