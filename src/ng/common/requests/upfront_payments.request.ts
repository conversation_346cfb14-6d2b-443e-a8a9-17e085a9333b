import { z } from 'zod';

/**
 * The request to initiate an upfront payment workflow
 */
export const UpfrontPaymentSchema = z.object({
  amountCents: z
    .number()
    .int()
    .positive()
    .describe('The upfront payment amount in cents'),

  description: z.string().describe('The description of the upfront payment'),

  sellerUserId: z
    .string()
    .uuid()
    .describe('The user ID of the seller, typically be the supplier'),

  buyerUserId: z
    .string()
    .uuid()
    .describe('The user ID of the buyer, typically be the retailer'),

  orderId: z
    .string()
    .uuid()
    .describe('The order ID of the order that the upfront payment is for'),

  invoiceId: z.string().uuid().describe('The invoice ID of the invoice'),

  userId: z.string(),

  paymentMethodId: z.string(),
});

/**
 * The request to initiate an upfront payment workflow
 */
export type UpfrontPayment = z.infer<typeof UpfrontPaymentSchema>;

/**
 * The request to complete the upfront payment workflow
 */
export const UpfrontPaymentCompleteSchema = z.object({
  orderId: z
    .string()
    .uuid()
    .describe('The order ID of the order that the upfront payment is for'),

  finalAmountCents: z
    .number()
    .int()
    .positive()
    .describe(
      'The final amount in cents. Can be less than the upfront payment amount if the buyer has been refunded, or more if the buyer has paid more than the upfront payment amount.'
    ),

  description: z.string().describe('The description of the upfront payment'),
});

/**
 * The request to complete the upfront payment workflow
 */
export type UpfrontPaymentComplete = z.infer<
  typeof UpfrontPaymentCompleteSchema
>;

export const CancelUpfrontPaymentSchema = z.object({
  orderId: z
    .string()
    .uuid()
    .describe('The order ID of the order that the upfront payment is for'),

  /**
   * The reason for the revert
   */
  reason: z.string(),
});

export type CancelUpfrontPayment = z.infer<typeof CancelUpfrontPaymentSchema>;

export type CancelUpfrontPaymentResponse = {
  success: true;
};
