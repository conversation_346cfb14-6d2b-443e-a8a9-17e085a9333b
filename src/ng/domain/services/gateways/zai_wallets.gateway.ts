import Logger from 'bunyan';
import {
  GatewayWallets,
  WalletAccount,
} from '../../../common/types/gateway.types';
import { ZaiClient } from './zai_fund_transfer.gateway';

/**
 * This is a gateway class that is responsible for handling all the
 * wallet related operations
 */
export class ZaiWalletsGateway implements GatewayWallets {
  constructor(
    private readonly logger: Logger,
    private readonly zai: ZaiClient
  ) {
    // Constructor
  }

  /**
   * Get the user's wallet account.
   * @param walletId - The ID of the user to get the wallet account for.
   * @returns The user's wallet account.
   */
  async getUserWallet(walletId: string): Promise<WalletAccount> {
    const wallet = await this.zai.users
      .showUserWalletAccounts(walletId)
      .then(res => res.wallet_accounts);

    if (!wallet?.id) {
      this.logger.error({ walletId }, 'Failed to load wallet');

      throw new Error('Failed to load wallet');
    }

    return {
      accountId: wallet.id,
      balance: wallet.balance ?? 0,
    };
  }
}
