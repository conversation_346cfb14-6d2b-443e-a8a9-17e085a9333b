import assert from 'node:assert';
import Logger from 'bunyan';
import { CardPaymentMethodRepository } from '../../repositories/card_payment_method.repository';
import { PaymentMethodRepository } from '../../repositories/payment_method.repository';
import { PaymentMethodModel } from '../../../../models/payment_methods';
import { CardPaymentMethodModel } from '../../../../models/card_payment_method';
import {
  BankPaymentMethod,
  CardPaymentMethod,
  PaymentMethod,
} from '../../../../models';
import { CreateTransactionAttrs } from '../../../../types/payment_types';
import { BankPaymentMethodRepository } from '../../repositories/bank_payment_method.repository';
import {
  Backend,
  PaymentType,
  TransactionAttributes,
} from '../../../../models/transaction';

/**
 * This is a service class that is responsible for handling all the
 * payment method related operations
 */
export class PaymentMethodService {
  constructor(
    private readonly paymentMethodRepo: PaymentMethodRepository,
    private readonly bankPaymentMethodRepo: BankPaymentMethodRepository,
    private readonly cardPaymentMethodRepo: CardPaymentMethodRepository,
    private readonly logger: Logger
  ) {
    // Constructor
  }

  /**
   * Get the payment account ID from the transaction details
   * @param transaction - The transaction to get the payment method from.
   * @param walletAccount - The wallet account to get the payment method from.
   * @returns The payment method.
   */
  async getBackendAccountId(
    userId: string,
    transaction: CreateTransactionAttrs
  ): Promise<string> {
    if (transaction.bank_payment_method_id) {
      const method =
        await this.bankPaymentMethodRepo.getByUserAndPaymentMethodId(
          userId,
          transaction.bank_payment_method_id
        );

      assert(method?.backend_id, 'Bank payment method backend ID is required');

      return method?.backend_id;
    }

    if (transaction.card_payment_method_id) {
      const method =
        await this.cardPaymentMethodRepo.getByUserAndPaymentMethodId(
          userId,
          transaction.card_payment_method_id
        );

      assert(method?.backend_id, 'Card payment method backend ID is required');

      return method?.backend_id;
    }

    if (transaction.wallet_payment_method_id) {
      return transaction.wallet_payment_method_id;
    }

    this.logger.error(
      `Invalid payment method for transaction ${transaction.id}`
    );
    throw new Error('Invalid Payment Method');
  }

  /**
   * Select a payment method for an upfront holding payment. This type of
   * payment must be immediate (i.e. the receipt of funds must be immediate and not
   * delayed by bank transfer such as with bank payment).
   * @param userId
   * @param paymentMethodId
   * @returns
   */
  async selectUpfrontHoldingPaymentMethod(
    userId: string,
    paymentMethodId: string,
    finstroUpfrontEnabled: boolean
  ): Promise<{
    paymentMethod: PaymentMethodModel | undefined;
    cardPaymentMethod: CardPaymentMethodModel | undefined;
  }> {
    this.logger.info(
      { userId, paymentMethodId },
      'Selecting upfront payment method'
    );

    const cardPaymentMethod =
      await this.cardPaymentMethodRepo.getByUserAndPaymentMethodId(
        userId,
        paymentMethodId
      );

    if (cardPaymentMethod) {
      this.logger.info(
        { userId, paymentMethodId, backendId: cardPaymentMethod.backend_id },
        'Found credit card payment method for upfront holding payment'
      );

      return { paymentMethod: undefined, cardPaymentMethod };
    }

    if (!finstroUpfrontEnabled) {
      this.logger.error(
        { userId, paymentMethodId },
        'No payment method found for upfront holding payment for user & finstro is disabled'
      );

      throw Error('Payment method not found');
    }

    const paymentMethod = await this.paymentMethodRepo.getUserPaymentMethod(
      userId,
      paymentMethodId
    );

    if (!paymentMethod) {
      this.logger.error('No payment method found for user');
      throw Error('Payment method not found');
    }

    this.logger.info(
      { userId, paymentMethodId, backendId: paymentMethod.accountId },
      'Selected payment method'
    );

    return { paymentMethod, cardPaymentMethod: undefined };
  }

  /**
   * Validate the payment method for a transaction.
   * @param transaction - The transaction to validate the payment method for.
   * @returns The payment method.
   */
  async validatePaymentMethod(
    transaction: TransactionAttributes
  ): Promise<
    | { type: PaymentType; backend: Backend }
    | { type: 'unknown' | 'foodbomb'; backend?: Backend }
  > {
    // An accounting transaction does not require a payment method
    if (transaction.transactionType === 'accounting') {
      if (transaction.type === 'foodbomb') {
        return { type: 'foodbomb' };
      }

      return { type: 'unknown' };
    }

    const paymentMethodMissing =
      !transaction.paymentMethodId &&
      !transaction.wallet_payment_method_id &&
      !transaction.card_payment_method_id &&
      !transaction.bank_payment_method_id;

    if (paymentMethodMissing) {
      throw Error('Transaction must have an associated payment method');
    }

    if (transaction.bank_payment_method_id) {
      const method = await BankPaymentMethod.findByPk(
        transaction.bank_payment_method_id
      );

      if (!method) {
        throw Error('Transaction must have an associated payment method');
      }

      if (method.user_id !== transaction.buyer_id) {
        throw Error('Payment method must be owned by retailer');
      }

      return { type: 'direct', backend: method.backend as Backend };
    }

    if (transaction.card_payment_method_id) {
      const method = await CardPaymentMethod.findByPk(
        transaction.card_payment_method_id
      );
      if (!method) {
        throw Error('Transaction must have an associated payment method');
      }

      if (method.user_id !== transaction.buyer_id) {
        throw Error('Payment method must be owned by retailer');
      }

      return {
        type: method.issuer as PaymentType,
        backend: method.backend as Backend,
      };
    }

    if (transaction.paymentMethodId) {
      const paymentMethod = await PaymentMethod.findByPk(
        transaction.paymentMethodId
      );

      if (!paymentMethod) {
        throw Error('Transaction must have an associated payment method');
      }

      if (paymentMethod.accountId !== transaction.buyer_id) {
        throw Error('Payment method must be owned by retailer');
      }

      return { type: paymentMethod.type, backend: paymentMethod.backend };
    }

    return { type: 'wallet', backend: Backend.PROMISEPAY };
  }
}
