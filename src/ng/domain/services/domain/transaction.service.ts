import Logger from 'bunyan';
import * as Sequelize from 'sequelize';
import assert from 'node:assert';
import { CreateTransactionAttrs } from '../../../../types/payment_types';
import { TransactionsRepository } from '../../repositories/transactions.repository';
import {
  states,
  TransactionInstance,
  truncateDeclineReason,
} from '../../../../models/transaction';
import { calculateFee } from '../../../../actions/transaction_create_action';
import { validatePaymentMethod } from '../../../../controllers/transactions';
import { JournalTransaction } from '../../../../lib/journal_v2/journal_v2';
import { JournalName } from '../../../../models/journal_v2';
import { CurrencyCodes } from '../../../../types/currency_codes';
import steveo from '../../../../lib/steveo/steveo';
import { UserInstance } from '../../../../models/user';
import { zaiCallbackHandlers } from '../../../../utils/transaction_context_handlers';
import { calculateRelease } from '../../../../lib/calculations';
import { UserRepository } from '../../repositories/user.repository';

/**
 * This is a service class that is responsible for handling all the
 * transaction related operations and checks
 */
export class TransactionService {
  constructor(
    private readonly transactionsRepository: TransactionsRepository,
    private readonly userRepository: UserRepository,
    private readonly logger: Logger
  ) {
    // Constructor
  }

  /**
   * Idempotently get or create a transaction.
   * @param payload - Transaction creation payload
   * @returns The created transaction.
   */
  async getOrCreateTransaction(
    seller: UserInstance,
    payload: CreateTransactionAttrs,
    applyFee: 'apply-fee' | 'no-fee',
    txn?: Sequelize.Transaction
  ): Promise<TransactionInstance> {
    this.logger.trace({ transaction: payload }, 'Creating transaction');

    try {
      const { type } = await validatePaymentMethod(payload);
      const fee =
        applyFee === 'apply-fee'
          ? calculateFee(seller, payload.amount, type)
          : 0;
      const create = { ...payload, type, fee };

      this.logger.trace(
        { sellerId: create.seller_id, buyerId: create.buyer_id },
        `Holding funds using payment method ${type} for ${create.amount} cents and fee of ${create.fee}`
      );

      const [instance, created] =
        await this.transactionsRepository.upsertTransaction(create, txn);

      if (!created) {
        return instance;
      }

      // Create pending ledger entries between the seller's and buyer's wallet account
      const journal = new JournalTransaction(instance.id);
      await journal.credit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: create.seller_id,
        amount: create.amount,
      });

      await journal.debit({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: create.buyer_id,
        amount: create.amount,
      });

      await journal.commit(txn);

      this.logger.trace({ instance }, 'Transaction created');

      return instance;
    } catch (error) {
      this.logger.error(
        { error },
        'Error ensuring transaction exists in postgres'
      );
      if (txn) {
        await txn.rollback();
      }
      throw error;
    }
  }

  /**
   * Performs side effects for legacy workflows, particularly handling Zai async transactions.
   * Note: Do not run this in a database transaction, workflow assumes updates have been persisted to the database
   * TODO: Remove this once we move to upfront payment redesign
   * @deprecated Marking this deprecated to discourage usage.
   * @param transactionId - The ID of the transaction to process
   * @returns Promise<void>
   */
  async performSideEffectsForLegacyWorkflows({
    transactionId,
  }: {
    transactionId: string;
  }) {
    const transaction = await this.transactionsRepository.getTransactionById(
      transactionId
    );
    if (!transaction) {
      this.logger.warn(
        { transactionId },
        '[legacy] Tried to perform side effects for a transction that does not exist in the system'
      );
      return;
    }

    const { context } = transaction;

    if (!context) {
      this.logger.error(
        { transactionId },
        '[legacy] Missing context on the transaction'
      );
      return;
    }

    if (context.isZaiAsync && context.workflow) {
      this.logger.info(
        { transactionId, context },
        '[legacy] Initiating side effects for Zai async transaction'
      );

      try {
        await zaiCallbackHandlers[context.workflow](
          transaction,
          this.logger.child({ transactionId })
        );
      } catch (err) {
        // We swallow errors to mimic the behaviour of the old code
        this.logger.error(
          { err, transactionId },
          '[legacy] Error calling zai callback handler'
        );
      }
    }
  }

  /**
   * Update a transaction to success.
   * @param transactionId - The ID of the transaction to update.
   */
  async markCompleted({
    transactionId,
    receivedAt,
    releaseAt,
    txn,
    emitChangeEvent = true,
  }: {
    transactionId: string;
    receivedAt: string;
    releaseAt?: string;
    txn?: Sequelize.Transaction;
    emitChangeEvent?: boolean;
  }) {
    this.logger.trace(
      { transactionId, emitChangeEvent },
      'Updating transaction to success'
    );

    await this.transactionsRepository.updateTransaction(
      transactionId,
      {
        state: states.completed,
        received_at: receivedAt,
        release_at: releaseAt,
      },
      txn
    );

    const journal = new JournalTransaction(transactionId);

    await journal.markEntriesSettled(txn);

    if (emitChangeEvent) {
      await steveo.publish('transaction-changed', {
        transactionId,
      });
    }
  }

  /**
   * Update a transaction to failed.
   * @param transactionId - The ID of the transaction to update.
   * @param declineReason - The reason for the decline.
   */
  async markFailed({
    transactionId,
    declineReason,
    declineCode,
    txn,
    emitChangeEvent = true,
  }: {
    transactionId: string;
    declineReason: string;
    declineCode?: string;
    txn?: Sequelize.Transaction;
    emitChangeEvent?: boolean;
  }) {
    this.logger.trace(
      { transactionId, emitChangeEvent },
      'Updating transaction to failed'
    );

    await this.transactionsRepository.updateTransaction(
      transactionId,
      {
        state: states.failed,
        declineReason: truncateDeclineReason(declineReason),
        declineCode,
      },
      txn
    );

    const journal = new JournalTransaction(transactionId);

    await journal.markEntriesArchived();

    if (emitChangeEvent) {
      await steveo.publish('transaction-changed', {
        transactionId,
      });
    }
  }

  /**
   * Update a transaction to failed.
   * @param transactionId - The ID of the transaction to update.
   * @param declineReason - The reason for the decline.
   */
  async markRefunded({
    transactionId,
    refundedAt,
    refundAmountCents,
    txn,
    emitChangeEvent = true,
  }: {
    transactionId: string;
    refundedAt: string;
    refundAmountCents: number;
    txn?: Sequelize.Transaction;
    emitChangeEvent?: boolean;
  }) {
    this.logger.trace(
      { transactionId, emitChangeEvent },
      'Updating transaction to refunded'
    );

    await this.transactionsRepository.updateTransaction(
      transactionId,
      {
        state: states.refunded,
        refunded_at: refundedAt,
        refund_amount: refundAmountCents,
      },
      txn
    );

    const journal = new JournalTransaction(transactionId);

    await journal.markEntriesArchived();

    if (emitChangeEvent) {
      await steveo.publish('transaction-changed', {
        transactionId,
      });
    }
  }

  /**
   * Set the release timestamp for a transaction id to now
   * so the background job will release the funds to the seller, additionally
   * set the settlement amount must be set in order for the disbursement
   * query to release the funds to the seller.
   * @param args - The payload for the activity.
   */
  async transactionsReleaseNow(args: {
    transactionId: string;
    sellerId: string;
    settlementAmountCents: number;
    txn?: Sequelize.Transaction;
  }) {
    this.logger.info({ args }, 'Releasing transaction funds to seller');

    const seller = await this.userRepository.getUserById(args.sellerId);

    assert(seller, 'Seller not found');

    const receivedAt = new Date().toISOString();
    // This is a reference to legacy transaction creation and payment logic
    // TODO: Calculate release should be moved to a helper function
    const releaseAt = calculateRelease(seller, receivedAt, 0, true);

    await this.transactionsRepository.updateTransaction(
      args.transactionId,
      {
        // Update the amount to the settlement amount.
        // This is done regardless of whether there were refunds or topups (upfront)
        // as the total amount will be used as a true state of an invoice amount.
        amount: args.settlementAmountCents,
        settlementAmount: args.settlementAmountCents,
        received_at: receivedAt,
        release_at: releaseAt,
      },
      args.txn
    );
  }
}
