import Logger from 'bunyan';
import { BankPaymentMethodModel } from '../../../models/bank_payment_method';
import { BankPaymentMethod } from '../../../models';

/**
 * This is a repository class that is responsible for handling all the
 * database operations related to card payment methods
 */
export class BankPaymentMethodRepository {
  constructor(private readonly logger: Logger) {
    // Constructor
  }

  /**
   * Get a card payment method by user and payment method id
   * @param userId - The user id
   * @param paymentMethodId - The payment method id
   * @returns The card payment method
   */
  async getByUserAndPaymentMethodId(
    userId: string,
    paymentMethodId: string
  ): Promise<BankPaymentMethodModel | null> {
    this.logger.trace(
      { userId, paymentMethodId },
      'Getting card payment method by user and payment method id'
    );

    const bankPaymentMethod = await BankPaymentMethod.findOne({
      where: {
        id: paymentMethodId,
        user_id: userId,
      },
    });

    return bankPaymentMethod;
  }
}
