import Logger from 'bunyan';
import * as Sequelize from 'sequelize';
import { Transaction } from '../../../models';
import {
  TransactionAttributes,
  TransactionInstance,
} from '../../../models/transaction';
import { CreateTransactionAttrs } from '../../../types/payment_types';

/**
 * This is a repository class that is responsible for handling all the
 * database operations related to transactions
 */
export class TransactionsRepository {
  constructor(private readonly logger: Logger) {
    // Constructor
  }

  /**
   * Get a transaction by ID.
   * @param id - The ID of the transaction to get.
   * @returns The transaction.
   */
  async getTransactionById(
    id: string,
    txn?: Sequelize.Transaction,
    lock?: boolean
  ): Promise<TransactionInstance | null> {
    this.logger.trace({ id }, 'Getting transaction by id');

    return Transaction.findByPk(id, { transaction: txn, lock });
  }

  /**
   * Get transactions by IDs.
   * @param ids - The IDs of the transactions to get.
   * @returns The transactions.
   */
  getTransactionsByIds(ids: string[], txn?: Sequelize.Transaction) {
    this.logger.trace({ ids }, 'Getting transactions by ids');

    return Transaction.findAll({ where: { id: ids }, transaction: txn });
  }

  /**
   * Idempotently get or create a transaction.
   * @param payload - Transaction creation payload
   * @returns The created transaction.
   */
  async createTransaction(
    payload: CreateTransactionAttrs,
    txn?: Sequelize.Transaction
  ): Promise<TransactionInstance> {
    this.logger.trace({ transaction: payload }, 'Creating transaction');

    return Transaction.create(payload, { transaction: txn });
  }

  /**
   * Upsert a transaction.
   * @param payload - The transaction creation payload.
   * @returns The created transaction.
   */
  async upsertTransaction(
    payload: CreateTransactionAttrs,
    txn?: Sequelize.Transaction
  ): Promise<[TransactionInstance, boolean | null]> {
    this.logger.trace({ transaction: payload }, 'Upserting transaction');

    return Transaction.upsert(payload, { transaction: txn });
  }

  async updateTransaction(
    id: string,
    payload: Partial<TransactionAttributes>,
    txn?: Sequelize.Transaction
  ): Promise<[affectedCount: number]> {
    this.logger.trace({ transaction: payload }, 'Updating transaction');

    return Transaction.update(payload, {
      where: { id },
      transaction: txn,
    });
  }
}
