import Logger from 'bunyan';
import { Sequelize, Transaction } from 'sequelize';
import { User, UserSetting } from '../../../models';
import { UserAttributes, UserInstance } from '../../../models/user';
import { UserSettingInstance } from '../../../models/user_setting';

/**
 * This is a repository class that is responsible for handling all the
 * database operations related to users
 */
export class UserRepository {
  constructor(private readonly logger: Logger) {
    // Constructor
  }

  /**
   * Update a user in the database.
   * @param user - The user to update.
   */
  async updateUser(id: string, user: Partial<UserAttributes>): Promise<void> {
    this.logger.trace({ user }, 'Updating user');

    await User.update(user, {
      where: { id },
    });
  }

  /**
   * Gets a user by their id.
   * @param userId - The id of the user to get.
   * @returns The user.
   */
  getUserById(
    userId: string,
    txn?: Transaction
  ): Promise<(UserInstance & { userSetting?: UserSettingInstance }) | null> {
    this.logger.trace({ userId }, 'Getting user by id');

    return User.findByPk(userId, {
      transaction: txn,
      include: [
        {
          model: UserSetting,
          as: 'userSetting',
          required: false,
        },
      ],
    });
  }

  /**
   * Gets multiple users by their ids returned in the order of the input userIds.
   * @param userIds - The ids of the users to get.
   * @returns The users objects ordered by the input userIds.
   */
  async getUsersById<T extends 'optional' | 'required'>(
    userIds: string[],
    verify: T = 'required' as T
  ): Promise<
    T extends 'required' ? UserInstance[] : (UserInstance | undefined)[]
  > {
    this.logger.trace({ userIds }, 'Getting users by id');

    const users = await User.findAll({
      where: {
        id: userIds,
      },
      order: [
        [
          Sequelize.literal(
            `CASE id ${userIds
              .map((id, index) => `WHEN '${id}' THEN ${index}`)
              .join(' ')} END`
          ),
          'ASC',
        ],
      ],
    });

    if (verify === 'required' && users.length !== userIds.length) {
      this.logger.error({ userIds }, 'User(s) not found');

      throw new Error('User(s) not found');
    }

    return users as T extends 'required'
      ? UserInstance[]
      : (UserInstance | undefined)[];
  }
}
