{"extends": "../../tsconfig.json", "compilerOptions": {"module": "commonjs", "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": false, "strictNullChecks": false}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}