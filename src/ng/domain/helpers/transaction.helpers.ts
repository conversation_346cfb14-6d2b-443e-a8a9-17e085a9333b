import { states, TransactionStates } from '../../../models/transaction';

export const isTransactionPending = (state: states): boolean =>
  TransactionStates.pending.includes(state);

export const isTransactionFinalized = (state: states): boolean =>
  TransactionStates.finalized.includes(state);

export const isTransactionFailed = (state: states): boolean =>
  TransactionStates.failed.includes(state);

export const isTransactionPaid = (state: states): boolean =>
  TransactionStates.paid.includes(state);

export const isTransactionCompleted = (state: states): boolean =>
  TransactionStates.completed.includes(state);
