import { TransactionType } from '../../../models/transaction';
import { CurrencyCodes } from '../../../types/currency_codes';

export type WalletTransferParams = {
  description: string;
  amountCents: number;
  recipientId: string;
  recipientText: string;
  senderId: string;
  senderText: string;
  currencyCode?: CurrencyCodes;
  orderIds: string[];
  invoiceIds: string[];
  transactionId: string;
  transactionType: TransactionType;
  relatedTransactions: string[];
};
