import { UpfrontFlowStep } from '../../../types/upfront.types';

/**
 * The current state of the upfront payment workflow.
 */
export type UpfrontPaymentState = {
  /**
   * The transaction ID for the holding amount, the amount taken
   * when the upfront order was placed.
   */
  holdingTransactionId: string;

  /**
   * The topup transaction is when the upfront order is finalised and
   * an amount needs to be taken to make up the difference on the final amount.
   */
  topUpTransactionId: string;

  /**
   * The total amount in cents held when the upfront order was placed.
   */
  holdingAmountCents: number;

  /**
   * The current step in the upfront payment workflow.
   */
  flowStep: UpfrontFlowStep;
};

export type UpfrontWorkflowResult =
  | {
      holdingTransactionId: string;
      finalisation: 'cancelled';
      refundAmountCents: number;
      reason?: string;
    }
  | {
      holdingTransactionId: string;
      finalisation: 'exact';
    }
  | {
      holdingTransactionId: string;
      finalisation: 'topup';
      topUpTransactionId: string;
      topUpAmountCents: number;
    }
  | {
      holdingTransactionId: string;
      finalisation: 'refund';
      refundAmountCents: number;
    };
