import { defineWorkflow } from '@ordermentum/temporal/workflow';
import { log, proxyActivities } from '@temporalio/workflow';
import { getActivityRetryPolicy } from '../../common/helpers/temporal.helpers';
import { WorkflowActivities } from '../activities.factory';

const activities = proxyActivities<WorkflowActivities>({
  startToCloseTimeout: '2 minutes',
  retry: {
    ...getActivityRetryPolicy(),
    maximumAttempts: 10,
    initialInterval: '10s',
    backoffCoefficient: 6,
  },
});

export type RefundWorkflowInput = {
  transactionId: string;
  refundAmountCents: number;
};

/**
 * This workflow is used to refund an upfront payment transaction.
 * - Either we receive a cancellation request and the upfront payment is refunded.
 * - Or the value of the order is reduced and the partial payment is refunded.
 *
 * LIMITATIONS:
 * - This only works for Zai transactions
 * - Needs a transaction state check against database and the provider
 */
export async function refundWorkflow(args: RefundWorkflowInput) {
  log.info('Refunding transaction', { args });
  const result = await activities.refundTransaction({
    transactionId: args.transactionId,
    refundAmountCents: args.refundAmountCents,
  });

  return result;
}

export const refundWorkflowDefinition = defineWorkflow({
  workflow: refundWorkflow,
  queueName: 'payments:generic',
  generateWorkflowId: (transactionId: string) =>
    `refund-transaction-${transactionId}`,
  queries: {},
  signals: {},
  updates: {},
  path: __filename,
});
