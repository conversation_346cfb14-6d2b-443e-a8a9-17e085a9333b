import { proxyActivities, startChild } from '@temporalio/workflow';
import { defineWorkflow } from '@ordermentum/temporal/workflow';
import type { WorkflowActivities } from '../activities.factory';
import { getActivityRetryPolicy } from '../../common/helpers/temporal.helpers';
import {
  getPromoSucceededArgs,
  getPromoTakePaymentArgs,
  getPromoWalletFundingArgs,
  getPromoWalletReversalArgs,
} from '../factories/promotional_payment.factory';
import { PromotionalPaymentWorkflowArgs } from '../types/promotional_payment.types';
import { walletTransferWorkflowDefinition } from './wallet_transfer.workflow';

export const DEFAULT_START_TO_CLOSE_TIMEOUT = '60 seconds';

/**
 * The activities for the promotional payment workflow.
 */
const activities = proxyActivities<WorkflowActivities>({
  startToCloseTimeout: DEFAULT_START_TO_CLOSE_TIMEOUT,
  retry: getActivityRetryPolicy(),
});

/**
 * This workflow is responsible for handling the payment for a promotional payment.
 * The promotional payment workflow works by:
 *
 * 1. Ensuring the principle promo wallet has sufficient funds
 * 2. Transfer the promo amount from the principle promo funding wallet to the seller's wallet
 * 3. Take payment from the venue to the supplier's wallet
 * 4. Mark the transactions as succeeded & call back to the OM service
 * 5. If the workflow fails, return the promo funds & mark the transactions as failed & call back to the OM service
 *
 * Original requirements: https://ordermentum.atlassian.net/browse/C1-1887
 */
export async function promotionalPaymentWorkflow(
  args: PromotionalPaymentWorkflowArgs
) {
  let promoFundsTransferred = false;

  try {
    // Step 1: Make sure there is enough money in the promotional wallet to cover the promo amount.
    await activities.walletCheckBalance({
      walletId: args.fundingWalletId,
      requiredAmount: args.promoAmountCents,
    });

    // Step 2: Move the funds from the promotional wallet to the seller's wallet.
    // We only do this if the promotional wallet has sufficient funds.
    await activities.walletTransfer(getPromoWalletFundingArgs(args));
    promoFundsTransferred = true;

    // Step 3: Take payment from the venue to the supplier's wallet
    await activities.paymentMethodTransfer(getPromoTakePaymentArgs(args));

    // Step 4: Mark the transactions as succeeded & call back to the OM service
    await activities.promoSucceeded(getPromoSucceededArgs(args));
  } catch (err) {
    // Error handling step 1: Return the promo funds if they were transferred
    if (promoFundsTransferred) {
      const workflowId = walletTransferWorkflowDefinition.generateWorkflowId(
        args.promoTransactionId
      );

      // Start the refund workflow without waiting for it to complete
      await startChild(walletTransferWorkflowDefinition.workflow, {
        workflowId,
        args: [getPromoWalletReversalArgs(args)],
        parentClosePolicy: 'ABANDON',
      });
    }

    // Error handling step 2: Mark the transactions as failed & call back to the OM service
    await activities.promoFailed(args);

    throw err;
  }
}

/**
 *
 */
export const promotionalPaymentWorkflowDefinition = defineWorkflow({
  workflow: promotionalPaymentWorkflow,
  queueName: 'payments:promotional-payments',
  generateWorkflowId: (identifier: string) =>
    `promotional-payment-${identifier}`,
  queries: {},
  signals: {},
  updates: {},
  path: __filename,
});
