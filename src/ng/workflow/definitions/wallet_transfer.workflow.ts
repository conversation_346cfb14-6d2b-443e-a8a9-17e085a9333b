import { defineWorkflow } from '@ordermentum/temporal/workflow';
import { proxyActivities } from '@temporalio/workflow';
import { getActivityRetryPolicy } from '../../common/helpers/temporal.helpers';
import { WorkflowActivities } from '../activities.factory';
import { WalletTransferParams } from '../types/wallet_transfer.types';

const activities = proxyActivities<WorkflowActivities>({
  startToCloseTimeout: '2 minutes',
  retry: {
    ...getActivityRetryPolicy(),
    initialInterval: '20s',
    backoffCoefficient: 5,
    maximumAttempts: 10,
  },
});

/**
 * This workflow is used to transfer funds from one wallet to another.
 * This is typically used as a child workflow when the parent needs to
 * ensure funds are transferred, but the result should not affect the parent.
 */
export async function walletTransferWorkflow(args: WalletTransferParams) {
  const result = await activities.walletTransfer(args);

  return result;
}

export const walletTransferWorkflowDefinition = defineWorkflow({
  workflow: walletTransferWorkflow,
  queueName: 'payments:wallet-transfer',
  generateWorkflowId: (identifier: string) => `wallet-transfer-${identifier}`,
  queries: {},
  signals: {},
  updates: {},
  path: __filename,
});
