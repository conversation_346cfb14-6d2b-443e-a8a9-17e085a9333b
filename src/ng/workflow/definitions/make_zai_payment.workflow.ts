import { defineWorkflow } from '@ordermentum/temporal/workflow';
import { log, proxyActivities, ApplicationFailure } from '@temporalio/workflow';
import { AccountIdRequestBody } from 'zai-payments';
import { getActivityRetryPolicy } from '../../common/helpers/temporal.helpers';
import { createActivities } from '../activities.factory';

const activities = proxyActivities<ReturnType<typeof createActivities>>({
  startToCloseTimeout: '2 minutes',
  retry: getActivityRetryPolicy(),
});

export type MakeZaiPaymentWorkflowInput = {
  transactionId: string;
  body: AccountIdRequestBody;
  /**
   * Release policy for disbursing funds to the seller.
   * If the policy is immediate, the funds will be released immediately.
   * If the policy is delayed, the funds will be released after 72 hours.
   *
   * @see src/actions/transaction_create_action.ts#addPaymentReleaseTimestamps
   */
  releasePolicy: 'immediate' | 'delayed';

  /**
   * Timestamp when the workflow initiation started (for measuring startup time)
   */
  workflowInitiationTime?: number;
};

export async function makeZaiPaymentWorkflow({
  transactionId,
  body,
  releasePolicy,
  workflowInitiationTime,
}: MakeZaiPaymentWorkflowInput) {
  const workflowStartTime = Date.now();

  log.info('Make Zai Payment Workflow Started', {
    transactionId,
    body,
    releasePolicy,
    startTime: workflowStartTime,
    initiationTime: workflowInitiationTime || 'unknown',
    startupLagMs: workflowInitiationTime
      ? workflowStartTime - workflowInitiationTime
      : 'unknown',
  });

  if (!body.account_id.trim() || !body.merchant_phone?.trim()) {
    log.error('Missing account_id or merchant_phone');

    throw ApplicationFailure.nonRetryable(
      'Missing required fields: account_id or merchant_phone'
    );
  }

  const result = await activities.makePaymentOnTransaction(
    transactionId,
    body.account_id,
    body.merchant_phone
  );

  log.info(result.success ? 'Payment successful' : 'Payment failed', {
    transactionId,
    result,
    workflow: 'make-zai-payment',
  });

  await activities.finalizeTransaction(transactionId, result, releasePolicy);

  let message: string;
  if (result.success) {
    message = 'Payment completed successfully';
  } else {
    switch (result.action) {
      case 'sync_state':
        message = 'Transaction state was out of sync and has been corrected';
        break;
      case 'payment_failed':
        message = `Payment failed: ${result.reason}`;
        break;
      case 'no_action_needed':
        message = `No payment action required: ${result.reason}`;
        break;
      default:
        message = `Payment processing completed: ${result.reason}`;
    }
  }

  log.info('Make Zai Payment Workflow Completed', {
    transactionId,
    success: result.success,
    action: result.action,
    duration: Date.now() - workflowStartTime,
    startupLagMs: workflowInitiationTime
      ? workflowStartTime - workflowInitiationTime
      : 'unknown',
    message,
  });

  return {
    success: result.success,
    message,
    reason: result.reason,
    action: result.action,
    transactionId,
  };
}

export const makeZaiPaymentWorkflowDefinition = defineWorkflow({
  workflow: makeZaiPaymentWorkflow,
  queueName: 'payments:make-payment',
  generateWorkflowId: (identifier: string) => `make-zai-payment-${identifier}`,
  signals: {},
  queries: {},
  updates: {},
  path: __filename,
});
