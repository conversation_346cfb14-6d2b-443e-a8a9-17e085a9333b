import {
  defineSignal,
  defineQuery,
  log,
  proxyActivities,
  set<PERSON><PERSON><PERSON>,
  uuid4,
  WorkflowError,
  executeChild,
} from '@temporalio/workflow';
import { defineWorkflow } from '@ordermentum/temporal/workflow';
import {
  UpfrontPayment,
  UpfrontPaymentComplete,
} from '../../common/requests/upfront_payments.request';
import type { createActivities } from '../activities.factory';
import {
  getActivityRetryPolicy,
  waitForSignal,
} from '../../common/helpers/temporal.helpers';
import { UpfrontFlowStep } from '../../../types/upfront.types';
import {
  createHoldingPaymentArgs,
  createTopUpPaymentArgs,
} from '../factories/upfront.factory';
import {
  UpfrontPaymentState,
  UpfrontWorkflowResult,
} from '../types/upfront.types';
import { refundWorkflowDefinition } from './refund.workflow';

const queries = {
  getState: defineQuery<UpfrontPaymentState>('getState'),
};
export const DEFAULT_START_TO_CLOSE_TIMEOUT = '60 seconds';

type FinalisedSignal = UpfrontPaymentComplete & {
  differenceCents: number;
  type: 'finalised';
};

type CancelledSignal = {
  reason?: string;
  type: 'cancelled';
};

const signals = {
  finalised: defineSignal<[FinalisedSignal]>('upfront-payment:finalised'),
  cancelled: defineSignal<[CancelledSignal]>('upfront-payment:cancelled'),
};

const activities = proxyActivities<ReturnType<typeof createActivities>>({
  startToCloseTimeout: DEFAULT_START_TO_CLOSE_TIMEOUT,
  retry: getActivityRetryPolicy(),
});

async function startChildRefundWorkflow(
  payload: Parameters<typeof refundWorkflowDefinition.workflow>[0],
  searchAttributes?: string
) {
  const workflowId = refundWorkflowDefinition.generateWorkflowId(
    payload.transactionId
  );

  log.info('Starting refund workflow', {
    workflowId,
    transactionId: payload.transactionId,
  });

  return executeChild(refundWorkflowDefinition.workflow, {
    workflowId,
    args: [
      {
        transactionId: payload.transactionId,
        refundAmountCents: payload.refundAmountCents,
      },
    ],
    searchAttributes: searchAttributes
      ? {
          CustomKeywordField: [searchAttributes],
        }
      : undefined,
  });
}

/**
 * Determines the settlement type based on the payment difference
 */
function getSettlementType(
  differenceCents: number
): 'topup' | 'refund' | 'exact' {
  if (differenceCents > 0) return 'topup';
  if (differenceCents < 0) return 'refund';
  return 'exact';
}

/**
 * Executes the initial holding payment phase
 */
async function executeHoldingPayment(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState
): Promise<void> {
  log.info('Executing holding payment', { currentState });

  try {
    await activities.paymentMethodTransfer(
      createHoldingPaymentArgs(upfront, currentState)
    );

    await activities.upfrontHoldingTransactionSucceeded({
      holdingTransactionId: currentState.holdingTransactionId,
      upfront,
    });
  } catch (error) {
    await activities.upfrontHoldingTransactionFailed({
      holdingTransactionId: currentState.holdingTransactionId,
      upfront,
      error: error as Error,
    });
    throw error;
  }
}

/**
 * Waits for and validates the finalise or cancellation signal
 */
async function waitForFinaliseSignal(): Promise<
  FinalisedSignal | CancelledSignal
> {
  log.info('Waiting for finalise or cancellation signal');

  const result = await Promise.race([
    waitForSignal<FinalisedSignal>(signals.finalised.name),
    waitForSignal<CancelledSignal>(signals.cancelled.name),
  ]);

  if (!result) {
    throw new WorkflowError('Upfront payment signal arguments are undefined');
  }

  return result;
}

/**
 * Handles top-up payments when final amount exceeds holding amount
 */
async function handleTopUpSettlement(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState,
  finalised: FinalisedSignal
): Promise<void> {
  log.info('Making top up payment', { currentState, finalised });

  try {
    // Execute top-up payment
    await activities.paymentMethodTransfer(
      createTopUpPaymentArgs(
        upfront,
        currentState,
        finalised.differenceCents,
        finalised.description
      )
    );

    await activities.upfrontFinaliseSucceeded(upfront, currentState);

    // Release both holding and top-up amounts to seller
    await Promise.all([
      activities.transactionsReleaseNow({
        transactionId: currentState.holdingTransactionId,
        settlementAmountCents: upfront.amountCents,
        sellerId: upfront.sellerUserId,
      }),
      activities.transactionsReleaseNow({
        transactionId: currentState.topUpTransactionId,
        settlementAmountCents: Math.abs(finalised.differenceCents),
        sellerId: upfront.sellerUserId,
      }),
    ]);
  } catch (error) {
    await activities.upfrontFinaliseFailed(upfront, currentState, error);
    throw error;
  }
}

/**
 * Handles partial refunds when final amount is less than holding amount
 */
async function handleRefundSettlement(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState,
  finalised: FinalisedSignal
): Promise<void> {
  log.info('Making partial refund to the buyer', { currentState, finalised });

  await activities.upfrontFinaliseSucceeded(upfront, currentState);

  const refundAmountCents = Math.abs(finalised.differenceCents);
  const settlementAmountCents = upfront.amountCents - refundAmountCents;

  // Release reduced amount to seller
  await activities.transactionsReleaseNow({
    transactionId: currentState.holdingTransactionId,
    settlementAmountCents,
    sellerId: upfront.sellerUserId,
  });

  await startChildRefundWorkflow(
    {
      transactionId: currentState.holdingTransactionId,
      refundAmountCents,
    },
    upfront.orderId
  );
}

/**
 * Handles exact amount settlements (no top-up or refund needed)
 */
async function handleExactSettlement(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState
): Promise<void> {
  log.info('No top up or refund required', { currentState });

  await activities.upfrontFinaliseSucceeded(upfront, currentState);

  await activities.transactionsReleaseNow({
    transactionId: currentState.holdingTransactionId,
    settlementAmountCents: upfront.amountCents,
    sellerId: upfront.sellerUserId,
  });
}

/**
 * Handles cancellation by refunding the holding transaction
 */
async function handleCancellation(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState,
  reason?: string
): Promise<UpfrontWorkflowResult> {
  log.info('Handling upfront payment cancellation', { currentState, reason });

  await startChildRefundWorkflow(
    {
      transactionId: currentState.holdingTransactionId,
      refundAmountCents: upfront.amountCents,
    },
    upfront.orderId
  );

  await activities.upfrontFinaliseCancelled(currentState);

  return {
    holdingTransactionId: currentState.holdingTransactionId,
    finalisation: 'cancelled',
    refundAmountCents: upfront.amountCents,
    reason,
  };
}

/**
 * Orchestrates the final settlement based on payment difference
 */
async function executeFinalSettlement(
  upfront: UpfrontPayment,
  currentState: UpfrontPaymentState,
  finalised: FinalisedSignal
): Promise<UpfrontWorkflowResult> {
  const settlementType = getSettlementType(finalised.differenceCents);

  switch (settlementType) {
    case 'topup':
      await handleTopUpSettlement(upfront, currentState, finalised);
      return {
        holdingTransactionId: currentState.holdingTransactionId,
        finalisation: 'topup',
        topUpTransactionId: currentState.topUpTransactionId,
        topUpAmountCents: Math.abs(finalised.differenceCents),
      };
    case 'refund':
      await handleRefundSettlement(upfront, currentState, finalised);
      return {
        holdingTransactionId: currentState.holdingTransactionId,
        finalisation: 'refund',
        refundAmountCents: Math.abs(finalised.differenceCents),
      };
    case 'exact':
      await handleExactSettlement(upfront, currentState);
      return {
        holdingTransactionId: currentState.holdingTransactionId,
        finalisation: 'exact',
      };
    default:
      throw new WorkflowError(`Unknown settlement type: ${settlementType}`);
  }
}

/**
 * The upfront payment workflow will move money from the buyer to the seller
 * where it takes money upfront and then settles the remainder of the transaction
 * after the upfront payment is finalised.
 *
 * @param upfront - The details of the upfront payment
 * @param holdingTransactionId - The transaction ID for the holding payment
 */
export async function upfrontPaymentWorkflow(
  upfront: UpfrontPayment,
  holdingTransactionId: string
): Promise<UpfrontWorkflowResult> {
  const currentState: UpfrontPaymentState = {
    holdingTransactionId,
    topUpTransactionId: uuid4(),
    holdingAmountCents: upfront.amountCents,
    flowStep: UpfrontFlowStep.WaitingForHoldingPayment,
  };

  setHandler(queries.getState, () => currentState);

  log.info('Starting upfront payment workflow', { currentState });

  try {
    // Phase 1: Initialize workflow
    await activities.upfrontStart(upfront);

    // Phase 2: Execute holding payment
    await executeHoldingPayment(upfront, currentState);

    currentState.flowStep = UpfrontFlowStep.WaitingForFinaliseSignal;

    // Phase 3: Wait for finalisation or cancellation signal
    const signalResult = await waitForFinaliseSignal();

    let result: UpfrontWorkflowResult;

    if (signalResult.type === 'cancelled') {
      // Phase 4: Handle cancellation
      currentState.flowStep = UpfrontFlowStep.Cancelled;
      result = await handleCancellation(
        upfront,
        currentState,
        signalResult.reason
      );
    } else {
      // Phase 4: Handle finalisation
      currentState.flowStep = UpfrontFlowStep.WaitingForFinalisePayment;

      result = await executeFinalSettlement(
        upfront,
        currentState,
        signalResult
      );
    }

    return result;
  } catch (error) {
    log.error('Upfront payment workflow failed', { error });
    throw error;
  } finally {
    if (currentState.flowStep !== UpfrontFlowStep.Cancelled) {
      currentState.flowStep = UpfrontFlowStep.Finished;
    }
  }
}

export const upfrontPaymentWorkflowDefinition = defineWorkflow({
  workflow: upfrontPaymentWorkflow,
  queueName: 'payments:upfront-payment',
  generateWorkflowId: (orderId: string) => `upfront-payment-order-${orderId}`,
  queries,
  signals,
  updates: {},
  path: __filename,
});
