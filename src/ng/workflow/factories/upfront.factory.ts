import { UpfrontPayment } from '../../common/requests/upfront_payments.request';
import { UpfrontPaymentState } from '../types/upfront.types';

export function createHoldingPaymentArgs(
  upfront: UpfrontPayment,
  state: UpfrontPaymentState
) {
  return {
    amountCents: upfront.amountCents,
    buyerId: upfront.buyerUserId,
    buyerText: upfront.description,
    description: upfront.description,
    orderIds: [upfront.orderId],
    invoiceIds: [upfront.invoiceId],
    paymentMethodId: upfront.paymentMethodId,
    sellerId: upfront.sellerUserId,
    sellerText: upfront.description,
    transactionId: state.holdingTransactionId,
    relatedTransactions: [],
    context: {
      // Store the original payment amount to preserve it when the holding
      // transaction amount is reduced due to order value decreases
      originalPayInAmountCents: upfront.amountCents,
    },
  };
}

export function createTopUpPaymentArgs(
  upfront: UpfrontPayment,
  state: UpfrontPaymentState,
  topupAmountCents: number,
  description: string
) {
  return {
    amountCents: topupAmountCents,
    buyerId: upfront.buyerUserId,
    buyerText: description,
    description: upfront.description,
    orderIds: [upfront.orderId],
    invoiceIds: [upfront.invoiceId],
    paymentMethodId: upfront.paymentMethodId,
    sellerId: upfront.sellerUserId,
    sellerText: description,
    transactionId: state.topUpTransactionId,
    relatedTransactions: [state.holdingTransactionId],
    context: {
      originalPayInTransactionId: state.holdingTransactionId,
      originalPayInAmountCents: upfront.amountCents,
      topUpAmountCents: topupAmountCents,
    },
  };
}
