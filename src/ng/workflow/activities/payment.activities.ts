import Logger from 'bunyan';
import { ApplicationFailure } from '@temporalio/common';
import {
  Error as SequelizeError,
  Transaction as SequelizeTransaction,
} from 'sequelize';
import { Item } from 'zai-payments';
import { TransactionsRepository } from '../../domain/repositories/transactions.repository';
import { UserRepository } from '../../domain/repositories/user.repository';
import { ZaiFundTransferGateway } from '../../domain/services/gateways/zai_fund_transfer.gateway';
import { states, TransactionModel } from '../../../models/transaction';
import { sequelize } from '../../../models';
import { TransactionService } from '../../domain/services/domain/transaction.service';
import { MakeZaiPaymentWorkflowInput } from '../definitions/make_zai_payment.workflow';
import { isRetryableError } from '../../common/helpers/errors.helpers';
import { getProviderErrorDetails } from '../../../lib/backends/promisepay/errors';
import {
  isTransactionCompleted,
  isTransactionFailed,
  isTransactionPending,
  isTransactionFinalized,
} from '../../domain/helpers/transaction.helpers';
import { calculateRelease } from '../../../lib/calculations';

export type MakePaymentActivityInput = {
  paymentMethodId: string;
  sellerMobileNumber: string;
  transaction: TransactionModel;
  log: Logger;
};

export type MakePaymentResult = {
  success: boolean;
  reason: string;
} & ( // Action to be taken after payment is attempted
  | {
      action: 'payment_success' | 'no_action_needed'; // payment_success: payment completed successfully, no_action_needed: no payment processing required
    }
  | {
      action: 'sync_state'; // sync_state: transaction state was out of sync and has been corrected
      state: states;
    }
  | {
      action: 'payment_failed'; // payment_failed: payment attempt failed with error
      code: string;
    }
);

/**
 * Create activities for making payments with comprehensive duplicate charge protection.
 */
export function createPaymentActivities(
  transactionsRepository: TransactionsRepository,
  transactionService: TransactionService,
  userRepository: UserRepository,
  zaiFundTransferGateway: ZaiFundTransferGateway,
  logger: Logger
) {
  /**
   * Atomically attempt to lock transaction for payment processing
   */
  async function lockTransactionForPayment(
    transactionId: string,
    log: Logger,
    txn: SequelizeTransaction
  ) {
    log.info('Attempting to lock transaction for payment');
    try {
      const transaction = await transactionsRepository.getTransactionById(
        transactionId,
        txn,
        true
      );

      return transaction;
    } catch (error) {
      throw ApplicationFailure.fromError(error, { nonRetryable: false });
    }
  }

  async function performPayment(
    input: MakePaymentActivityInput
  ): Promise<MakePaymentResult> {
    const { transaction, sellerMobileNumber, paymentMethodId, log } = input;

    log.info(
      { transaction, sellerMobileNumber, paymentMethodId },
      'Performing payment'
    );

    try {
      await zaiFundTransferGateway.makePayment(
        transaction.id,
        paymentMethodId,
        sellerMobileNumber
      );
      return {
        success: true,
        reason: 'Payment successful',
        action: 'payment_success',
      };
    } catch (error) {
      log.error({ error }, 'Performing payment failed');
      if (isRetryableError(error as Error)) {
        throw ApplicationFailure.fromError(error, { nonRetryable: false });
      }
      const { code, message, statusCode } = getProviderErrorDetails(error);
      log.error({ code, message, statusCode }, 'Error details from provider');
      return {
        success: false,
        reason: message ?? (error as Error).message,
        action: 'payment_failed',
        code,
      };
    }
  }

  return {
    /**
     * Makes a payment on a transaction.
     *
     * This function performs the following steps:
     * 1. Locks the transaction for payment processing.
     * 2. Checks if the transaction is in a state that allows payment.
     * 3. Gets the gateway item for the transaction.
     * 4. Checks if the transaction is finalized in the gateway.
     * 5. Performs the payment.
     *
     * @param transactionId - The ID of the transaction to make a payment on.
     * @param paymentMethodId - The ID of the payment method to use for the payment.
     * @param sellerMobileNumber - The mobile number of the seller.
     * @returns A promise that resolves with the payment result.
     */
    async makePaymentOnTransaction(
      transactionId: string,
      paymentMethodId: string,
      sellerMobileNumber: string
    ): Promise<MakePaymentResult> {
      const log = logger.child({ transactionId });
      log.info('Making payment on transaction');

      try {
        return await sequelize.transaction(async txn => {
          // Step 1: Lock and validate transaction
          const transaction = await lockTransactionForPayment(
            transactionId,
            log,
            txn
          );

          if (!transaction) {
            log.info('Transaction not found');
            return {
              success: false,
              reason: 'Transaction not found',
              action: 'no_action_needed',
            };
          }

          // Step 2: Check if transaction is in a state that allows payment
          if (!isTransactionPending(transaction.state)) {
            log.info(
              {
                currentState: transaction.state,
              },
              'Transaction in non-payable state'
            );
            return {
              success: false,
              reason: `Transaction in non-payable state, current state: ${transaction.state}`,
              action: 'no_action_needed',
            };
          }

          // Step 3: Get gateway item
          let gatewayItem: Item | undefined;
          try {
            gatewayItem = await zaiFundTransferGateway.getItem(transactionId);
          } catch (error) {
            log.error(error);
            if (isRetryableError(error as Error)) {
              throw ApplicationFailure.fromError(error, {
                nonRetryable: false,
              });
            }
            return {
              success: false,
              reason: 'Failed to get gateway item',
              action: 'sync_state',
              state: states.failed,
            };
          }

          const gatewayItemState = gatewayItem?.state;

          log.info({ gatewayItemState }, 'Gateway item state');

          if (!gatewayItemState) {
            const message = 'Gateway item not found for transaction';
            log.info(message);
            return {
              success: false,
              reason: message,
              action: 'sync_state',
              state: states.failed,
            };
          }

          // Step 4: Check if transaction is finalized in gateway
          if (isTransactionFinalized(gatewayItemState as states)) {
            const message = 'Transaction finalized in gateway';
            log.info({ gatewayItemState }, message);

            return {
              success: true,
              reason: message,
              action: 'sync_state',
              state: gatewayItemState as states,
            };
          }

          // Step 5: Perform payment
          const paymentResult = await performPayment({
            paymentMethodId,
            sellerMobileNumber,
            transaction,
            log,
          });

          return paymentResult;
        });
      } catch (error) {
        log.error(error, 'Error making payment');

        if (error instanceof ApplicationFailure) {
          throw error;
        }

        throw ApplicationFailure.fromError(error, {
          nonRetryable: !(error instanceof SequelizeError),
        });
      }
    },

    /**
     * Finalizes a transaction by updating its state based on the payment result.
     *
     * This function handles the following scenarios:
     * 1. If the payment result indicates no action is needed, it skips finalization.
     * 2. If the payment result indicates a sync state, it updates the transaction's state.
     * 3. If the payment result indicates a payment success, it marks the transaction as completed.
     * 4. If the payment result indicates a payment failure, it marks the transaction as failed.
     *
     * @param transactionId - The ID of the transaction to finalize.
     * @param paymentResult - The result of the payment.
     * @param releasePolicy - The release policy for the transaction.
     * @returns A promise that resolves when the transaction is finalized.
     */
    async finalizeTransaction(
      transactionId: string,
      paymentResult: MakePaymentResult,
      releasePolicy: MakeZaiPaymentWorkflowInput['releasePolicy']
    ): Promise<void> {
      const log = logger.child({ transactionId, paymentResult, releasePolicy });
      const immediateRelease = releasePolicy === 'immediate';

      if (paymentResult.action === 'no_action_needed') {
        log.info('Skipping finalization -- no action needed');
        return;
      }

      log.info('Finalizing transaction');

      try {
        await sequelize.transaction(async txn => {
          const transaction = await transactionsRepository.getTransactionById(
            transactionId,
            txn
          );

          if (!transaction) {
            log.info('Transaction not found');
            return;
          }

          const seller = await userRepository.getUserById(
            transaction.seller_id,
            txn
          );

          if (!seller) {
            log.info({ sellerId: transaction.seller_id }, 'Seller not found');
            return;
          }

          const receivedAt = new Date().toISOString();
          // This is a reference to legacy transaction creation and payment logic
          // TODO: Calculate release should be moved to a helper function
          const releaseAt = calculateRelease(
            seller,
            receivedAt,
            immediateRelease ? 0 : 72,
            immediateRelease
          );

          log.info(
            { receivedAt, releaseAt },
            'Release date calculated for transaction'
          );

          // Determine the final state and handle accordingly
          let finalState: states | undefined;
          let shouldMarkCompleted = false;
          let shouldMarkFailed = false;
          let declineReason: string | undefined;
          let declineCode: string | undefined;

          switch (paymentResult.action) {
            case 'sync_state':
              finalState = paymentResult.state;
              shouldMarkCompleted = isTransactionCompleted(finalState);
              shouldMarkFailed = isTransactionFailed(finalState);
              if (shouldMarkFailed) {
                declineReason = paymentResult.reason;
              }
              break;

            case 'payment_success':
              shouldMarkCompleted = true;
              break;

            case 'payment_failed':
              shouldMarkFailed = true;
              declineReason = paymentResult.reason;
              declineCode = paymentResult.code;
              break;

            default:
              // This should never happen as we've already filtered out 'no_action_needed'
              log.warn(
                { action: paymentResult.action },
                'Unexpected payment result action'
              );
              break;
          }

          if (shouldMarkCompleted) {
            await transactionService.markCompleted({
              transactionId,
              receivedAt,
              releaseAt,
              txn,
            });
          }

          if (shouldMarkFailed) {
            await transactionService.markFailed({
              transactionId,
              declineReason: declineReason!,
              declineCode,
              txn,
            });
          }

          if (finalState && paymentResult.action === 'sync_state') {
            await transactionsRepository.updateTransaction(
              transactionId,
              { state: finalState },
              txn
            );
          }
        });

        // TODO: Remove this once we move to upfront payment redesign
        await transactionService.performSideEffectsForLegacyWorkflows({
          transactionId,
        });
      } catch (error) {
        log.error(error);

        if (error instanceof ApplicationFailure) {
          throw error;
        }

        throw ApplicationFailure.fromError(error, {
          nonRetryable: !(error instanceof SequelizeError),
        });
      }
    },
  };
}
