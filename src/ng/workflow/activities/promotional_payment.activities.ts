import { sequelize } from '../../../models';
import { TransactionService } from '../../domain/services/domain/transaction.service';
import { omClient } from '../../../lib/om_client';
import {
  PromoSucceededArgs,
  PromotionalPaymentWorkflowArgs,
} from '../types/promotional_payment.types';
import { increment } from '../../../lib/stats';
import { toWorkflowError } from '../../common/helpers/errors.helpers';

/**
 * This is a collection of activities that are responsible for handling sponsored
 * @param zaiWalletsGateway - The Zai wallets gateway.
 * @returns
 */
export function createPromoActivities(transactionService: TransactionService) {
  return {
    /**
     * Handle the promotional payment complete success condition
     * Does not emit a change event
     *
     * @param payload - The promotional payment complete payload.
     */
    async promoSucceeded(args: PromoSucceededArgs) {
      try {
        await sequelize.transaction(async txn => {
          // Mark the transactions as released so the background job will release the
          // funds to the seller/recipient.
          // Release the PURCHASE funds
          await transactionService.transactionsReleaseNow({
            transactionId: args.purchaseTransactionId,
            sellerId: args.disbursementRecipientId,
            settlementAmountCents: args.purchaseDisbursementCents,
            txn,
          });

          // Release the PROMO funds
          await transactionService.transactionsReleaseNow({
            transactionId: args.promoTransactionId,
            sellerId: args.disbursementRecipientId,
            settlementAmountCents: args.promoDisbursementCents,
            txn,
          });

          // Mark the PURCHASE transaction as completed
          await transactionService.markCompleted({
            transactionId: args.purchaseTransactionId,
            receivedAt: new Date().toISOString(),
            emitChangeEvent: false,
            txn,
          });

          // Mark the PROMO transaction as completed
          await transactionService.markCompleted({
            transactionId: args.promoTransactionId,
            receivedAt: new Date().toISOString(),
            emitChangeEvent: false,
            txn,
          });

          await omClient.post('/v1/transactions/complete', {
            status: 'success',
            orderIds: args.orderIds,
            invoiceIds: args.invoiceIds,
            transactions: [
              {
                id: args.purchaseTransactionId,
                type: 'purchase',
              },
              {
                id: args.promoTransactionId,
                type: 'promotional',
              },
            ],
          });
        });

        increment('promo_succeeded');
      } catch (err) {
        increment('promo_succeeded_error');

        const error = err instanceof Error ? err : new Error('Unknown error');
        throw toWorkflowError(
          error,
          `Failed to finalise promo payment: ${error.message}`
        );
      }
    },

    /**
     * Handle the promo failed condition
     * @param purchaseTransactionId - The purchase transaction ID.
     * @param promoTransactionId - The promo transaction ID.
     * @param orderId - The order ID.
     * @param invoiceId - The invoice ID.
     */
    async promoFailed(args: PromotionalPaymentWorkflowArgs) {
      try {
        await sequelize.transaction(async txn => {
          await transactionService.markFailed({
            transactionId: args.purchaseTransactionId,
            declineReason: 'Promo failed',
            txn,
          });

          await transactionService.markFailed({
            transactionId: args.promoTransactionId,
            declineReason: 'Promo failed',
            txn,
          });

          await omClient.post('/v1/transactions/complete', {
            status: 'failed',
            orderIds: args.orderIds,
            invoiceIds: args.invoiceIds,
            transactions: [
              {
                id: args.purchaseTransactionId,
                type: 'purchase',
              },
              {
                id: args.promoTransactionId,
                type: 'promotional',
              },
            ],
          });

          increment('promo_failed');
        });
      } catch (err) {
        increment('promo_failed_error');

        throw err;
      }
    },
  };
}
