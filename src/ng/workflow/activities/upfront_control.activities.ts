import { ApplicationFailure } from '@temporalio/common';
import { UpfrontPayment } from '../../common/requests/upfront_payments.request';
import { UserService } from '../../domain/services/domain/user.service';
import { TransactionService } from '../../domain/services/domain/transaction.service';
import { omClient } from '../../../lib/om_client';
import { toWorkflowError } from '../../common/helpers/errors.helpers';
import { increment } from '../../../lib/stats';
import { UpfrontPaymentState } from '../types/upfront.types';

export function createUpfrontControlActivities(
  userService: UserService,
  transactionService: TransactionService
) {
  return {
    /**
     * Start the upfront payment workflow.
     *
     * @param payload - The upfront payment payload.
     */
    async upfrontStart(payload: UpfrontPayment) {
      const buyer = await userService.safeGetUser(payload.buyerUserId);
      if (!buyer) {
        throw ApplicationFailure.create({
          message: 'Buyer not found',
          nonRetryable: false,
        });
      }

      // TODO: Support finstro payments
      if (buyer.backend !== 'promisepay') {
        throw ApplicationFailure.create({
          message: 'Buyer backend is not Promisepay',
          nonRetryable: false,
        });
      }

      const seller = await userService.safeGetUser(payload.sellerUserId);
      if (!seller) {
        throw ApplicationFailure.create({
          message: 'Seller not found',
          nonRetryable: true,
        });
      }

      // TODO: Support finstro payments
      if (seller.backend !== 'promisepay') {
        throw ApplicationFailure.create({
          message: 'Seller backend is not Promisepay',
          nonRetryable: false,
        });
      }
    },

    /**
     * This marks the transaction as completed
     * Does not emit a change event
     *
     * @param args - The upfront payment complete payload.
     */
    async upfrontHoldingTransactionSucceeded(args: {
      holdingTransactionId: string;
      upfront: UpfrontPayment;
    }) {
      try {
        await transactionService.markCompleted({
          transactionId: args.holdingTransactionId,
          receivedAt: new Date().toISOString(),
          emitChangeEvent: false,
        });

        await omClient.patch(
          `/v1/orders/${args.upfront.orderId}/upfront/holding-finished`,
          {
            orderId: args.upfront.orderId,
            status: 'success',
            invoiceId: args.upfront.invoiceId,
            holdingTransactionId: args.holdingTransactionId,
          }
        );
        increment('upfront_payment_holding_transaction_succeeded');
      } catch (err) {
        increment('upfront_payment_holding_transaction_failed');

        throw toWorkflowError(
          err instanceof Error ? err : new Error('Unknown error'),
          'Failed to mark holding transaction as succeeded'
        );
      }
    },

    /**
     * This marks the transaction as failed
     * Does not emit a change event
     *
     * @param args - The upfront payment complete payload.
     */
    async upfrontHoldingTransactionFailed(args: {
      upfront: UpfrontPayment;
      holdingTransactionId: string;
      error: Error;
    }) {
      const declineReason =
        args.error instanceof Error
          ? args.error.message
          : 'Failed to process payment';

      try {
        await transactionService.markFailed({
          transactionId: args.holdingTransactionId,
          declineReason,
          emitChangeEvent: false,
        });

        await omClient.patch(
          `/v1/orders/${args.upfront.orderId}/upfront/holding-finished`,
          {
            orderId: args.upfront.orderId,
            status: 'failed',
            invoiceId: args.upfront.invoiceId,
            holdingTransactionId: args.holdingTransactionId,
          }
        );

        increment('upfront_payment_finalise_succeeded');
      } catch (err) {
        increment('upfront_payment_finalise_failed');

        throw toWorkflowError(
          err instanceof Error ? err : new Error('Unknown error'),
          'Failed to mark holding transaction as failed'
        );
      }
    },

    /**
     * Handle the upfront order complete success condition
     * Does not emit a change event
     *
     * @param payload - The upfront payment complete payload.
     */
    async upfrontFinaliseSucceeded(
      upfront: UpfrontPayment,
      state: UpfrontPaymentState
    ) {
      try {
        if (state.topUpTransactionId) {
          await transactionService.markCompleted({
            transactionId: state.topUpTransactionId,
            receivedAt: new Date().toISOString(),
            emitChangeEvent: false,
          });
        }

        await omClient.patch(
          `/v1/orders/${upfront.orderId}/upfront/finalise-finished`,
          {
            orderId: upfront.orderId,
            status: 'success',
            invoiceId: upfront.invoiceId,
            holdingTransactionId: state.holdingTransactionId,
            topUpTransactionId: state.topUpTransactionId,
          }
        );
      } catch (err) {
        increment('upfront_payment_finalise_succeeded');

        throw toWorkflowError(
          err instanceof Error ? err : new Error('Unknown error'),
          'Failed to finalise upfront payment'
        );
      }
    },

    /**
     * Handle the upfront order complete failure condition
     * Does not emit a change event
     *
     * @param payload - The upfront payment complete payload.
     */
    async upfrontFinaliseFailed(
      upfront: UpfrontPayment,
      state: UpfrontPaymentState,
      error: unknown
    ) {
      const declineReason =
        error instanceof Error ? error.message : 'Failed to process payment';

      try {
        await transactionService.markFailed({
          transactionId: state.topUpTransactionId,
          declineReason,
          emitChangeEvent: false,
        });

        await omClient.patch(
          `/v1/orders/${upfront.orderId}/upfront/finalise-finished`,
          {
            orderId: upfront.orderId,
            status: 'failed',
            invoiceId: upfront.invoiceId,
            holdingTransactionId: state.holdingTransactionId,
            topUpTransactionId: state.topUpTransactionId,
          }
        );

        increment('upfront_payment_finalise_succeeded');
      } catch (err) {
        increment('upfront_payment_finalise_failed');

        throw toWorkflowError(
          err instanceof Error ? err : new Error('Unknown error'),
          'Failed to finalise upfront payment'
        );
      }
    },

    /**
     * Handle the upfront order cancellation
     * Does not emit a change event
     *
     * @param state - The upfront payment state.
     */
    async upfrontFinaliseCancelled(state: UpfrontPaymentState) {
      try {
        await transactionService.markRefunded({
          transactionId: state.holdingTransactionId,
          emitChangeEvent: false,
          refundedAt: new Date().toISOString(),
          refundAmountCents: state.holdingAmountCents,
        });

        increment('upfront_payment_refund_succeeded');
      } catch (err) {
        increment('upfront_payment_refund_failed');

        throw toWorkflowError(
          err instanceof Error ? err : new Error('Unknown error'),
          'Failed to finalise upfront payment'
        );
      }
    },
  };
}
