{"extends": ["../../.eslintrc"], "overrides": [{"parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module", "project": "./src/ng/tsconfig.json"}, "files": ["**/*.ts", "**/*.tsx"], "rules": {"no-undef": "warn", "no-unused-vars": "off", "import/no-unresolved": "error", "import/named": "off", "@typescript-eslint/no-unnecessary-condition": "warn", "no-use-before-define": "off", "@typescript-eslint/no-use-before-define": "error", "no-shadow": "off", "@typescript-eslint/no-shadow": ["error"], "@typescript-eslint/no-redeclare": ["error"], "@typescript-eslint/no-floating-promises": "warn", "@typescript-eslint/no-misused-promises": "warn"}}]}