import { expect } from '@playwright/test';
import { UpfrontFlowStep } from '../../src/types/upfront.types';
import { UpfrontHttp } from '../http/upfront.http';

/**
 * Wait for an upfront payment to be in a specific state.
 *
 * @param orderId - The order ID.
 * @param state - The state to wait for.
 * @param timeout - The timeout in milliseconds.
 * @returns A promise that resolves when the upfront payment is in the specified state.
 */
export async function waitForUpfrontToBeInState(
  upfrontHttp: UpfrontHttp,
  orderId: string,
  state: UpfrontFlowStep,
  timeout = 20000
) {
  return expect
    .poll(
      async () => {
        const status = await upfrontHttp.getUpfrontStatus(orderId);

        return status === state;
      },
      { timeout }
    )
    .toBe(true);
}
