import { getUserHttp } from '../http/user.http';
import { getPaymentMethodsHttp } from '../http/payment-method.http';
import { createUserFixture } from '../fixtures/user.e2e_fixture';
import logger from '../../src/lib/logger';

/**
 * Ensure that the E2E users exist.
 * @param endpointUrl - The endpoint URL.
 */
export async function ensureE2eUsersExist(endpointUrl: string) {
  const userHttp = getUserHttp(endpointUrl);
  const paymentMethodsHttp = getPaymentMethodsHttp(endpointUrl);

  const user = createUserFixture({
    mobile_number: '+61485254632',
    email: '<EMAIL>',
    name: {
      first: 'E2E',
      last: 'Seller',
    },
    dob: '1990/01/01',
  });

  const createdUser = await userHttp.createUser(user);
  logger.info({ createdUser });

  const method = await paymentMethodsHttp.createCreditCardMethod(createdUser);
  logger.info({ method });
}
