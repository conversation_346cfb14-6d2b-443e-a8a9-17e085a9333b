import { expect } from '@playwright/test';
import moment from 'moment';

expect.extend({
  toBeInThePast(received: unknown) {
    return {
      pass: moment(received as string).isBefore(moment()),
      message: () => `Release at ${received} is in the past`,
    };
  },
});

// TypeScript declaration for your custom matchers
declare global {
  namespace PlaywrightTest {
    interface Matchers<R> {
      toBeInThePast(): R;
    }
  }
}
