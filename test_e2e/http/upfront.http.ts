import config from 'config';
import assert from 'node:assert';
// eslint-disable-next-line import/no-extraneous-dependencies
import request from 'supertest';
import { UpfrontFlowStep } from '../../src/types/upfront.types';
import {
  UpfrontPayment,
  UpfrontPaymentComplete,
} from '../../src/ng/common/requests/upfront_payments.request';

const AUTH_USER = config.get<string>('AUTH_USER');
const AUTH_PASS = config.get<string>('AUTH_PASS');

export function getUpfrontHttp(url: string) {
  return {
    /**
     * Create an upfront payment.
     * @param payload - The upfront payment payload.
     * @returns The upfront payment response.
     */
    async createUpfrontPayment(payload: UpfrontPayment) {
      const response = await request(url)
        .post('/v3/upfront-payments')
        .auth(AUTH_USER, AUTH_PASS)
        .send(payload);

      assert.equal(response.status, 200);
      assert.ok(response.body.holdingTransactionId);

      return response.body as { holdingTransactionId: string };
    },

    /**
     * Get the status of an upfront payment.
     * @param orderId - The order ID.
     * @returns The upfront payment status.
     */
    async getUpfrontStatus(orderId: string): Promise<UpfrontFlowStep> {
      const response = await request(url)
        .get(`/v3/upfront-payments/status/${orderId}`)
        .auth(AUTH_USER, AUTH_PASS);

      assert.equal(response.status, 200);
      assert.ok(response.body.flowStep);

      return response.body.flowStep as UpfrontFlowStep;
    },

    /**
     * Finalize an upfront payment.
     * @param payload - The upfront payment complete payload.
     * @returns The upfront payment response.
     */
    async finalizeUpfrontPayment(payload: UpfrontPaymentComplete) {
      const response = await request(url)
        .post('/v3/upfront-payments/finalise')
        .auth(AUTH_USER, AUTH_PASS)
        .send(payload);

      assert.equal(response.status, 200);

      return response.body as {
        finalisedTransactionId: string;
      };
    },
  };
}

export type UpfrontHttp = ReturnType<typeof getUpfrontHttp>;
