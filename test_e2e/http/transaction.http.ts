import config from 'config';
import assert from 'node:assert';
// eslint-disable-next-line import/no-extraneous-dependencies
import request from 'supertest';
import { TransactionModel } from '../../src/models/transaction';

const AUTH_USER = config.get<string>('AUTH_USER');
const AUTH_PASS = config.get<string>('AUTH_PASS');

export function getTransactionHttp(url: string) {
  return {
    async getTransaction(transactionId: string) {
      const response = await request(url)
        .get(`/v1/transactions/${transactionId}`)
        .auth(AUTH_USER, AUTH_PASS);

      assert.equal(response.status, 200);

      return response.body as TransactionModel;
    },
  };
}

export type TransactionHttp = ReturnType<typeof getTransactionHttp>;
