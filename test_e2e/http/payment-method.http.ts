import config from 'config';
// eslint-disable-next-line import/no-extraneous-dependencies
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import assert from 'node:assert';
import { Backend } from '../../src/models/transaction';
import { CurrencyCodes } from '../../src/types/currency_codes';
import logger from '../../src/lib/logger';

const AUTH_USER = config.get<string>('AUTH_USER');
const AUTH_PASS = config.get<string>('AUTH_PASS');

export function getPaymentMethodsHttp(url: string) {
  return {
    async createCreditCardMethod(params: {
      id: string;
      name: {
        first: string;
        last: string;
      };
      backend: Backend;
      stripe_customer_id: string;
    }) {
      const method = {
        user_id: params.id,
        is_settlement_account: false,
        backend: Backend.PROMISEPAY,
        id: uuidv4(),
        data: {
          type: 'card',
          name: '<PERSON> <PERSON>',
          expiry_month: '12',
          expiry_year: '2028',
          number: '****************',
          currency: CurrencyCodes.AUD,
          cvv: '123',
          issuer: 'visa',
        },
        backend_id: params.backend,
        stripe_customer_id: params.stripe_customer_id,
        addedById: params.id,
      };

      const response = await request(url)
        .post('/v1/cards')
        .auth(AUTH_USER, AUTH_PASS)
        .send(method);

      logger.info({ response: response.body });

      assert.equal(response.status, 200);
    },
  };
}
