import config from 'config';
import assert from 'node:assert';
// eslint-disable-next-line import/no-extraneous-dependencies
import request from 'supertest';
import { CreateUserRequest } from '../../src/types/requests/user-requests.type';
import { SerializeAttributes } from '../../src/models/user';

const AUTH_USER = config.get<string>('AUTH_USER');
const AUTH_PASS = config.get<string>('AUTH_PASS');

export function getUserHttp(url: string) {
  return {
    async getUser(id: string) {
      const response = await request(url)
        .get(`/v1/users/${id}`)
        .auth(AUTH_USER, AUTH_PASS);

      assert.equal(response.status, 200);

      return response.body;
    },

    async createUser(user: CreateUserRequest): Promise<SerializeAttributes> {
      const response = await request(url)
        .post('/v1/users')
        .auth(AUTH_USER, AUTH_PASS)
        .send(user);

      assert.equal(response.status, 200);

      return response.body as SerializeAttributes;
    },
  };
}
