import { v4 as uuid } from 'uuid';
// eslint-disable-next-line import/no-extraneous-dependencies
import faker from 'faker';
import { CreateUserRequest } from '../../src/types/requests/user-requests.type';
import { Backend } from '../../src/models/transaction';

/**
 * Returns a serialised user fixture with optional overrides
 */
export function createUserFixture(
  data?: Partial<CreateUserRequest>
): CreateUserRequest {
  return {
    id: uuid(),
    email: faker.internet.email(),
    name: {
      first: faker.name.firstName(),
      last: faker.name.lastName(),
    },
    dob: '1990/01/01',
    dd_settlement_delay_hours: 0,
    ordermentum_id: uuid(),
    external_id: uuid(),
    address: {
      line_1: '123 Test St',
      line_2: 'Test Suburb',
      city: 'Test City',
      state: 'Test State',
      postcode: '1234',
      country: 'AUS',
      ...data,
    },
    backend: Backend.PROMISEPAY,
    ...data,
  };
}
