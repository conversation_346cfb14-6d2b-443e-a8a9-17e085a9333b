{
  "extends": [
    "@ordermentum/ordermentum/mocha",
    "@ordermentum/ordermentum/jest"
  ],
  "rules": {
    // These rules are not useful in tests
    "@typescript-eslint/no-shadow": "off",
    "no-unused-vars": "off"
  },
  "overrides": [
    {
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "sourceType": "module",
        "project": "./test_e2e/tsconfig.json"
      },
      "files": [
        "**/*.ts",
        "**/*.tsx"
      ],
      "rules": {
        "no-undef": "warn",
        "no-unused-vars": "off",
        "import/no-unresolved": "error",
        "import/named": "off",
        "@typescript-eslint/no-unnecessary-condition": "warn",
        "no-use-before-define": "off",
        "@typescript-eslint/no-use-before-define": "error",
        "no-shadow": "off",
        "@typescript-eslint/no-shadow": [
          "error"
        ],
        "@typescript-eslint/no-redeclare": [
          "error"
        ],
        "@typescript-eslint/no-floating-promises": "warn",
        "@typescript-eslint/no-misused-promises": "warn"
      }
    }
  ]
}