const { Sequelize } = require('sequelize');

module.exports = {
  async up(queryInterface) {
    await queryInterface.createTable('user_settings', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      settlement_delay_days: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      manual_disbursement: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      is_supplier: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      net_settlement: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      reference: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      settlement_rate: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      weekly_settlement: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      company_descriptor: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: 'users', key: 'id' },
        onUpdate: 'cascade',
        onDelete: 'cascade',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('user_settings');
  },
};
