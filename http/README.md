
# Zai API playground

Utilising the VSCode REST Client extension we can have a neat method to play with the Zai API that is independent of test runners or application code. This is very useful in confirming API behaviour.

Think of these as a replacement for Postman requests with the added benefits that:

- They are Git controlled and versioned
- We do not have to pay <PERSON>man anything


## Configuring

You will need to update the local `.vscode/settings.json` file to include environment variables in order to run the Zai HTTP requests:

```json
  {
    // ...
    "rest-client.environmentVariables": {
      "zai-prelive": {
        "ZAI_AUTH_URL": "https://au-0000.sandbox.auth.assemblypay.com/tokens",
        "ZAI_CLIENT_ID": "6jng5nsvelhkf5v4eenqfof4bp",
        "ZAI_CLIENT_SECRET": "1g3qp0ksp8q48qotrv689k8c4cp140s795bm9cfad92sk5n0hau2",
        "ZAI_CLIENT_SCOPE": "im-au-03/11fe0200-b1a8-471a-840d-a031d3924c3f:08e7f6ef-b667-4749-9112-450c7b6944f7:3"
      }
    }
  }
```

## Running requests

- Install the [extension](https://marketplace.visualstudio.com/items?itemName=humao.rest-client)
- Run each request by clicking the text above it `send request`


