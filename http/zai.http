
# Auth
# @name tokenAPI
POST {{ZAI_AUTH_URL}}
accept: application/json
content-type: application/json

{
  "grant_type": "client_credentials",
  "client_id": "{{ZAI_CLIENT_ID}}",
  "client_secret": "{{ZAI_CLIENT_SECRET}}",
  "scope": "{{ZAI_CLIENT_SCOPE}}"
}
###

@authToken = {{tokenAPI.response.body.access_token}}


# Get all items in Zai (paged)
GET {{ZAI_URL}}/items
Authorization: Bearer {{authToken}}

###

# Create a new card account for the sender. This will ensure a valid card account
# with a fresh token with worldpay exists (NOTE: the card account token will expire
# in the pre-live zai environment in 7days: https://ordermentum.slack.com/archives/CJGHC367K/p1750112838340259?thread_ts=**********.862719&cid=CJGHC367K
# @name createCardAccount
# @prompt senderId
#
# REMINDERS:
#  - Do this when the card expires https://ordermentum.slack.com/archives/CJGHC367K/p1750112838340259?thread_ts=**********.862719&cid=CJGHC367K
#  - Update the database record too
#
# TEST FOR FAILURE:
#  - Use `REFUSEDRC51MAC24` as the fullname
#
POST {{ZAI_URL}}/card_accounts
Authorization: Bearer {{authToken}}
Accept: application/json
Content-Type: application/json

{
  "full_name": "REFUSEDRC51MAC24",
  "number": "****************",
  "expiry_month": "{{$datetime 'MM' }}",
  "expiry_year": "{{$datetime 'YYYY' }}",
  "cvv": "123",
  "user_id": "{{senderId}}"
}

###

# CREATE an item between sender and recipient
# @name createItem
# @prompt senderId
# @prompt recipientId
POST {{ZAI_URL}}/items
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "id": "{{$guid}}",
  "name": "rest test item",
  "amount": 100,
  "currency": "AUD",
  "payment_type": 2,
  "buyer_id": "{{senderId}}",
  "seller_id": "{{recipientId}}"
}

###

# READ/GET an existing item by id
# @prompt itemId
GET {{ZAI_URL}}/items/{{itemId}}
Authorization: Bearer {{authToken}}

###

# MAKE a payment against an item
# @prompt itemId
# @prompt cardAccountId
PATCH {{ZAI_URL}}/items/{{itemId}}/make_payment
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "account_id": "{{cardAccountId}}",
  "merchant_phone": "+***********",
  "ccv": "444"
}

###

# READ/GET a card account
# @prompt cardAccountId
GET {{ZAI_URL}}/card_accounts/{{cardAccountId}}
Authorization: Bearer {{authToken}}
Accept: application/json

###

# READ/GET a card account users
# @prompt cardAccountId
GET {{ZAI_URL}}/card_accounts/{{cardAccountId}}/users
Authorization: Bearer {{authToken}}

###

# @prompt userId
GET {{ZAI_URL}}/users/{{userId}}/card_accounts
Authorization: Bearer {{authToken}}

###

# @prompt userId
GET {{ZAI_URL}}/users/{{userId}}/card_accounts
Authorization: Bearer {{authToken}}

###

# DELETE (redact) a card account
# @prompt cardAccountId
DELETE {{ZAI_URL}}/card_accounts/{{cardAccountId}}
Authorization: Bearer {{authToken}}

