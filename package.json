{"name": "payments", "version": "1.0.0", "description": "A payments microservice for the Ordermentum platform", "main": "src/index.js", "scripts": {"build": "yarn build:clean && yarn run tsc", "build:clean": "rm -rf build", "start": "node --max-http-header-size=65536 ./bin/app", "start:local": "NODE_ENV=development node --max-http-header-size=65536 ./bin/app", "start:local:debugger": "NODE_ENV=development node --inspect=0.0.0.0:9339 --max-http-header-size=65536 ./bin/app", "start:e2e": "source .env-e2e && node --max-http-header-size=65536 ./bin/app | node_modules/.bin/bunyan", "jobs": "node ./bin/jobs", "jobs:local": "NODE_ENV=development node ./bin/jobs", "jobs:e2e": "source .env-e2e && NODE_ENV=test node ./bin/jobs | node_modules/.bin/bunyan", "watch": "yarn spec -- --watch", "lint": "NODE_OPTIONS='--max-old-space-size=4096' yarn eslint '**/*.{ts,js}'", "promisepay:clearWebhooks": "/usr/bin/env node ./bin/promisepay_clear_webhooks", "db:migration": "sequelize migration:create --url `echo $DATABASE_URI`", "db:migrate": "sequelize db:migrate --url `echo $DATABASE_URI`", "db:migrate:undo": "sequelize db:migrate:undo --url `echo $DATABASE_URI`", "typecheck": "yarn tsc --noEmit", "db:setup": "./bin/initialize && ./bin/create_topics && yarn run db:migrate", "db:setup:dev": "NODE_ENV=development yarn run db:setup", "db:fresh": "npm run db:reset && npm run db:migrate", "db:reset": "./bin/reset", "setup:dev": "yarn run db:setup:dev && yarn run user:setup:dev", "spec": "NODE_ENV=test yarn run mocha 'test/**/*.{js,ts}' --reporter spec", "spec:coverage": "yarn nyc --silent --no-clean yarn spec", "report-coverage": "yarn nyc report --reporter=html", "spec:local": "NODE_ENV=test yarn run mocha 'test/**/*.{js,ts}' --reporter spec", "spec:inspect": "NODE_ENV=test yarn run mocha 'test/**/*.{js,ts}' --reporter spec --inspect=0.0.0.0:9339", "spec:runner": "NODE_ENV=test yarn run mocha", "spec:runner:local": "NODE_ENV=test yarn run mocha", "test": "npm run db:reset && npm run db:setup && npm run db:migrate && npm run spec", "test:e2e": "TS_NODE_TRANSPILE_ONLY=true ts-node -P tsconfig_e2e.json -r tsconfig-paths/register node_modules/.bin/playwright test", "start:dev": "node --max-http-header-size=65536 ./bin/app", "watch:app": "watchexec -w src -r yarn run start:dev | ./node_modules/.bin/bunyan -o short", "start:dev:local": "source .env-local && node --max-http-header-size=65536 ./bin/app", "watch:app:local": "watchexec -w src -r yarn run start:dev:local | ./node_modules/.bin/bunyan -o short", "topics:setup": "./bin/create_topics", "prepare": "husky install", "watch:jobs:local": "watchexec -w src -r yarn run jobs:local | ./node_modules/.bin/bunyan -o short", "index:migrate": "node bin/migrate-indices.js", "user:setup:dev": "NODE_ENV=development node bin/add_sandbox_om_users"}, "repository": {"type": "git", "url": "git+ssh://**************/ordermentum/payments.git"}, "author": "<EMAIL>", "license": "UNLICENSED", "bugs": {"url": "https://github.com/ordermentum/payments/issues"}, "nyc": {"exclude": ["migrations", "config", "test", "node_modules"]}, "resolutions": {"@types/express": "4.17.23", "@types/bunyan": "1.8.11", "@ordermentum/asap-core": "0.1.9", "@ordermentum/asap-core/axios": "1.10.0"}, "homepage": "https://github.com/ordermentum/payments#readme", "private": true, "dependencies": {"@elastic/elasticsearch": "7.17.14", "@ordermentum/asap-core": "0.1.9", "@ordermentum/auth-driver": "^5.1.1", "@ordermentum/auth-middleware": "^4.0.0", "@ordermentum/axios-asap": "^0.2.0", "@ordermentum/axios-retry": "^1.0.1", "@ordermentum/cache-machine": "2.3.1", "@ordermentum/express-asap": "0.2.0-beta1", "@ordermentum/fireflight": "^1.1.0", "@ordermentum/health": "1.0.3", "@ordermentum/prowl": "^0.6.0", "@ordermentum/scheduler": "^0.1.8", "@ordermentum/serdes_changelog": "2.0.2", "@ordermentum/serdes_payment_method": "2.0.0", "@ordermentum/serdes_payment_transactions": "2.0.2", "@ordermentum/slingshot": "^1.0.3", "@ordermentum/temporal": "1.0.0", "@playwright/test": "^1.52.0", "@sentry/node": "7.120.3", "@slack/client": "5.0.2", "@steveojs/datadog": "^7.1.3", "@steveojs/scheduler-sequelize": "^7.6.0", "@temporalio/client": "^1.12.1", "@temporalio/common": "^1.12.1", "@temporalio/worker": "^1.12.1", "@temporalio/workflow": "^1.12.1", "@types/redlock": "^4.0.7", "assembly-payments": "0.2.0-beta9", "aws-sdk": "2.1692.0", "axios": "1.10.0", "basic-auth": "2.0.1", "bignumber.js": "9.3.0", "bluebird": "3.7.2", "body-parser": "1.20.3", "bodybuilder": "2.5.1", "bunyan": "1.8.15", "bunyan-middleware": "1.0.2", "commander": "6.2.1", "config": "^3.3.9", "core-js": "3.43.0", "credit-card-type": "9.1.0", "cron-parser": "2.18.0", "csv-parse": "5.6.0", "dd-trace": "^4.20.0", "express": "4.21.2", "express-async-errors": "3.1.1", "express-async-handler": "^1.2.0", "express-bearer-token": "2.4.0", "hot-shots": "^11.1.0", "http-terminator": "^3.2.0", "httperrors": "2.3.0", "ioredis": "4.30.0", "joi": "^17.6.0", "json2csv": "5.0.7", "jsonwebtoken": "9.0.2", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "lodash.get": "4.4.2", "mandrill-api": "1.0.45", "memory-format": "0.1.0", "moment": "2.30.1", "moment-business": "3.0.1", "moment-timezone": "0.6.0", "multer": "1.4.5-lts.2", "pg": "8.16.3", "prom-client": "^14.1.0", "qs": "^6.11.0", "raven": "1.2.1", "redlock": "4.2.0", "request": "2.88.2", "retry-machine": "^1.0.2", "s3-upload-stream": "1.0.7", "sequelize": "6.37.7", "sequelize-cli": "6.6.3", "spreedly": "0.0.16", "squel": "^5.13.0", "steveo": "^8.2.1", "stripe": "^17.5.0", "superagent": "6.1.0", "tv4": "1.3.0", "typescript": "4.9.5", "uuid": "11.1.0", "yup": "0.32.11", "zai-payments": "^0.2.3", "zod": "^3.22.2"}, "devDependencies": {"@emotion/eslint-plugin": "^11.10", "@ordermentum/eslint-config-ordermentum": "4.0.0", "@temporalio/testing": "^1.12.1", "@types/bluebird": "3.5.42", "@types/body-parser": "^1.19.2", "@types/bunyan": "1.8.11", "@types/chai": "4.3.20", "@types/chai-as-promised": "^8.0.2", "@types/config": "^3.3.5", "@types/csv-parse": "1.2.5", "@types/express": "4.17.23", "@types/faker": "5.5.9", "@types/handlebars": "4.1.0", "@types/http-errors": "^2.0.4", "@types/ioredis": "4.28.10", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "9.0.10", "@types/lodash": "4.17.19", "@types/mocha": "10.0.10", "@types/multer": "1.4.13", "@types/node": "20.19.2", "@types/qs": "6.14.0", "@types/s3-upload-stream": "1.0.7", "@types/sinon": "17.0.4", "@types/supertest": "6.0.3", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^5.48.2", "@typescript-eslint/parser": "^5.48.2", "chai": "4.5.0", "chai-as-promised": "^7.1.1", "codecov": "3.8.3", "doubleagent": "2.0.0", "eslint": "^8.55.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-mocha": "^9.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.1", "eslint-plugin-react-hooks": "^4", "eslint-plugin-unicorn": "^45.0.2", "factory.ts": "^1.4.2", "faker": "5.5.3", "husky": "8.0.3", "lint-staged": "9.5.0", "mocha": "^10.8.2", "nock": "^13.5.5", "nyc": "^17.0.0", "oakydoke": "1.1.2", "prettier": "^2.8.3", "prompt-promise": "1.0.3", "sinon": "15.2.0", "sinon-chai": "^3.7.0", "supertest": "7.1.1", "ts-node": "^10.9.2", "ts-node-dev": "1.1.8", "ts-sinon": "^2.0.2"}, "lint-staged": {"*.{ts,js,jsx}": ["yarn run eslint --fix --max-warnings 0 --no-ignore", "git add"]}, "prettier": {"singleQuote": true, "arrowParens": "avoid"}, "packageManager": "yarn@1.22.22"}