process.env.TEMPORAL_DISABLE_TLS = true;

module.exports = {
  DEPLOYED: false,
  NODE_ENV: 'test',
  DATABASE_URI:
    process.env.DATABASE_URI ||
    'postgres://ordermentum:@localhost/ordermentum_payments_testing',
  DATABASE_INITIALIZATION_URI:
    process.env.DATABASE_INITIALIZATION_URI ||
    'postgres://postgres@localhost/template1',
  ELASTICSEARCH_HOST: process.env.ELASTICSEARCH_HOST || 'http://localhost:9200',
  ELASTICSEARCH_USER: process.env.ELASTICSEARCH_USER || 'elastic',
  ELASTICSEARCH_PASS: process.env.ELASTICSEARCH_PASS || 'changeme',
  ELASTICSEARCH_PREFIX: process.env.ELASTICSEARCH_PREFIX || 'testing',
  REDIS_URL: process.env.REDIS_URL || 'localhost',
  PORT: 4000,
  LOG_LEVEL: process.env.LOG_LEVEL ?? 'info',
  AUTH_URL: process.env.AUTH_URL || '**************************',
  AUTH_USER: process.env.AUTH_USER || 'test',
  AUTH_PASS: process.env.AUTH_PASS || 'pass',
  DISABLE_AUTH: false,
  PP_USER: '<EMAIL>',
  PP_TOKEN: 'OWEyNWZkZDg5MjZmOTcwYjUzOTJlMGU2MGU1NTMyOGI=',
  STRIPE_SECRET_KEY: 'sk_test_I0SzY3cdEAPI1Cz3GJUugONV00Q7GMPNct',
  INTEGRATION_WEBHOOK: 'http://localhost:3001',
  PP_SANDPIT: true,
  SKIP_BACKENDS: process.env.SKIP_BACKENDS === 'true',
  STEVEO_ENGINE: process.env.STEVEO_ENGINE || 'sqs',
  CALLBACK_URI: process.env.CALLBACK_URI || 'https://example.com',
  POSSIBLE_FAILURE_DAYS: 4,
  OM_USER_ID: '01DDC48A-77C6-4A52-BFB9-14D2B6894C2D',
  OM_HOLDING_USER_ID: 'e1e927a9-dcce-493e-9db3-154ee2af69e9',
  OM_FUNDING_USER_ID: '72d4a432-02a5-1ac8-1b66-46a6d156c2e2',
  OM_REFUND_USER_ID: 'b918eaed-e2a0-1017-36d8-4f5f07196160',
  OM_WALLET_USER_ID: 'e84b6979-29ad-4afc-af00-61873dab0354',
  OM_FINSTRO_USER_ID: '9D31186B-9B15-45C6-A022-B0B672845E1B',
  FEES_ACCOUNT_ID: 'BAEAB5E8-6C93-4FF3-8842-F801FB327E84',
  DEFAULT_JOB_LAG: '5000',
  JOB_ENQUEUE_LIMIT: '1',
  AWS_REGION: 'us-east-1',
  AWS_BUCKET_NAME: 'exports.ordermentum.io',
  S3_HOST: 'https://localhost:4566',
  KAFKA_BOOTSTRAP_SERVERS: 'localhost:9092',
  DISABLE_STEVEO_HEALTH: process.env.DISABLE_STEVEO_HEALTH || false,
  KAFKA_DEFAULT_PARTITIONS: 1,
  OM_BASE_URL: 'http://localhost:3001',
  DOUBLE_ENTRY_JOURNAL: true,
  FINSTRO_UPFRONT_ENABLED: true,
  FINSTRO_API_URL: 'https://coreapi.sit-au.finstro.com/api/SDK',
  FINSTRO_SDK_TOKEN: 'REPLACE_ME',
  INSIGHTS_URL: 'http://localhost:36382',
  NOTIFICATIONS_URL: 'http://localhost:32308',
  JWT_SECRET: 'testsecret',
  RETAILER_ROUTES_ENABLED: true,
  INDEXING_ENABLED: true,
  MANDRILL_TOKEN: '',
  SLACK_TOKEN: '',
  AP_CLIENT_ID: process.env.AP_CLIENT_ID || '',
  AP_CLIENT_SECRET: process.env.AP_CLIENT_SECRET || '',
  AP_CLIENT_SCOPE: process.env.AP_CLIENT_SCOPE || '',
  ELASTIC_APM_SECRET_TOKEN: '',
  SENTRY_DSN: '',
  SPREEDLY_ENVIRONMENT: '',
  SPREEDLY_TOKEN: '',
  STRIPE_SIGNING_SECRET: '',
  ASAP: {
    ASAP_USER_ID: '',
    ASAP_PUBLIC_KEYS_URL: 'http://nowhere',
    ASAP_PUBLIC_KEY_ID: 'payments',
    ASAP_PRIVATE_KEY: '',
    insecureMode: true,
  },
  DB_POOL_SIZE: 5,
  JOURNAL_V3: true,
  ENABLED_WORKERS: ['temporal', 'sqs', 'database', 'kafka'],
  TEMPORAL_QUEUES: [],
};
