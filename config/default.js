module.exports = {
  DEPLOYED: true,
  SANDBOX: process.env?.SANDBOX === 'true',
  NODE_ENV: process.env.NODE_ENV,
  DATABASE_URI: process.env.DATABASE_URI,
  DATABASE_INITIALIZATION_URI: process.env.DATABASE_INITIALIZATION_URI,
  ELASTIC<PERSON>ARCH_HOST: process.env.ELASTICSEARCH_HOST,
  ELASTICSEARCH_USER: process.env.ELASTICSEARCH_USER,
  ELASTICSEARCH_PASS: process.env.ELASTICSEARCH_PASS,
  <PERSON>LASTIC<PERSON>ARCH_PREFIX: process.env.ELASTICSEARCH_PREFIX,
  REDIS_URL: process.env.REDIS_URL,
  PORT: parseInt(process.env.PORT || '4000', 10),
  LOG_LEVEL: process.env.LOG_LEVEL,
  AUTH_USER: process.env.AUTH_USER,
  AUTH_PASS: process.env.AUTH_PASS,
  DISABLE_AUTH: process.env?.DISABLE_AUTH === 'true',
  PP_USER: process.env.PP_USER,
  PP_TOKEN: process.env.PP_TOKEN,
  STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
  INTEGRATION_WEBHOOK: process.env.INTEGRATION_WEBHOOK,
  PP_SANDPIT: process.env?.PP_SANDPIT === 'true',
  SKIP_BACKENDS: process.env?.SKIP_BACKENDS === 'true',
  STEVEO_ENGINE: process.env.STEVEO_ENGINE,
  CALLBACK_URI: process.env.CALLBACK_URI,
  POSSIBLE_FAILURE_DAYS: process.env.POSSIBLE_FAILURE_DAYS,
  OM_USER_ID: process.env.OM_USER_ID,
  OM_HOLDING_USER_ID: process.env.OM_HOLDING_USER_ID,
  OM_FUNDING_USER_ID: process.env.OM_FUNDING_USER_ID,
  OM_REFUND_USER_ID: process.env.OM_REFUND_USER_ID,
  OM_WALLET_USER_ID: process.env.OM_WALLET_USER_ID,
  OM_FINSTRO_USER_ID: process.env.OM_FINSTRO_USER_ID,
  FEES_ACCOUNT_ID: process.env.FEES_ACCOUNT_ID,
  DEFAULT_JOB_LAG: parseInt(process.env.DEFAULT_JOB_LAG || '5000', 10),
  JOB_ENQUEUE_LIMIT: parseInt(process.env.JOB_ENQUEUE_LIMIT || '1', 10),
  AWS_REGION: process.env.AWS_REGION,
  AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
  S3_HOST: process.env.S3_HOST,
  LOCALSTACK_HOST: process.env.LOCALSTACK_HOST,
  LOCALSTACK_UP_ENDPOINT: process.env.LOCALSTACK_UP_ENDPOINT,
  KAFKA_BOOTSTRAP_SERVERS: process.env.KAFKA_BOOTSTRAP_SERVERS,
  DISABLE_STEVEO_HEALTH: process.env.DISABLE_STEVEO_HEALTH || false,
  KAFKA_DEFAULT_PARTITIONS: process.env.KAFKA_DEFAULT_PARTITIONS,
  OM_BASE_URL: process.env.OM_BASE_URL,
  DOUBLE_ENTRY_JOURNAL: process.env?.DOUBLE_ENTRY_JOURNAL === 'true',
  FINSTRO_API_URL: process.env.FINSTRO_API_URL,
  FINSTRO_SDK_TOKEN: process.env.FINSTRO_SDK_TOKEN,
  FINSTRO_UPFRONT_ENABLED: process.env?.FINSTRO_UPFRONT_ENABLED === 'true',
  INSIGHTS_URL: process.env.INSIGHTS_URL,
  AUTH_URL: process.env.AUTH_URL,
  NOTIFICATIONS_URL: process.env.NOTIFICATIONS_URL,
  JWT_SECRET: process.env.JWT_SECRET,
  RETAILER_ROUTES_ENABLED: process.env?.RETAILER_ROUTES_ENABLED === 'true',
  INDEXING_ENABLED: process.env?.INDEXING_ENABLED === 'true',
  MANDRILL_TOKEN: process.env.MANDRILL_TOKEN,
  SLACK_TOKEN: process.env.SLACK_TOKEN,
  AP_CLIENT_ID: process.env.AP_CLIENT_ID,
  AP_CLIENT_SECRET: process.env.AP_CLIENT_SECRET,
  AP_CLIENT_SCOPE: process.env.AP_CLIENT_SCOPE,
  ELASTIC_APM_SECRET_TOKEN: process.env.ELASTIC_APM_SECRET_TOKEN,
  SENTRY_DSN: process.env.SENTRY_DSN,
  SPREEDLY_ENVIRONMENT: process.env.SPREEDLY_ENVIRONMENT,
  SPREEDLY_TOKEN: process.env.SPREEDLY_TOKEN,
  STRIPE_SIGNING_SECRET: process.env.STRIPE_SIGNING_SECRET,
  DB_POOL_SIZE: parseInt(process.env.DB_POOL_SIZE || '5', 10),
  ASAP: {
    service: 'payments',
    ASAP_USER_ID: process.env.ASAP_USER_ID,
    ASAP_PUBLIC_KEYS_URL: process.env.ASAP_PUBLIC_KEYS_URL,
    insecureMode: false,
    maxLifeTimeSeconds: parseInt(process.env.ASAP_EXPIRY || '300', 10),
    ASAP_PUBLIC_KEY_ID: process.env.ASAP_PUBLIC_KEY_ID,
    ASAP_PRIVATE_KEY: process.env.ASAP_PRIVATE_KEY,
  },
  SQS_MAX_MESSAGES: parseInt(process.env.SQS_MAX_MESSAGES || '1', 10),
  WORKER_COUNT: parseInt(process.env.WORKER_COUNT || '1', 10),
  REDIS_USE_TLS: process.env?.REDIS_USE_TLS === 'true',
  JOURNAL_V3: process.env.JOURNAL_V3 === 'true',
  // Values can be comma separated list of queues: payments:make-payment
  TEMPORAL_QUEUES: process.env.TEMPORAL_QUEUES?.split(',') || [],
  // Values can be comma separated list of workers: temporal,sqs,database,kafka
  ENABLED_WORKERS: process.env.ENABLED_WORKERS?.split(',') || [],
};
