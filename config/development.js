process.env.TEMPORAL_DISABLE_TLS = true;

module.exports = {
  DEPLOYED: false,
  NODE_ENV: 'development',
  DATABASE_URI:
    'postgres://ordermentum:@localhost/ordermentum_payments_development',
  DATABASE_INITIALIZATION_URI: 'postgres://postgres@localhost/template1',
  ELASTICSEARCH_HOST: 'http://localhost:9200',
  ELASTICSEARCH_USER: 'elastic',
  ELASTICSEARCH_PASS: 'changeme',
  ELASTICSEARCH_PREFIX: 'development',
  REDIS_URL: 'localhost',
  PORT: 4000,
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  AUTH_USER: 'test',
  AUTH_PASS: 'pass',
  DISABLE_AUTH: false,
  PP_USER: '<EMAIL>',
  PP_TOKEN: 'OWEyNWZkZDg5MjZmOTcwYjUzOTJlMGU2MGU1NTMyOGI=',
  STRIPE_SECRET_KEY: 'sk_test_I0SzY3cdEAPI1Cz3GJUugONV00Q7GMPNct',
  INTEGRATION_WEBHOOK: 'http://localhost:3001',
  PP_SANDPIT: true,
  SKIP_BACKENDS: true,
  STEVEO_ENGINE: 'sqs',
  CALLBACK_URI: 'https://ordermentum-payments.loca.lt',
  POSSIBLE_FAILURE_DAYS: 4,
  OM_USER_ID: '0725a810-78f4-45a1-91db-7ce0462afbba',
  OM_HOLDING_USER_ID: 'af0a84ea-2bce-da17-e67b-8cda2153f7d2',
  OM_FUNDING_USER_ID: '0725a810-78f4-45a1-91db-7ce0462afbba',
  OM_REFUND_USER_ID: '0725a810-78f4-45a1-91db-7ce0462afbba',
  OM_WALLET_USER_ID: '4071918A-98B3-4AC1-9CAF-9EA39FA5BA40',
  OM_FINSTRO_USER_ID: 'B22DCF6A-269E-43AE-A644-AC39A66EDDD7',
  FEES_ACCOUNT_ID: 'BAEAB5E8-6C93-4FF3-8842-F801FB327E84',
  DEFAULT_JOB_LAG: '5000',
  JOB_ENQUEUE_LIMIT: '1',
  AWS_REGION: 'us-east-1',
  AWS_BUCKET_NAME: 'exports.ordermentum.io',
  S3_HOST: 'https://localhost:4566',
  LOCALSTACK_HOST: 'http://localhost:4566',
  LOCALSTACK_UP_ENDPOINT: 'http://localhost:4566',
  KAFKA_BOOTSTRAP_SERVERS: 'localhost:9092',
  DISABLE_STEVEO_HEALTH: false,
  KAFKA_DEFAULT_PARTITIONS: 1,
  OM_BASE_URL: 'http://localhost:3001',
  DOUBLE_ENTRY_JOURNAL: true,
  FINSTRO_API_URL: '',
  FINSTRO_SDK_TOKEN: '',
  FINSTRO_UPFRONT_ENABLED: true,
  INSIGHTS_URL: 'http://localhost:36382',
  AUTH_URL: '**************************',
  NOTIFICATIONS_URL: 'http://localhost:32308',
  JWT_SECRET: 'testsecret',
  RETAILER_ROUTES_ENABLED: true,
  INDEXING_ENABLED: true,
  MANDRILL_TOKEN: '',
  SLACK_TOKEN: '',
  AP_CLIENT_ID: '6jng5nsvelhkf5v4eenqfof4bp',
  AP_CLIENT_SECRET: '1g3qp0ksp8q48qotrv689k8c4cp140s795bm9cfad92sk5n0hau2',
  AP_CLIENT_SCOPE:
    'im-au-03/11fe0200-b1a8-471a-840d-a031d3924c3f:08e7f6ef-b667-4749-9112-450c7b6944f7:3',
  ELASTIC_APM_SECRET_TOKEN: '',
  SENTRY_DSN: '',
  SPREEDLY_ENVIRONMENT: '',
  SPREEDLY_TOKEN: '',
  STRIPE_SIGNING_SECRET: '',
  ASAP: {
    ASAP_USER_ID: '',
    ASAP_PUBLIC_KEYS_URL: 'http://nowhere',
    ASAP_PUBLIC_KEY_ID: 'payments',
    ASAP_PRIVATE_KEY: '',
    insecureMode: true,
  },
  DB_POOL_SIZE: 5,
  JOURNAL_V3: true,
  ENABLED_WORKERS: ['temporal', 'sqs', 'database', 'kafka'],
  TEMPORAL_QUEUES: [],
};
