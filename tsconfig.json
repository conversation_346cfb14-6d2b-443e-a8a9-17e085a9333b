{"compilerOptions": {"lib": ["es2022"], "target": "es2017", "module": "commonjs", "allowJs": true, "checkJs": false, "declaration": false, "outDir": "build", "strict": true, "resolveJsonModule": true, "alwaysStrict": true, "noUnusedLocals": true, "noImplicitAny": false, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "node"}, "include": ["src"]}