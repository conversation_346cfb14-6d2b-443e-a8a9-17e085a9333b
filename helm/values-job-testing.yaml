replicaCount: 1
nameOverride: payments
image:
  repository: ************.dkr.ecr.ap-southeast-2.amazonaws.com/ordermentum/payments
  tag: develop
  pullPolicy: IfNotPresent
service:
  type: ClusterIP
  externalPort: 9464
  internalPort: 9464 # This should match the PORT value in the env section
  enabled: true
disableLivenessProbe: true
disableReadinessProbe: true
resources:
  limits:
    #  cpu: 100m
    memory: 768Mi
  requests:
    #  cpu: 100m
    memory: 512Mi
serviceAccount: aws-s3-sqs-access-v2
env:
  awsRegion:
    name: 'AWS_REGION'
    value: 'us-east-1'
  awsBucketName:
    name: 'AWS_BUCKET_NAME'
    value: 'exports.ordermentum-sandbox.com'
  callbackUri:
    name: 'CALLBACK_URI'
    value: 'https://payments.ordermentum-sandbox.com/'
  logLevel:
    name: 'LOG_LEVEL'
    value: 'info'
  spreedlyEnvironment:
    name: 'SPREEDLY_ENVIRONMENT'
    value: '2FJ5Ylv3UHTgMFU1rV7FWjbu76J'
  spreedlyToken:
    name: 'SPREEDLY_TOKEN'
    value: 'ipcrcsWydA4BsJQOMsGJM1gLMvV99Fvff3jBnSJaBIdQ0ZL44l5bUEQFKsfvhluG'
  nodeEnv:
    name: 'NODE_ENV'
    value: 'testing'
  disableAuth:
    name: 'DISABLE_AUTH'
    value: 'false'
  port:
    name: 'PORT'
    value: 9464
  possibleFailureDays:
    name: 'POSSIBLE_FAILURE_DAYS'
    value: 4
  ppSandpit:
    name: 'PP_SANDPIT'
    value: 'true'
  skipBackends:
    name: 'SKIP_BACKENDS'
    value: 'false'
  tz:
    name: 'TZ'
    value: 'Australia/Sydney'
  steveoEngine:
    name: 'STEVEO_ENGINE'
    value: 'sqs'
  redisUrl:
    name: 'REDIS_URL'
    value: 'master.redis-main.hzrisb.use1.cache.amazonaws.com'
  redisUseTls:
    name: 'REDIS_USE_TLS'
    value: 'true'
  elasticsearchHost:
    name: 'ELASTICSEARCH_HOST'
    value: 'http://es-data.sandbox.ordermentum-sandbox.com:9201'
  dbPoolSize:
    name: 'DB_POOL_SIZE'
    value: 10
  defaultJobLag:
    name: 'DEFAULT_JOB_LAG'
    value: '5000'
  jobEnqueueLimit:
    name: 'JOB_ENQUEUE_LIMIT'
    value: 5
  workerCount:
    name: 'WORKER_COUNT'
    value: 5
  sqsMaxMessages:
    name: 'SQS_MAX_MESSAGES'
    value: 5
  asapPublicKeysUrl:
    name: 'ASAP_PUBLIC_KEYS_URL'
    value: 'https://ordermentum-service-public-keys-sandbox.s3.us-east-1.amazonaws.com/'
  baseUrl:
    name: 'OM_BASE_URL'
    value: 'https://app.ordermentum-sandbox.com'
  insightsUrl:
    name: 'INSIGHTS_URL'
    value: 'https://insights.ordermentum-sandbox.com'
  notificationsUrl:
    name: 'NOTIFICATIONS_URL'
    value: 'https://notifications.ordermentum-sandbox.com'
  doubleEntryJournal:
    name: 'DOUBLE_ENTRY_JOURNAL'
    value: 'true'
  journalV3:
    name: 'JOURNAL_V3'
    value: 'true'
  asapUserId:
    name: 'ASAP_USER_ID'
    value: '6f955320-d486-46b9-a45d-7f0ac3760d2c'
  datadogEnvironment:
    name: DD_ENV
    value: sandbox
  datadogServiceName:
    name: DD_SERVICE
    value: payments-job
  datadogServiceVersion:
    name: DD_VERSION
    value: ''
  datadogEntityId:
    name: DD_ENTITY_ID
    valueFrom:
      fieldRef:
        apiVersion: v1
        fieldPath: metadata.uid
  nodeOptions:
    name: NODE_OPTIONS
    value: ' --require=dd-trace/init'
  finstroAPIUrl:
    name: 'FINSTRO_API_URL'
    value: 'https://coreapi.sit-au.finstro.com/api/SDK'
  authUrl:
    name: 'AUTH_URL'
    value: 'https://auth.ordermentum-sandbox.com'
  publicRoutesEnabled:
    name: 'RETAILER_ROUTES_ENABLED'
    value: 'true'
  indexingEnabled:
    name: 'INDEXING_ENABLED'
    value: 'true'
  elasticSearchUser:
    name: 'ELASTICSEARCH_USER'
    value: 'elastic'
  elasticSearchPass:
    name: 'ELASTICSEARCH_PASS'
    value: 'changeme'
  elasticSearchPrefix:
    name: 'ELASTICSEARCH_PREFIX'
    value: 'testing'
  finstroUpfrontEnabled:
    name: 'FINSTRO_UPFRONT_ENABLED'
    value: 'true'
  enabledWorkers:
    name: ENABLED_WORKERS
    value: 'temporal,sqs,database,kafka'
  temporalQueues:
    name: TEMPORAL_QUEUES
    value: 'payments:upfront-payment,payments:generic,payments:promotional-payments,payments:wallet-transfer'
externalSecrets:
  - AUTH_PASS
  - AUTH_USER
  - DATABASE_URI
  - OM_USER_ID
  - OM_FUNDING_USER_ID
  - OM_HOLDING_USER_ID
  - OM_REFUND_USER_ID
  - PP_TOKEN
  - AP_CLIENT_ID
  - AP_CLIENT_SECRET
  - AP_CLIENT_SCOPE
  - PP_USER
  - SLACK_TOKEN
  - MANDRILL_TOKEN
  - SENTRY_DSN
  - STRIPE_SECRET_KEY
  - STRIPE_SIGNING_SECRET
  - INTEGRATION_WEBHOOK
  - KAFKA_BOOTSTRAP_SERVERS
  - ASAP_PUBLIC_KEY_ID
  - ASAP_PRIVATE_KEY
  - OM_WALLET_USER_ID
  - FEES_ACCOUNT_ID
  - REDIS_PASSWORD
  - OM_FINSTRO_USER_ID
  - FINSTRO_SDK_TOKEN
  - JWT_SECRET
mixedHPA:
  enabled: true
  spec:
    pollingInterval: 15
    minReplicaCount: 1
    triggers:
      - type: temporal
        taskQueue: "payments:wallet-transfer"
        endpoint: "account-sandbox.qefiw.tmprl.cloud:7233"
        namespace: "account-sandbox.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: temporal
        taskQueue: "payments:generic"
        endpoint: "account-sandbox.qefiw.tmprl.cloud:7233"
        namespace: "account-sandbox.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: temporal
        taskQueue: "payments:upfront-payment"
        endpoint: "account-sandbox.qefiw.tmprl.cloud:7233"
        namespace: "account-sandbox.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: temporal
        taskQueue: "payments:promotional-payments"
        endpoint: "account-sandbox.qefiw.tmprl.cloud:7233"
        namespace: "account-sandbox.qefiw"
        queueTypes: "workflow,activity"
        targetQueueSize: 1
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BATCH'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BATCH-CHECK'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BATCH-DISBURSEMENT'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BATCH-NOTIFICATION'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BATCH-TRANSACTIONS'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_BULK-TRANSACTION-CHECK'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_DISBURSEMENT'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_NOTIFICATION'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_PAYMENT-RUN-CREATE'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_REFUND-CHECK'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_TEST'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_TESTING_TRANSACTION-CHANGED'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_TRANSACTION-CHECK'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_TRANSACTIONS-SYNC'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_WALLET-CHECK'
        awsRegion: 'us-east-1'
        queueLength: '300'
      - type: aws-sqs-queue
        queueURL: 'https://sqs.us-east-1.amazonaws.com/************/TESTING_PAYMENTS_WEBHOOK'
        awsRegion: 'us-east-1'
        queueLength: '300'
secretsManagerSecretName: payments
deployment:
  podLabels:
    tags.datadoghq.com/env: sandbox
    tags.datadoghq.com/service: payments-job
  additionalPodAnnotations:
    # Datadog annotations for OpenMetrics autodiscovery
    ad.datadoghq.com/payments-job.checks: |
      {
        "openmetrics": {
          "init_config": {},
          "instances": [
            {
              "openmetrics_endpoint": "http://%%host%%:%%port%%/metrics",
              "namespace": "temporal",
              "metrics": [".*"]
            }
          ]
        }
      }
  command:
    - yarn
    - run
    - jobs
vpa:
  enabled: true
