#!/bin/bash
set -exo pipefail

list_deployment_pods() {
  kubectl get pods \
    --namespace "${KUBE_NAMESPACE}" \
    --selector="release=${HELM_RELEASE_NAME}" \
    --output=wide
}
trap list_deployment_pods EXIT

REPO_NAME="$(basename -s .git $(git remote get-url origin))"
HELM_RELEASE_NAME=payments
HELM_CHART=ordermentum/service

export AUTH_API_KEY=$(kubectl get secret ${HELM_VALUES}-auth --namespace ${KUBE_NAMESPACE} -o jsonpath="{.data.API_KEY}" | base64 -d)
export AUTH_SVC_NAME=$(kubectl get svc --namespace ${KUBE_NAMESPACE} --selector="app=auth" -o jsonpath="{.items[0].metadata.name}")
export AUTH_CLUSTER_PORT=$(kubectl get svc --namespace ${KUBE_NAMESPACE} --selector="app=auth" -o jsonpath="{.items[0].spec.ports[0].port}")
export AUTH_URL="http://:${AUTH_API_KEY}@${AUTH_SVC_NAME}.${KUBE_NAMESPACE}:${AUTH_CLUSTER_PORT}"

helm upgrade "${HELM_RELEASE_NAME}" "${HELM_CHART}" \
--namespace "${KUBE_NAMESPACE}" \
--values ./helm/values-api-${HELM_VALUES}.yaml \
--set image.tag="${OM_DOCKER_TAG}" \
--set appRepoName="${REPO_NAME}" \
--set env.authUrl.value="${AUTH_URL}" \
--set autoscaling.enabled=true \
--set-string env.datadogServiceVersion.value="${BUILDKITE_COMMIT:0:8}" \
--install \
--wait 

HELM_CHART=ordermentum/service

HELM_RELEASE_NAME=payments-job
helm upgrade "${HELM_RELEASE_NAME}" "${HELM_CHART}" \
--namespace "${KUBE_NAMESPACE}" \
--values ./helm/values-job-${HELM_VALUES}.yaml \
--set image.tag="${OM_DOCKER_TAG}" \
--set appRepoName="${REPO_NAME}" \
--set-string env.datadogServiceVersion.value="${BUILDKITE_COMMIT:0:8}" \
--install \
--wait 

HELM_RELEASE_NAME=payments-make-payment-workflow-job
helm upgrade "${HELM_RELEASE_NAME}" "${HELM_CHART}" \
--namespace "${KUBE_NAMESPACE}" \
--values ./helm/values-make-payment-workflow-job-${HELM_VALUES}.yaml \
--set image.tag="${OM_DOCKER_TAG}" \
--set appRepoName="${REPO_NAME}" \
--set-string env.datadogServiceVersion.value="${BUILDKITE_COMMIT:0:8}" \
--install \
--wait 

echo "👌"
