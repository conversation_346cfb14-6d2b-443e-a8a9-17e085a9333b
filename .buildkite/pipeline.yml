steps:
  - name: Test
    command: bin/wait.sh auth:3000 -- .buildkite/test.sh
    if: build.message !~ /skip tests/
    plugins:
      - ecr#v2.9.0:
          login: true
          retries: 3
          account_ids: "************"
          region: ap-southeast-2
      - docker-compose#v5.10.0:
          run: test
          pull:
            - auth
          config: docker-compose-build.yml

  - name: ':eslint:'
    command: .buildkite/eslint.sh
    if: build.message !~ /skip tests/
    plugins:
      - ecr#v2.9.0:
          login: true
          retries: 3
          account_ids: "************"
          region: ap-southeast-2
      - docker-compose#v5.10.0:
          run: test
          pull:
            - auth
          config: docker-compose-build.yml

  - name: ':typescript: Typecheck'
    if: build.message !~ /skip tests/
    command: .buildkite/typecheck.sh
    plugins:
      - ecr#v2.9.0:
          login: true
          retries: 3
          account_ids: "************"
          region: ap-southeast-2
      - docker-compose#v5.10.0:
          run: test
          pull:
            - auth
          config: docker-compose-build.yml

  - wait

  - block: Deploy to Sandbox
    branches: '!develop !main !master'

  - name: ':package:'
    command: .buildkite/package.sh


  - block: ':shipit: Release'
    branches: main


  - name: ':aws: Deploy AWS resources'
    command: |
      VALUES_YAML_FILE_PATH=helm/values-api-$${HELM_VALUES}.yaml \
        deploy-application-resources
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - name: ':kubernetes: :postgres: Database Migrations'
    command: .buildkite/migrate-helm.sh db
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - name: ':kubernetes: Indexer Migrations'
    command: .buildkite/migrate-helm.sh indexer
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - name: ':kubernetes: :kafka: Topic Migrations'
    command: .buildkite/migrate-helm.sh topics
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - name: ':kubernetes: Rotate ASAP keys'
    command: rotate-keys --service payments
    env:
      WITH_DEPLOY_CREDENTIALS: true

  - wait

  - name: ':kubernetes: Deploy'
    command: .buildkite/deploy-helm.sh
    env:
      WITH_DEPLOY_CREDENTIALS: true

