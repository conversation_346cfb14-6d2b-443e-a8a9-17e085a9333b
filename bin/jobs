#!/usr/bin/env node
/* eslint-disable no-console */
/* eslint-disable import/no-dynamic-require */

/* eslint-disable global-require */
/* eslint-disable import/no-unresolved */
/* eslint-disable prefer-destructuring */
/* eslint-disable import/no-extraneous-dependencies */
let baseDirectory;

if (['testing', 'production'].includes(process.env.NODE_ENV)) {
  baseDirectory = `../build`;
} else {
  require('ts-node').register({ transpileOnly: true });
  baseDirectory = `../src`;
}

const { start } = require(`${baseDirectory}/start_consumers`);
const { logger } = require(`${baseDirectory}/lib/logger`);

(async function () {
  try {
    await start();
  } catch (error) {
    logger.error(error);
    process.exit(1);
  }
})();
