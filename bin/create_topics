#!/usr/bin/env node

if (!process.env.NODE_ENV) process.env.NODE_ENV = 'development';

let steveoKafka;
let steveoSQS;

if (['development', 'test'].includes(process.env.NODE_ENV)) {
  require('ts-node').register({ transpileOnly: true });
  steveoKafka = require('../src/lib/steveo/steveo_kafka').default;
  steveoSQS = require('../src/lib/steveo/steveo').default;
  require('../src/tasks');
} else {
  steveoKafka = require('../build/lib/steveo/steveo_kafka').default;
  steveoSQS = require('../build/lib/steveo/steveo').default;
  require('../build/tasks');
}

async function start() {
  const runners = [
    steveoSQS.runner(),
    process.env.NODE_ENV === 'test' ? null : steveoKafka.runner(),
  ].filter(Boolean);
  for (const runner of runners) {
    await runner.createQueues();
    console.log(runner.config.engine, ' done');
  }
  console.log('Done');
  process.exit();
}

start().catch(e => {
  console.error(e);
  process.exit(1);
});
