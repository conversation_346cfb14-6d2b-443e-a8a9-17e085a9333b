export NODE_ENV=test
export DATABASE_URI=postgres://ordermentum:@localhost/ordermentum_payments_testing
export DATABASE_INITIALIZATION_URI=postgres://postgres@localhost/template1
export REDIS_URL=localhost
export PORT=4000
export LOG_LEVEL=info
export AUTH_USER=test
export AUTH_PASS=pass
export DISABLE_AUTH=false
export PP_USER=<EMAIL>
export PP_TOKEN=OWEyNWZkZDg5MjZmOTcwYjUzOTJlMGU2MGU1NTMyOGI=
export STRIPE_SECRET_KEY=sk_test_I0SzY3cdEAPI1Cz3GJUugONV00Q7GMPNct
export INTEGRATION_WEBHOOK=http://localhost:3001
export PP_SANDPIT=true
export SKIP_BACKENDS=false
export STEVEO_ENGINE=sqs
export CALLBACK_URI=https://ordermentum-payment.loca.lt
export POSSIBLE_FAILURE_DAYS=4
export OM_USER_ID=01DDC48A-77C6-4A52-BFB9-14D2B6894C2D
export OM_HOLDING_USER_ID=e1e927a9-dcce-493e-9db3-154ee2af69e9
export OM_FUNDING_USER_ID=72d4a432-02a5-1ac8-1b66-46a6d156c2e2
export OM_REFUND_USER_ID=b918eaed-e2a0-1017-36d8-4f5f07196160
export OM_WALLET_USER_ID=e84b6979-29ad-4afc-af00-61873dab0354
export OM_FINSTRO_USER_ID=9D31186B-9B15-45C6-A022-B0B672845E1B
export FEES_ACCOUNT_ID=BAEAB5E8-6C93-4FF3-8842-F801FB327E84
export DEFAULT_JOB_LAG=5000
export JOB_ENQUEUE_LIMIT=1
export AWS_REGION=us-east-1
export AWS_BUCKET_NAME=exports.ordermentum.io
export S3_HOST=http://localhost:4566
export LOCALSTACK_HOST=http://localhost:4566
export AP_CLIENT_ID=6jng5nsvelhkf5v4eenqfof4bp
export AP_CLIENT_SCOPE=im-au-03/11fe0200-b1a8-471a-840d-a031d3924c3f:08e7f6ef-b667-4749-9112-450c7b6944f7:3
export AP_CLIENT_SECRET=1g3qp0ksp8q48qotrv689k8c4cp140s795bm9cfad92sk5n0hau2
export LOCALSTACK_UP_ENDPOINT=http://127.0.0.1:4566
export KAFKA_BOOTSTRAP_SERVERS=localhost:9092
export DISABLE_STEVEO_HEALTH=false
export KAFKA_DEFAULT_PARTITIONS=1
export OM_BASE_URL=http://localhost:3001
export DOUBLE_ENTRY_JOURNAL=true
export FINSTRO_API_URL=https://coreapi.sit-au.finstro.com/api/SDK
export FINSTRO_SDK_TOKEN=REPLACE_ME # replace with value in aws secrets manager
export INSIGHTS_URL=http://localhost:36382
export AUTH_URL=**************************
export JWT_SECRET=testsecret
export RETAILER_ROUTES_ENABLED=true
export INDEXING_ENABLED=true
export ELASTICSEARCH_HOST=http://localhost:9200
export ELASTICSEARCH_USER=elastic
export ELASTICSEARCH_PASS=changeme
export ELASTICSEARCH_PREFIX=development
export FINSTRO_UPFRONT_ENABLED=true
export TEMPORAL_DISABLE_TLS=true
