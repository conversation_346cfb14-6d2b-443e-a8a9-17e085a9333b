version: '2'
services:
  common:
    build:
      dockerfile: Dockerfile.testing
      context: .
      args:
        - NPM_AUTH_TOKEN=${NPM_CI_TOKEN}
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    ports:
      - '4000:4000'
    environment:
      - LOG_LEVEL=debug
      - PORT=4000
      - AUTH_USER=test
      - AUTH_PASS=pass
      - STEVEO_ENGINE=redis
      - REDIS_URL=redis
      - DISABLE_AUTH=false
      - DEFAULT_JOB_LAG=5000
      - DATABASE_INITIALIZATION_URI=postgres://postgres@postgresql/template1
      - POSSIBLE_FAILURE_DAYS=4
      - DISABLE_STEVEO_HEALTH=true
      - DOUBLE_ENTRY_JOURNAL=true
      - RETAILER_ROUTES_ENABLED=true
  test:
    extends: common
    links:
      - postgresql
      - redis
      - auth
    depends_on:
      - postgresql
      - redis
      - auth
    environment:
      - NODE_ENV=test
      - DATABASE_URI=postgres://ordermentum@postgresql/ordermentum_payments_testing
      - SKIP_BACKENDS=true
      - CALLBACK_URI=https://example.com/
      - OM_BASE_URL=http://localhost:3001
      - INSIGHTS_URL=http://localhost:3002
      - NOTIFICATIONS_URL=http://localhost:32308
      - PP_SANDPIT=true
      - STEVEO_ENGINE=redis
      - REDIS_URL=redis
      - PP_USER=<EMAIL>
      - PP_TOKEN=MWU3YjE3NGE0ODAyZWMxMTJkYjY4MDg5NWU2ZmE0ZmU=
      - STRIPE_SECRET_KEY=sk_test_I0SzY3cdEAPI1Cz3GJUugONV00Q7GMPNct
      - INTEGRATION_WEBHOOK={INTEGRATION_WEBHOOK}
      - OM_USER_ID=01DDC48A-77C6-4A52-BFB9-14D2B6894C2D
      - OM_REFUND_USER_ID=5D0F11F8-3632-4011-8480-B05B658FA2FD
      - OM_WALLET_USER_ID=e84b6979-29ad-4afc-af00-61873dab0354
      - OM_FINSTRO_USER_ID=9D31186B-9B15-45C6-A022-B0B672845E1B
      - FINSTRO_API_URL=https://coreapi.sit-au.finstro.com/api/SDK
      - FINSTRO_SDK_TOKEN=REPLACE_ME
      - AUTH_URL=*********************
      - FEES_ACCOUNT_ID=BAEAB5E8-6C93-4FF3-8842-F801FB327E84
      - CI=${CI}
      - BUILDKITE=${BUILDKITE}
      - CODECOV_TOKEN=${CODECOV_TOKEN}
      - BUILDKITE_REPO=${BUILDKITE_REPO}
      - BUILDKITE_BRANCH=${BUILDKITE_BRANCH}
      - BUILDKITE_TAG=${BUILDKITE_TAG}
      - BUILDKITE_COMMIT=${BUILDKITE_COMMIT}
      - BUILDKITE_JOB_ID=${BUILDKITE_JOB_ID}
      - BUILDKITE_BUILD_ID=${BUILDKITE_BUILD_ID}
      - BUILDKITE_BUILD_URL=${BUILDKITE_BUILD_URL}
      - BUILDKITE_AGENT_NAME=${BUILDKITE_AGENT_NAME}
      - AWS_REGION=ap-southeast-2
      - BUILDKITE_COMMAND=${BUILDKITE_COMMAND}
      - BUILDKITE_MESSAGE=${BUILDKITE_MESSAGE}
      - BUILDKITE_TIMEOUT=${BUILDKITE_TIMEOUT}
      - BUILDKITE_BUILD_NUMBER=${BUILDKITE_BUILD_NUMBER}
      - BUILDKITE_ORGANIZATION_SLUG=${BUILDKITE_ORGANIZATION_SLUG}
      - BUILDKITE_PIPELINE_SLUG=${BUILDKITE_PIPELINE_SLUG}
      - BUILDKITE_PIPELINE_PROVIDER=${BUILDKITE_PIPELINE_PROVIDER}
      - BUILDKITE_PIPELINE_DEFAULT_BRANCH=${BUILDKITE_PIPELINE_DEFAULT_BRANCH}
      - BUILDKITE_PULL_REQUEST=${BUILDKITE_PULL_REQUEST}
      - BUILDKITE_ARTIFACT_PATHS=${BUILDKITE_ARTIFACT_PATHS}
      - BUILDKITE_BUILD_CREATOR=${BUILDKITE_BUILD_CREATOR}
      - BUILDKITE_BUILD_CREATOR_EMAIL=${BUILDKITE_BUILD_CREATOR_EMAIL}
      - BUILDKITE_CLEAN_CHECKOUT=${BUILDKITE_CLEAN_CHECKOUT}
      - BUILDKITE_BUILD_CHECKOUT_PATH=${BUILDKITE_BUILD_CHECKOUT_PATH}
      - BUILDKITE_BUILD_CREATOR_TEAMS=${BUILDKITE_BUILD_CREATOR_TEAMS}
      - INDEXING_ENABLED=true
      - ELASTICSEARCH_HOST=http://elasticsearch:9200
      - ELASTICSEARCH_USER=elastic
      - ELASTICSEARCH_PASS=changeme
      - ELASTICSEARCH_PREFIX=testing
      - FINSTRO_UPFRONT_ENABLED=true
  auth:
    image: 581666996624.dkr.ecr.ap-southeast-2.amazonaws.com/ordermentum/auth:develop
    environment:
      DATABASE_URI: postgres://ordermentum@postgresql/auth_development
      BOOTSTRAP_AUTH: 'true'
      STORAGE: database
      PORT: 3000
      JWT_SECRET: testsecret
      API_KEY: hai
      REDIS_URL: redis
      ENABLE_DB: 1
      DATABASE_INITIALIZATION_URI: postgres://postgres@postgresql/template1
      PERMS_FROM_TABLE: 'true'
      NODE_ENV: 'testing'
    depends_on:
      - postgresql
      - redis
      - elasticsearch
    links:
      - postgresql
      - redis
      - elasticsearch
  redis:
    image: redis
    ports:
      - '6379:6379'
  postgresql:
    image: postgres:9.6
    command: postgres
    environment:
      - POSTGRES_HOST_AUTH_METHOD=trust
    volumes:
      - ./config/database:/docker-entrypoint-initdb.d
    ports:
      - '5432:5432'
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.29
    environment:
      - "http.host=0.0.0.0"
      - "transport.host=0.0.0.0"
      - "discovery.type=single-node"
      - cluster.name=payments-cluster
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ulimits:
      nproc: 65535
      nofile:
        soft: 20000
        hard: 65536
    ports:
      - "9200"
