export NODE_ENV=test
export DATABASE_URI=postgres://ordermentum:@localhost/ordermentum_payments_testing
export DATABASE_INITIALIZATION_URI=postgres://postgres@127.0.0.1/template1
export FINSTRO_API_URL=https://coreapi.sit-au.finstro.com/api/SDK
export TEMPORAL_DISABLE_TLS=true
export AWS_ACCESS_KEY=test
export AWS_SECRET_ACCESS_KEY=test
export LOCALSTACK_HOST=http://localhost:4566
export LOCALSTACK_UP_ENDPOINT=http://localhost:4566
