# Payments

[![Build status](https://badge.buildkite.com/4b934c4a104fc977cbb731864212fe3fe1e044ea629baf3775.svg)](https://buildkite.com/ordermentum/payments)

Payment gateway abstraction service for the Ordermentum platform.

## Design

The payments service is designed to abstract across multiple payment providers. ie: if the business wants to add <PERSON><PERSON> as a payment provider, they just get added as an additional backend and the API of the Payments service doesn't change.

For this reason, Promisepay (now ~Assembly Payments~ Zai) is treated as an arms-length plugin. When it comes time to add a new payment provider, create a new folder in `src/lib/backends/` and go from there. You'll need to implement the methods the controllers call, and potentially the webhook methods as well depending on the semantics of your new provider.

The control flow is that each entity in the system is assigned a backend at the time of creation. At the moment this is just `backend = 'promisepay'` because it's the only one. In the future, that may be a decision based on geography, relative pricing or other factors. When that logic needs to be added, it should first go in the controllers, then split out in a separate lib if necessary.

The option also exists to, say, mirror all bank payment methods on Promisepay and NewPaymentService, then decide which of them to use for a given Transaction at the time the Transaction is created. The world is your oyster!

### Callbacks

Promisepay has a system of webhooks which it calls Callbacks. They are fired only once. If anything goes wrong, they are never retried. This may change in the future.

There are some helper scripts in `/bin` to assist with the process of unsetting callback URLs since it's a bit of a palaver.

When the service is started it will attempt to ensure that the callback URLs specified in the environment are what is actually set in Promisepay. If they differ, it will complain on `logger.error`.

If they differ, you need to clear out the old ones. This isn't done automatically in case a process with an old set of conf values happens to run. Just run `./bin/promisepay_clear_webhoooks` and restart an instance of the service to have it re-set the correct URLs. There's no way to have it have two sets of callback URLs simultaneously, eg: while you transition, because Promisepay.

### Communicating with external services

Communication with external services for payments is done by publishing events to the Steveo message queues, where the external system will pick them up and process them in its own time.

### Robust workflows

Robust workflow execution is handled by Temporal.

Please familiarise yourself with the Temporal way of writing workflows idempotent activities.

## Setup local environment

### Execution profiles

We currently have two "execution profiles" `test` and `local`.

These profiles are not be confused with environments such as `sandbox` etc. Instead they exist to delineate between different datastores so local test data for a user can be kept distinct from test runner data.


### Initialise the environment

When the services first start they will need to be initialised & have seed data inserted into the stores

```bash
# Setup message queue topics and run db schema migration
yarn db:setup

```

## Tests

### Test card data

When using the Zai pre-live environment in tests you can use different card data to invoke different behaviour. See the [WorldPay test card data](https://developer.worldpay.com/products/access/card-payments/testing#cvc-test-values).

### Running local tests

The main test execution profile is a mixture of unit and integration tests

```bash
source .env-test && yarn test
```

### End to end (E2E) tests

End to end tests provide an added layer of test resilience by testing from the API to the test environments of the payment gateways.

To run the tests the API & job processing services must be started with alternate environment configurations that tell the payment gateway (e.g. Zai) to use the pre-live test environment.

```bash
source .env-test

# Start the API
yarn start:e2e

# Start the jobs & temporal processor
yarn job:e2e

# Run the tests
yarn test:e2e
```

### Zai endpoint testing 

Zai endpoint tests can be run independently of the payment service code with the [REST Client](https://marketplace.visualstudio.com/items?itemName=humao.rest-client) VSCode extension.

Find tests in [http/zai.http](http/zai.http)


## Holding account user

You will need to create a holding account locally

```
INSERT INTO "public"."users"("id","first_name","last_name","email","address_line_1","address_line_2","city","state","postcode","country","backend","mobile_number","dob","settlement_webhook","company_name","company_tax_number","company_address_line_1","company_address_line_2","company_city","company_state","company_postcode","company_country","created_at","updated_at","deleted_at","dd_settlement_delay_hours","batch_settlement_payments","company_legal_name","ordermentum_id","external_id","status","configuration","stripe_customer_id","payment_method_id","payment_method_type")
VALUES
(E'4071918A-98B3-4AC1-9CAF-9EA39FA5BA40',E'Holding Account','Holding Account',E'<EMAIL>',E'44 Sarah Crescent',E'',E'West Summer',E'Australian Capital Territory',E'0977',E'AUS',E'promisepay',E'+***************',NULL,E'',E'Dickinson - Durgan Trading',NULL,E'44 Sarah Crescent',E'',E'West Summer',E'Australian Capital Territory',E'0977',E'AUS',E'2024-12-05 22:37:41.447+00',E'2024-12-06 08:03:54.304+00',NULL,40,FALSE,E'Dickinson - Durgan Legal','4071918A-98B3-4AC1-9CAF-9EA39FA5BA40',E'4071918A-98B3-4AC1-9CAF-9EA39FA5BA40',E'active',E'{"supplier": false, "netSettlement": false, "manualDisbursement": false}',NULL,NULL,NULL);
```
LEGACY......
Someone should do a seed script or something
