/* eslint-disable camelcase */
import { expect } from 'chai';
import { randomUUID } from 'crypto';
import { v4 as uuid } from 'uuid';
import { SinonSandbox, SinonStub, createSandbox } from 'sinon';
import config from 'config';
import moment from 'moment';
import { createAsapTestHeader } from '../helpers/asap_authentication';
import { app, auth } from '../helper';
import FixtureFactory from '../fixtures/transaction';
import { mockTransaction } from '../fixtures/http/transaction_fixture';
import { PaymentMethod, Job, User, Transaction } from '../../src/models';
import { Backend } from '../../src/models/transaction';
import { client } from '../../src/lib/backends/promisepay';
import * as userWalletAccount from '../../src/lib/backends/promisepay/user_wallet_account';
import { systemConfig } from '../../src/system_config';
import { finstroClient } from '../../src/lib/backends/finstro/client';
import { finstroInvoicePaymentTask } from '../../src/tasks/finstro_invoice_payment_task';

const OM_FINSTRO_USER_ID = config.get<string>('OM_FINSTRO_USER_ID');

describe('/v1/transactions', () => {
  let fixture;
  let sandbox: SinonSandbox;
  let providerWalletAccountStub: SinonStub;
  let finstroClientPostStub: SinonStub;

  beforeEach(async () => {
    fixture = await (await FixtureFactory()).instance();
    systemConfig.SKIP_BACKENDS = false;
    sandbox = createSandbox();
    sandbox
      .stub(client.items, 'makePayment')
      // @ts-expect-error
      .resolves({ items: { state: 'completed' } });
    sandbox.stub(client.items, 'createItem').resolves({});
    sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
    sandbox.stub(client.items, 'showItem').resolves({});
    systemConfig.DOUBLE_ENTRY_JOURNAL = true;
    sandbox.spy(Job, 'create');
    finstroClientPostStub = sandbox.stub(finstroClient, 'post');
    await User.findOrCreate({
      where: {
        id: OM_FINSTRO_USER_ID,
      },
      // @ts-ignore
      defaults: {
        id: OM_FINSTRO_USER_ID,
        ordermentum_id: randomUUID(),
        first_name: '',
        last_name: '',
        email: '',
        address_line_1: '',
        city: '',
        state: '',
        postcode: '',
        country: '',
        backend: Backend.PROMISEPAY,
      },
    });
  });
  afterEach(() => {
    systemConfig.DOUBLE_ENTRY_JOURNAL = false;
    systemConfig.SKIP_BACKENDS = true;
    sandbox.restore();
  });

  describe('GET', () => {
    it('must return a 200', async () => {
      const response = await app
        .get('/v1/transactions')
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
    });

    it('must return an array', async () => {
      const response = await app
        .get('/v1/transactions')
        .set('Authorization', auth);
      expect(response.status).to.equal(200);
      expect(Array.isArray(response.body));
    });

    it('must return a transaction', async () => {
      const response = await app
        .get('/v1/transactions')
        .set('Authorization', auth);
      expect(response.body.length).to.be.gt(0);
    });

    it('must limit transactions', async () => {
      // On initial setup of payments, this test case will always
      // return 1 transaction and will fail but on succeeding run
      // this test will not fail calling transaction factory here
      // just to make sure.
      await (await FixtureFactory()).instance();
      const response = await app
        .get('/v1/transactions')
        .query({ per_page: 2 })
        .set('Authorization', auth);

      expect(response.body.length).to.equal(2);
    });

    it('must filter transactions by invoiceId', async () => {
      await (await FixtureFactory()).instance();
      const response = await app
        .get('/v1/transactions')
        .query({ filter: { invoiceId: fixture.invoiceIds.at(0) } })
        .set('Authorization', auth);
      expect(response.body.length).to.equal(1);
    });

    it('must filter transactions by invoiceId and state', async () => {
      const second = await (await FixtureFactory()).instance();
      await second.update({
        state: Transaction.states.failed,
        invoiceIds: fixture.invoiceIds,
      });

      const response = await app
        .get('/v1/transactions')
        .query({
          filter: { state: 'failed', invoiceId: fixture.invoiceIds.at(0) },
        })
        .set('Authorization', auth);
      expect(response.body.length).to.equal(1);
    });

    it('must filter transactions invoiceId', async () => {
      const second = await (await FixtureFactory()).instance();
      await second.update({
        invoiceIds: fixture.invoiceIds,
      });

      const response = await app
        .get('/v1/transactions')
        .query({
          filter: { invoiceId: fixture.invoiceIds.at(0) },
        })
        .set('Authorization', auth);
      expect(response.body.length).to.equal(2);
    });

    it('must order transactions', async () => {
      const response = await app
        .get('/v1/transactions')
        .query({ sort: '-id' })
        .set('Authorization', auth);

      const copied = Object.assign([], response.body);
      // One day I will remember that JS sort is in place
      // _before_ I spend 5 minutes debugging this test.
      // One day...
      const sorted = copied.sort((a, b) => {
        if (a.id < b.id) return 1;
        if (b.id < a.id) return -1;
        return 0;
      });
      expect(response.body).to.eql(sorted);
    });

    describe('times', () => {
      it('must respect updated_since', async () => {
        const now = moment();

        await (await FixtureFactory()).instance();
        await fixture.reload();

        const response = await app
          .get('/v1/transactions')
          .query({
            updated_since: now.toISOString(),
          })
          .set('Authorization', auth);
        expect(response.body.length).to.gte(1);
      });
    });
  });

  describe('GET/:id', () => {
    it('should get the right transaction', async () => {
      const response = await app
        .get(`/v1/transactions/${fixture.id}`)
        .set('Authorization', auth);
      expect(response.body.id).to.equal(fixture.id);
    });

    it('should allow asap calls on transaction', async () => {
      const response = await app
        .get(`/v1/transactions/${fixture.id}`)
        .set('Authorization', createAsapTestHeader());
      expect(response.body.id).to.equal(fixture.id);
    });

    it('should return 404 for a missing transaction', async () => {
      const response = await app
        .get(`/v1/transactions/${uuid()}`)
        .set('Authorization', auth);

      expect(response.status).to.equal(404);
    });
  });

  describe('POST', () => {
    it('should reject an incomplete body with a 400', async () => {
      const response = await app
        .post('/v1/transactions')
        .send({})
        .set('Authorization', auth);
      expect(response.status).to.equal(400);
    });

    it('should reject a body with an invalid id', async () => {
      const fixtureData = await mockTransaction();
      fixtureData.id = 'oh hai';
      const response = await app
        .post('/v1/transactions')
        .send(fixtureData)
        .set('Authorization', auth);
      expect(response.status).to.equal(400);
    });

    it('should accept a valid body', async () => {
      const fixtureData = await mockTransaction();
      const response = await app
        .post('/v1/transactions')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(response.status).to.equal(200);
    });

    it('should return the persisted data', async () => {
      const fixtureData = await mockTransaction();
      const response = await app
        .post('/v1/transactions')
        .send(fixtureData)
        .set('Authorization', auth);

      expect(response.body.name).to.eql(fixtureData.name);
    });

    it('should accept a valid finstro transaction request, without using wallet', async () => {
      const transactionRequestPayload: any = await mockTransaction();

      // set up a finstro payment method and use it's values in the request payload
      const paymentMethodId = randomUUID();
      const customerId = randomUUID();
      await PaymentMethod.create({
        id: paymentMethodId,
        backend: Backend.FINSTRO,
        details: {
          customerId,
        },
        accountId: transactionRequestPayload.buyer_id,
        type: 'trade-account',
      });

      transactionRequestPayload.payment_method = {
        id: paymentMethodId,
        type: 'trade-account',
      };
      transactionRequestPayload.bank_payment_method_id = null;

      providerWalletAccountStub = sandbox.stub(
        userWalletAccount,
        'userWalletAccount'
      );

      const finstroTaskStub = sandbox
        .stub(finstroInvoicePaymentTask, 'publish')
        .resolves();

      // client post mock covers both /FetchCustomerByUserId/${customerId} and /SendInvoice requests - which is why we'll give back different responses at different times
      finstroClientPostStub
        .onFirstCall()
        .resolves({
          data: {
            data: [
              {
                status: 'ACTIVE',
                accountInfo: { balance: 30000, limit: 40000 },
              },
            ],
          },
        })
        .onSecondCall()
        .resolves({
          data: {
            response: {
              invoice_status: 'APPROVED',
            },
          },
        });

      const response = await app
        .post('/v1/transactions')
        .send(transactionRequestPayload)
        .set('Authorization', auth);

      expect(response.status).to.equal(200);
      expect(finstroTaskStub.callCount).to.equal(1);
      expect(providerWalletAccountStub.callCount).to.equal(0);
    });
  });

  // it('should accept a valid finstro transaction request, using wallet and paying difference', async () => {
  //   const transactionRequestPayload: any = await mockTransaction();

  //   // set up a finstro payment method and use it's values in the request payload
  //   const paymentMethodId = randomUUID();
  //   const customerId = randomUUID();
  //   await PaymentMethod.create({
  //     id: paymentMethodId,
  //     backend: Backend.FINSTRO,
  //     details: {
  //       customerId,
  //     },
  //     accountId: transactionRequestPayload.buyer_id,
  //     type: 'trade-account',
  //   });

  //   transactionRequestPayload.bank_payment_method_id = null;
  //   transactionRequestPayload.payment_method = {
  //     id: paymentMethodId,
  //     type: 'trade-account',
  //   };
  //   transactionRequestPayload.useBuyerWallet = true;
  //   transactionRequestPayload.backend = Backend.FINSTRO;
  //   transactionRequestPayload.amount = 10000;

  //   providerWalletAccountStub = sandbox
  //     .stub(userWalletAccount, 'userWalletAccount')
  //     .onFirstCall()
  //     .resolves({
  //       id: customerId,
  //       balance: 500,
  //     })
  //     .onSecondCall()
  //     .resolves({
  //       id: customerId,
  //       balance: 10000,
  //     });

  //   // client post mock covers both /FetchCustomerByUserId/${customerId} and /SendInvoice requests - which is why we'll give back different responses at different times
  //   finstroClientPostStub
  //     .onFirstCall()
  //     .resolves({
  //       data: {
  //         data: [
  //           {
  //             status: 'ACTIVE',
  //             accountInfo: { balance: 500, limit: 50000 },
  //           },
  //         ],
  //       },
  //     })
  //     .onSecondCall()
  //     .resolves({
  //       data: {
  //         data: [
  //           {
  //             status: 'ACTIVE',
  //             accountInfo: { balance: 500, limit: 50000 },
  //           },
  //         ],
  //       },
  //     })
  //     .onThirdCall()
  //     .resolves({
  //       data: {
  //         response: {
  //           invoice_status: 'APPROVED',
  //         },
  //       },
  //     });

  //   const finstroInvoicePaymentTaskStub = sandbox
  //     .stub(finstroInvoicePaymentTask, 'publish')
  //     .resolves();

  //   const response = await app
  //     .post('/v1/transactions')
  //     .send(transactionRequestPayload)
  //     .set('Authorization', auth);

  //   expect(response.status).to.equal(200);
  //   expect(finstroInvoicePaymentTaskStub.callCount).to.equal(0); // this task isn't needed for upfront payments
  //   expect(providerWalletAccountStub.callCount).to.equal(2);
  //   expect(finstroClientPostStub.callCount).to.equal(3);
  // });

  it('should accept a valid finstro transaction request, using wallet with more than enough funds', async () => {
    const transactionRequestPayload: any = await mockTransaction();

    // set up a finstro payment method and use it's values in the request payload
    const paymentMethodId = randomUUID();
    const customerId = randomUUID();
    await PaymentMethod.create({
      id: paymentMethodId,
      backend: Backend.FINSTRO,
      details: {
        customerId,
      },
      accountId: transactionRequestPayload.buyer_id,
      type: 'trade-account',
    });

    transactionRequestPayload.bank_payment_method_id = null;
    transactionRequestPayload.payment_method = {
      id: paymentMethodId,
      type: 'trade-account',
    };
    transactionRequestPayload.useBuyerWallet = true;
    transactionRequestPayload.backend = Backend.FINSTRO;
    transactionRequestPayload.amount = 10000;

    providerWalletAccountStub = sandbox
      .stub(userWalletAccount, 'userWalletAccount')
      .onFirstCall()
      .resolves({
        id: customerId,
        balance: 11000,
      });

    // client post mock covers both /FetchCustomerByUserId/${customerId} and /SendInvoice requests - which is why we'll give back different responses at different times
    finstroClientPostStub.onFirstCall().resolves({
      data: {
        data: [
          {
            status: 'ACTIVE',
            accountInfo: { balance: 500, limit: 50000 },
          },
        ],
      },
    });

    const finstroInvoicePaymentTaskStub = sandbox
      .stub(finstroInvoicePaymentTask, 'publish')
      .resolves();

    const response = await app
      .post('/v1/transactions')
      .send(transactionRequestPayload)
      .set('Authorization', auth);

    expect(response.status).to.equal(200);
    expect(finstroInvoicePaymentTaskStub.callCount).to.equal(0); // this task isn't needed for upfront payments
    expect(providerWalletAccountStub.callCount).to.equal(1);
    expect(finstroClientPostStub.callCount).to.equal(1);
  });
});
