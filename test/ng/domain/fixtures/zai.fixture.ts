import sinon from 'sinon';
import { v4 as uuidv4 } from 'uuid';
import {
  DisburseFromWalletToBankAccount,
  GetOrCreatePaymentBody,
} from '../../../../src/ng/common/types/gateway.types';
import {
  ZaiClient,
  ZaiFundTransferGateway,
} from '../../../../src/ng/domain/services/gateways/zai_fund_transfer.gateway';
import logger from '../../../../src/lib/logger';

/**
 * Returns a mock request body for creating a Zai payment item
 * @param overrides
 * @returns
 */
export const zaiItemFixture = (
  overrides: Partial<GetOrCreatePaymentBody> = {}
): GetOrCreatePaymentBody => ({
  id: uuidv4(),
  name: 'Test item',
  amount: 100,
  currency: 'AUD',
  externalBuyerId: 'buyer123',
  externalSellerId: 'seller123',
  feeIds: undefined,
  description: 'Test item',
  taxInvoice: false,
  customDescriptor: 'Test item',
  ...(overrides ?? {}),
});

/**
 * Returns a mock request body for disbursing funds from a Zai wallet to a bank account
 * @param overrides
 * @returns
 */
export const disburseFixture = (
  overrides: Partial<DisburseFromWalletToBankAccount> = {}
): DisburseFromWalletToBankAccount => ({
  fromWallet: {
    accountId: 'wallet123',
    balance: 200,
  },
  amount: 100,
  toBankAccount: {
    bankAccountId: 'bank123',
  },
  descriptor: 'Test disbursement',
  ...(overrides ?? {}),
});

/**
 * The overrides for the Zai client
 */
export type ZaiClientOverrides = {
  accounts?: {
    [key: string]: sinon.SinonStub;
  };
  bankAccounts?: {
    [key: string]: sinon.SinonStub;
  };
  batchTransactions?: {
    [key: string]: sinon.SinonStub;
  };
  fees?: {
    [key: string]: sinon.SinonStub;
  };
  items?: {
    [key: string]: sinon.SinonStub;
  };
  transactions?: {
    [key: string]: sinon.SinonStub;
  };
  users?: {
    [key: string]: sinon.SinonStub;
  };
  walletAccounts?: {
    [key: string]: sinon.SinonStub;
  };
};

/**
 * Returns a mock Zai client
 * @param sandbox
 * @returns
 */
export function zaiClientFixture(
  sandbox: sinon.SinonSandbox,
  overrides: ZaiClientOverrides = {}
): ZaiClientOverrides {
  // NOTE: Only partially mocked, please extend as needed
  return {
    accounts: {
      showAddress: sandbox.stub().resolves({}),
      ...(overrides.accounts ?? {}),
    },
    bankAccounts: {
      showBankAccount: sandbox.stub().resolves({}),
      redactBankAccount: sandbox.stub().resolves({}),
      createBankAccount: sandbox.stub().resolves({}),
      showBankAccountUser: sandbox.stub().resolves({}),
      ...(overrides.bankAccounts ?? {}),
    },
    batchTransactions: {
      listBatchTransactions: sandbox.stub().resolves({}),
      showBatchTransaction: sandbox.stub().resolves({}),
      ...(overrides.batchTransactions ?? {}),
    },
    fees: {
      listFees: sandbox.stub().resolves({}),
      createFee: sandbox.stub().resolves({}),
      showFee: sandbox.stub().resolves({}),
      ...(overrides.fees ?? {}),
    },
    items: {
      listItems: sandbox.stub().resolves({}),
      createItem: sandbox.stub().resolves({}),
      showItem: sandbox.stub().resolves({}),
      deleteItem: sandbox.stub().resolves({}),
      showItemSeller: sandbox.stub().resolves({}),
      showItemBuyer: sandbox.stub().resolves({}),
      showItemFees: sandbox.stub().resolves({}),
      showItemStatus: sandbox.stub().resolves({}),
      showItemWireDetails: sandbox.stub().resolves({}),
      makePayment: sandbox.stub().resolves({}),
      releasePayment: sandbox.stub().resolves({}),
      refund: sandbox.stub().resolves({}),
      ...(overrides.items ?? {}),
    },
    transactions: {
      listTransactions: sandbox.stub().resolves({}),
      showTransactionBankAccount: sandbox.stub().resolves({}),
      showSupplementaryData: sandbox.stub().resolves({}),
      showTransaction: sandbox.stub().resolves({}),
      showTransactionUser: sandbox.stub().resolves({}),
      showTransactionFees: sandbox.stub().resolves({}),
      showTransactionCardAccount: sandbox.stub().resolves({}),
      showTransactionWalletAccount: sandbox.stub().resolves({}),
      ...(overrides.transactions ?? {}),
    },
    users: {
      listUserBPayAccounts: sandbox.stub().resolves({}),
      showUserCardAccount: sandbox.stub().resolves({}),
      showUser: sandbox.stub().resolves({}),
      updateUser: sandbox.stub().resolves({}),
      createUser: sandbox.stub().resolves({}),
      listUsers: sandbox.stub().resolves({}),
      showUserBankAccount: sandbox.stub().resolves({}),
      setUserDisbursementAccount: sandbox.stub().resolves({}),
      showUserWalletAccounts: sandbox.stub().resolves({}),
      ...(overrides.users ?? {}),
    },
    walletAccounts: {
      showWallet: sandbox.stub().resolves({}),
      withdrawFunds: sandbox.stub().resolves({}),
      ...(overrides.walletAccounts ?? {}),
    },
  };
}

/**
 * Returns a mock Zai fund transfer gateway
 * @param sandbox
 * @returns
 */
export function zaiFundTransferGatewayFixture(
  sandbox: sinon.SinonSandbox,
  overrides: ZaiClientOverrides = {}
) {
  const zaiStubs = zaiClientFixture(sandbox, overrides);
  const zaiClient = zaiStubs as unknown as ZaiClient;
  const zaiFundsGateway = new ZaiFundTransferGateway(logger, zaiClient);

  return { zaiFundsGateway, zaiClient, zaiStubs };
}
