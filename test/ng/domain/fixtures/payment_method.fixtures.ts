import { createStubInstance } from 'sinon';
import { v4 as uuid } from 'uuid';
import { Sync } from 'factory.ts';
import { CurrencyCodes } from '../../../../src/ng/common/types/currency.types';
import { PaymentMethodModel } from '../../../../src/models/payment_methods';
import { Backend } from '../../../../src/models/transaction';
import { CardPaymentMethodModel } from '../../../../src/models/card_payment_method';
import { PaymentMethodRepository } from '../../../../src/ng/domain/repositories/payment_method.repository';
import { CardPaymentMethodRepository } from '../../../../src/ng/domain/repositories/card_payment_method.repository';
import { PaymentMethodService } from '../../../../src/ng/domain/services/domain/payment_method.service';
import { logger } from '../../../../src/lib/logger';
import { BankPaymentMethodRepository } from '../../../../src/ng/domain/repositories/bank_payment_method.repository';

export const cardPaymentMethodFactory =
  Sync.makeFactory<CardPaymentMethodModel>({
    id: uuid(),
    name: 'CardPaymentMethod',
    expiry_month: 1,
    expiry_year: 2025,
    last4: '1234',
    currency: CurrencyCodes.USD,
    backend: 'promisepay',
    backend_id: 'spreedly_id',
    is_settlement_account: true,
    issuer: 'issuer',
    user_id: 'userA',
    spreedly_id: 'spreedly_id',
    addedById: 'userA',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted_at: undefined,
  });

export const paymentMethodFactory = Sync.makeFactory<PaymentMethodModel>({
  id: uuid(),
  type: 'trade-account',
  accountId: 'userA',
  backend: Backend.PROMISEPAY,
  details: {},
  currency: CurrencyCodes.USD,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  deletedAt: undefined,
  surcharge: '0',
  name: 'PaymentMethod',
});

/**
 * Mock the PaymentMethodService and its dependencies
 */
export function mockPaymentMethodService() {
  const paymentMethodRepository = createStubInstance(PaymentMethodRepository);
  const bankPaymentMethodRepository = createStubInstance(
    BankPaymentMethodRepository
  );
  const cardPaymentMethodRepository = createStubInstance(
    CardPaymentMethodRepository
  );
  const userId = 'userA';
  const paymentMethodId = '456';

  const paymentMethodService = new PaymentMethodService(
    paymentMethodRepository,
    bankPaymentMethodRepository,
    cardPaymentMethodRepository,
    logger
  );

  return {
    paymentMethodRepository,
    bankPaymentMethodRepository,
    cardPaymentMethodRepository,
    paymentMethodService,
    userId,
    paymentMethodId,
  };
}
