import { v4 as uuidv4 } from 'uuid';
import { Sync } from 'factory.ts';
import { CurrencyCodes } from '../../../../src/ng/common/types/currency.types';
import { Backend } from '../../../../src/models/transaction';
import { BankPaymentMethodModel } from '../../../../src/models/bank_payment_method';

export const bankPaymentMethodFactory =
  Sync.makeFactory<BankPaymentMethodModel>({
    id: uuidv4(),
    name: 'BankPaymentMethod',
    bank_name: 'Bank of America',
    account_name: '<PERSON>',
    addedById: 'userA',
    routing_number: '**********',
    account_number: '**********',
    country: 'US',
    holder_type: 'individual',
    user_id: 'userA',
    account_type: 'checking',
    currency: CurrencyCodes.USD,
    backend: Backend.PROMISEPAY,
    backend_id: 'backend123',
    is_settlement_account: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    deleted_at: undefined,
  });
