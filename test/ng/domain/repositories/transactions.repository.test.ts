import { v4 as uuidv4 } from 'uuid';
import { expect } from 'chai';
import { TransactionsRepository } from '../../../../src/ng/domain/repositories/transactions.repository';
import { transactionFixture } from '../fixtures/transaction.fixture';
import { createUserFixture } from '../fixtures/user.fixture';
import { CurrencyCodes } from '../../../../src/ng/common/types/currency.types';
import { Transaction, User, PaymentMethod } from '../../../../src/models';
import { Backend, states } from '../../../../src/models/transaction';
import { logger } from '../../../../src/lib/logger';
import { createPaymentMethodFixture } from '../fixtures/payment_method.fixture';

describe('TransactionsRepository', () => {
  const repository = new TransactionsRepository(logger);

  describe('getTransactionById', () => {
    it('should get transaction successfully', async () => {
      // Create test users
      const seller = await User.create(createUserFixture());
      const buyer = await User.create(createUserFixture());

      // Create a test transaction
      const transaction = await Transaction.create(
        transactionFixture({
          seller_id: seller.id,
          buyer_id: buyer.id,
        })
      );

      const result = await repository.getTransactionById(transaction.id);

      expect(result).not.to.equal(undefined);
      expect(result?.id).to.equal(transaction.id);
      expect(result?.seller_id).to.equal(seller.id);
      expect(result?.buyer_id).to.equal(buyer.id);
    });

    it('should return null when transaction not found', async () => {
      const nonexistentTransactionId = uuidv4();
      const result = await repository.getTransactionById(
        nonexistentTransactionId
      );
      expect(result).to.equal(null);
    });
  });

  describe('createTransaction', () => {
    it('should create transaction successfully', async () => {
      // Create test users
      const seller = await User.create(createUserFixture());
      const buyer = await User.create(createUserFixture());
      const paymentMethod = await PaymentMethod.create(
        createPaymentMethodFixture(buyer.id)
      );

      const result = await repository.createTransaction(
        transactionFixture({
          amount: 1000,
          description: 'Test transaction',
          buyer_id: buyer.id,
          seller_id: seller.id,
          name: 'Test',
          backend: Backend.PROMISEPAY,
          currency: CurrencyCodes.AUD,
          state: states.pending,
          paymentMethodId: paymentMethod.id,
        })
      );

      expect(result).not.to.equal(undefined);
      expect(result.id).not.to.equal(undefined);
      expect(result.seller_id).to.equal(seller.id);
      expect(result.buyer_id).to.equal(buyer.id);
      expect(result.amount).to.equal(1000);
      expect(result.currency).to.equal(CurrencyCodes.AUD);
      expect(result.description).to.equal('Test transaction');
    });
  });

  // TODO: Add getOrCreateTransaction tests
});
