import { uuid4 } from '@temporalio/workflow';
import { expect } from 'chai';
import { CreateTransactionAttrs } from '../../../../../src/types/payment_types';
import { Backend, states } from '../../../../../src/models/transaction';
import { bankPaymentMethodFactory } from '../../fixtures/bank_payment_method.fixture';
import {
  cardPaymentMethodFactory,
  mockPaymentMethodService,
  paymentMethodFactory,
} from '../../fixtures/payment_method.fixtures';

describe('PaymentMethodService', () => {
  describe('getBackendAccountId', () => {
    it('should select a card payment method as preferred if available', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const mockService = mockPaymentMethodService();
      const expectedCardInstance = cardPaymentMethodFactory.build();
      const inputTransaction: CreateTransactionAttrs = {
        card_payment_method_id: inputPaymentMethodId,
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(mockService.userId, inputPaymentMethodId)
        .resolves(expectedCardInstance);

      // ACT
      const actualAccountId =
        await mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        );

      // ASSERT
      expect(actualAccountId).to.equal(expectedCardInstance.backend_id);
    });

    it('should select a bank payment method if card payment method is not available', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const mockService = mockPaymentMethodService();
      const expectedBankMethod = bankPaymentMethodFactory.build();
      const inputTransaction: CreateTransactionAttrs = {
        bank_payment_method_id: inputPaymentMethodId,
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      mockService.bankPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(mockService.userId, inputPaymentMethodId)
        .resolves(expectedBankMethod);

      // ACT
      const actualAccountId =
        await mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        );

      // ASSERT
      expect(actualAccountId).to.equal(expectedBankMethod.backend_id);
    });

    it('should return wallet payment method id when wallet payment is used', async () => {
      // ARRANGE
      const expectedWalletId = 'wallet_123';
      const mockService = mockPaymentMethodService();
      const inputTransaction: CreateTransactionAttrs = {
        wallet_payment_method_id: expectedWalletId,
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      // ACT
      const actualAccountId =
        await mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        );

      // ASSERT
      expect(actualAccountId).to.equal(expectedWalletId);
    });

    it('should throw error when no payment method is provided', async () => {
      // ARRANGE
      const mockService = mockPaymentMethodService();
      const inputTransaction: CreateTransactionAttrs = {
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      // ACT & ASSERT
      await expect(
        mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        )
      ).to.be.rejectedWith('Invalid Payment Method');
    });

    it('should throw error when bank payment method is not found', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const mockService = mockPaymentMethodService();
      const inputTransaction: CreateTransactionAttrs = {
        bank_payment_method_id: inputPaymentMethodId,
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      mockService.bankPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(mockService.userId, inputPaymentMethodId)
        .resolves(undefined);

      // ACT & ASSERT
      await expect(
        mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        )
      ).to.be.rejectedWith('Bank payment method backend ID is required');
    });

    it('should throw error when card payment method is not found', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const mockService = mockPaymentMethodService();
      const inputTransaction: CreateTransactionAttrs = {
        card_payment_method_id: inputPaymentMethodId,
        seller_id: 'seller123',
        buyer_id: 'buyer123',
        amount: 10000,
        description: 'Test transaction',
        name: 'Test payment',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      };

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(mockService.userId, inputPaymentMethodId)
        .resolves(undefined);

      // ACT & ASSERT
      await expect(
        mockService.paymentMethodService.getBackendAccountId(
          mockService.userId,
          inputTransaction
        )
      ).to.be.rejectedWith('Card payment method backend ID is required');
    });
  });

  describe('selectUpfrontHoldingPaymentMethod', () => {
    it('should return card payment method when available', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const inputUserId = 'user123';
      const inputFinstroEnabled = false;
      const mockService = mockPaymentMethodService();
      const expectedCardMethod = cardPaymentMethodFactory.build();

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(expectedCardMethod);

      // ACT
      const actualResult =
        await mockService.paymentMethodService.selectUpfrontHoldingPaymentMethod(
          inputUserId,
          inputPaymentMethodId,
          inputFinstroEnabled
        );

      // ASSERT
      expect(actualResult.cardPaymentMethod).to.equal(expectedCardMethod);
      expect(actualResult.paymentMethod).to.be.undefined;
    });

    it('should return payment method when card not found and finstro is enabled', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const inputUserId = 'user123';
      const inputFinstroEnabled = true;
      const mockService = mockPaymentMethodService();
      const expectedPaymentMethod = paymentMethodFactory.build();

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(undefined);

      mockService.paymentMethodRepository.getUserPaymentMethod
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(expectedPaymentMethod);

      // ACT
      const actualResult =
        await mockService.paymentMethodService.selectUpfrontHoldingPaymentMethod(
          inputUserId,
          inputPaymentMethodId,
          inputFinstroEnabled
        );

      // ASSERT
      expect(actualResult.paymentMethod).to.equal(expectedPaymentMethod);
      expect(actualResult.cardPaymentMethod).to.be.undefined;
    });

    it('should throw error when no card found and finstro is disabled', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const inputUserId = 'user123';
      const inputFinstroEnabled = false;
      const mockService = mockPaymentMethodService();

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(undefined);

      // ACT & ASSERT
      await expect(
        mockService.paymentMethodService.selectUpfrontHoldingPaymentMethod(
          inputUserId,
          inputPaymentMethodId,
          inputFinstroEnabled
        )
      ).to.be.rejectedWith('Payment method not found');
    });

    it('should throw error when no payment method found and finstro is enabled', async () => {
      // ARRANGE
      const inputPaymentMethodId = uuid4();
      const inputUserId = 'user123';
      const inputFinstroEnabled = true;
      const mockService = mockPaymentMethodService();

      mockService.cardPaymentMethodRepository.getByUserAndPaymentMethodId
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(undefined);

      mockService.paymentMethodRepository.getUserPaymentMethod
        .withArgs(inputUserId, inputPaymentMethodId)
        .resolves(undefined);

      // ACT & ASSERT
      await expect(
        mockService.paymentMethodService.selectUpfrontHoldingPaymentMethod(
          inputUserId,
          inputPaymentMethodId,
          inputFinstroEnabled
        )
      ).to.be.rejectedWith('Payment method not found');
    });
  });
});
