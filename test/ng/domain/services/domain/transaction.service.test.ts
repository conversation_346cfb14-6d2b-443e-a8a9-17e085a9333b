import { v4 as uuidv4 } from 'uuid';
import { expect } from 'chai';
import assert from 'node:assert';
import sinon, { SinonSandbox } from 'sinon';
import { StubbedInstance, stubInterface } from 'ts-sinon';
import { uuid4 } from '@temporalio/workflow';
import { TransactionsRepository } from '../../../../../src/ng/domain/repositories/transactions.repository';
import { CurrencyCodes } from '../../../../../src/ng/common/types/currency.types';
import {
  Backend,
  states,
  TransactionAttributes,
} from '../../../../../src/models/transaction';
import { logger } from '../../../../../src/lib/logger';
import { TransactionService } from '../../../../../src/ng/domain/services/domain/transaction.service';
import { CreateTransactionAttrs } from '../../../../../src/types/payment_types';
import * as transactionController from '../../../../../src/controllers/transactions';
import { JournalTransaction } from '../../../../../src/lib/journal_v2/journal_v2';
import { JournalName } from '../../../../../src/models/journal_v2';
import steveo from '../../../../../src/lib/steveo/steveo';
import { sequelize } from '../../../../../src/models';
import {
  userFactory,
  userRepositoryFactory,
} from '../../../fixtures/user.fixture';
import { UserRepository } from '../../../../../src/ng/domain/repositories/user.repository';
import {
  transactionFactory,
  transactionsRepositoryFactory,
} from '../../../fixtures/transaction.fixture';

describe('TransactionService', () => {
  let sandbox: SinonSandbox;
  let service: TransactionService;
  let transactionsRepository: StubbedInstance<TransactionsRepository>;
  let userRepository: StubbedInstance<UserRepository>;
  let validatePaymentMethodStub: sinon.SinonStub;
  let journalCreditStub: sinon.SinonStub;
  let journalDebitStub: sinon.SinonStub;
  let journalCommitStub: sinon.SinonStub;
  let journalMarkEntriesSettledStub: sinon.SinonStub;
  let journalMarkEntriesArchivedStub: sinon.SinonStub;
  let steveoPublishStub: sinon.SinonStub;
  let sequelizeTransactionStub: import('sequelize').Transaction;
  let transaction: TransactionAttributes;
  const sellerId = uuid4();
  const buyerId = uuid4();

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    sandbox.restore();

    // Create repository stub
    transactionsRepository = stubInterface<TransactionsRepository>();
    userRepository = stubInterface<UserRepository>();
    transaction = transactionFactory.build({
      card_payment_method_id: uuid4(),
    });
    userRepository = userRepositoryFactory({
      users: [{ id: buyerId }, { id: sellerId }],
    });
    transactionsRepository = transactionsRepositoryFactory({
      transactions: [transaction],
    });

    // Create service instance
    service = new TransactionService(
      transactionsRepository,
      userRepository,
      logger
    );

    // Stub external dependencies
    validatePaymentMethodStub = sandbox.stub(
      transactionController,
      'validatePaymentMethod'
    );

    // Stub journal transaction methods
    journalCreditStub = sandbox
      .stub(JournalTransaction.prototype, 'credit')
      .resolves();
    journalDebitStub = sandbox
      .stub(JournalTransaction.prototype, 'debit')
      .resolves();
    journalCommitStub = sandbox
      .stub(JournalTransaction.prototype, 'commit')
      .resolves();
    journalMarkEntriesSettledStub = sandbox
      .stub(JournalTransaction.prototype, 'markEntriesSettled')
      .resolves();
    journalMarkEntriesArchivedStub = sandbox
      .stub(JournalTransaction.prototype, 'markEntriesArchived')
      .resolves();

    steveoPublishStub = sandbox.stub(steveo, 'publish').resolves();

    // Mock sequelize transaction
    sequelizeTransactionStub = stubInterface<import('sequelize').Transaction>();
    sandbox.stub(sequelize, 'transaction').callsFake(async (callback?: any) => {
      if (callback) {
        return callback(sequelizeTransactionStub);
      }
      return sequelizeTransactionStub;
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getOrCreateTransaction', () => {
    let mockTransaction: any;
    let mockPayload: CreateTransactionAttrs;
    const seller = userFactory.build();

    beforeEach(() => {
      mockTransaction = {
        id: uuidv4(),
        amount: 1000,
        currency: CurrencyCodes.AUD,
        state: states.pending,
        seller_id: uuidv4(),
        buyer_id: uuidv4(),
      };

      mockPayload = {
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test transaction',
        name: 'Test',
        seller_id: uuidv4(),
        buyer_id: uuidv4(),
        backend: Backend.PROMISEPAY,
        invoiceIds: [uuidv4()],
        orderIds: [uuidv4()],
        state: states.pending,
      } as CreateTransactionAttrs;

      validatePaymentMethodStub.resolves({ type: 'card' });
      transactionsRepository.upsertTransaction.resolves([
        mockTransaction,
        true,
      ]);
    });

    it('should create a new transaction with journal entries', async () => {
      const result = await service.getOrCreateTransaction(
        seller,
        mockPayload,
        'no-fee'
      );

      expect(result).to.deep.equal(mockTransaction);
      expect(validatePaymentMethodStub.calledOnce).to.be.true;
      expect(validatePaymentMethodStub.calledWith(mockPayload)).to.be.true;

      expect(transactionsRepository.upsertTransaction.calledOnce).to.be.true;
      expect(
        transactionsRepository.upsertTransaction.firstCall.args[0]
      ).to.deep.include({
        ...mockPayload,
        type: 'card',
      });

      expect(journalCreditStub.calledOnce).to.be.true;
      expect(journalCreditStub.firstCall.args[0]).to.deep.equal({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: mockPayload.seller_id,
        amount: mockPayload.amount,
      });

      expect(journalDebitStub.calledOnce).to.be.true;
      expect(journalDebitStub.firstCall.args[0]).to.deep.equal({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: mockPayload.buyer_id,
        amount: mockPayload.amount,
      });

      expect(journalCommitStub.calledOnce).to.be.true;
    });

    it('should return existing transaction without creating journal entries', async () => {
      transactionsRepository.upsertTransaction.resolves([
        mockTransaction,
        false,
      ]);

      const result = await service.getOrCreateTransaction(
        seller,
        mockPayload,
        'no-fee'
      );

      expect(result).to.deep.equal(mockTransaction);
      expect(validatePaymentMethodStub.calledOnce).to.be.true;
      expect(transactionsRepository.upsertTransaction.calledOnce).to.be.true;

      // Journal entries should not be created for existing transaction
      expect(journalCreditStub.called).to.be.false;
      expect(journalDebitStub.called).to.be.false;
      expect(journalCommitStub.called).to.be.false;
    });

    it('should handle transaction with sequelize transaction', async () => {
      const result = await service.getOrCreateTransaction(
        seller,
        mockPayload,
        'no-fee',
        sequelizeTransactionStub
      );

      expect(result).to.deep.equal(mockTransaction);
      expect(
        transactionsRepository.upsertTransaction.firstCall.args[1]
      ).to.equal(sequelizeTransactionStub);
      expect(journalCommitStub.firstCall.args[0]).to.equal(
        sequelizeTransactionStub
      );
    });

    it('should rollback transaction on error', async () => {
      const error = new Error('Database error');
      transactionsRepository.upsertTransaction.rejects(error);

      try {
        await service.getOrCreateTransaction(
          seller,
          mockPayload,
          'no-fee',
          sequelizeTransactionStub
        );
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
        expect(sequelizeTransactionStub.rollback).to.have.been.calledOnce;
      }
    });

    it('should handle validation error', async () => {
      const validationError = new Error('Invalid payment method');
      validatePaymentMethodStub.rejects(validationError);

      try {
        await service.getOrCreateTransaction(seller, mockPayload, 'no-fee');
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(validationError);
        expect(transactionsRepository.upsertTransaction.called).to.be.false;
      }
    });
  });

  describe('markCompleted', () => {
    const transactionId = uuidv4();
    const receivedAt = new Date().toISOString();
    const releaseAt = new Date().toISOString();

    beforeEach(() => {
      transactionsRepository.updateTransaction.resolves();
    });

    it('should mark transaction as completed and settle journal entries', async () => {
      await service.markCompleted({
        transactionId,
        receivedAt,
        releaseAt,
      });

      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
      expect(
        transactionsRepository.updateTransaction.firstCall.args
      ).to.deep.equal([
        transactionId,
        {
          state: states.completed,
          received_at: receivedAt,
          release_at: releaseAt,
        },
        undefined,
      ]);

      expect(journalMarkEntriesSettledStub.calledOnce).to.be.true;
      expect(steveoPublishStub.calledOnce).to.be.true;
      expect(steveoPublishStub.firstCall.args).to.deep.equal([
        'transaction-changed',
        { transactionId },
      ]);
    });

    it('should handle transaction with sequelize transaction', async () => {
      await service.markCompleted({
        transactionId,
        receivedAt,
        releaseAt,
        txn: sequelizeTransactionStub,
      });

      expect(
        transactionsRepository.updateTransaction.firstCall.args[2]
      ).to.equal(sequelizeTransactionStub);
      expect(journalMarkEntriesSettledStub.firstCall.args[0]).to.equal(
        sequelizeTransactionStub
      );
    });

    it('should handle errors during update', async () => {
      const error = new Error('Update failed');
      transactionsRepository.updateTransaction.rejects(error);

      try {
        await service.markCompleted({
          transactionId,
          receivedAt,
          releaseAt,
        });
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
        expect(steveoPublishStub.called).to.be.false;
      }
    });
  });

  describe('markFailed', () => {
    const transactionId = uuidv4();
    const declineReason = 'Insufficient funds';
    const declineCode = 'NSF';

    beforeEach(() => {
      transactionsRepository.updateTransaction.resolves();
    });

    it('should mark transaction as failed and archive journal entries', async () => {
      await service.markFailed({
        transactionId,
        declineReason,
        declineCode,
      });

      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
      expect(
        transactionsRepository.updateTransaction.firstCall.args
      ).to.deep.equal([
        transactionId,
        {
          state: states.failed,
          declineReason,
          declineCode,
        },
        undefined,
      ]);

      expect(journalMarkEntriesArchivedStub.calledOnce).to.be.true;
      expect(steveoPublishStub.calledOnce).to.be.true;
      expect(steveoPublishStub.firstCall.args).to.deep.equal([
        'transaction-changed',
        { transactionId },
      ]);
    });

    it('should truncate long decline reasons', async () => {
      const longDeclineReason = 'A'.repeat(300); // Create a very long string

      await service.markFailed({
        transactionId,
        declineReason: longDeclineReason,
      });

      const actualDeclineReason =
        transactionsRepository.updateTransaction.firstCall.args[1]
          .declineReason;
      expect(actualDeclineReason).to.exist;
      expect(actualDeclineReason!.length).to.be.at.most(255);
    });

    it('should handle transaction without decline code', async () => {
      await service.markFailed({
        transactionId,
        declineReason,
      });

      expect(
        transactionsRepository.updateTransaction.firstCall.args[1]
      ).to.deep.equal({
        state: states.failed,
        declineReason,
        declineCode: undefined,
      });
    });

    it('should handle transaction with sequelize transaction', async () => {
      await service.markFailed({
        transactionId,
        declineReason,
        declineCode,
        txn: sequelizeTransactionStub,
      });

      expect(
        transactionsRepository.updateTransaction.firstCall.args[2]
      ).to.equal(sequelizeTransactionStub);
    });

    it('should handle errors during update', async () => {
      const error = new Error('Update failed');
      transactionsRepository.updateTransaction.rejects(error);

      try {
        await service.markFailed({
          transactionId,
          declineReason,
        });
        expect.fail('Should have thrown an error');
      } catch (err) {
        expect(err).to.equal(error);
        expect(steveoPublishStub.called).to.be.false;
      }
    });
  });

  describe('transactionsReleaseNow', () => {
    it('should release the funds to the seller', async () => {
      // ARRANGE
      userRepository.getUserById
        .withArgs(sellerId)
        .resolves(userFactory.build({ id: sellerId }));
      transaction.amount = 2000;
      transaction.settlementAmount = undefined;
      const settlementAmountCents = 1800;
      const beforeRelease = new Date();

      // ACT
      await service.transactionsReleaseNow({
        transactionId: transaction.id ?? '',
        settlementAmountCents,
        sellerId,
      });

      // ASSERT
      const update = transactionsRepository.updateTransaction.firstCall.args[1];
      const afterRelease = new Date();

      expect(transactionsRepository.updateTransaction.calledOnce).to.be.true;
      expect(update.settlementAmount).to.equal(settlementAmountCents);
      expect(update.amount).to.equal(settlementAmountCents);
      expect(update.received_at).to.be.ok;
      expect(update.release_at).to.be.ok;

      // Verify received_at is within reasonable time window
      if (!update.received_at) expect.fail('Received at is not set');
      expect(update.received_at).to.be.a('string');
      const receivedAt = new Date(update.received_at);
      expect(receivedAt.getTime()).to.be.at.least(beforeRelease.getTime());
      expect(receivedAt.getTime()).to.be.at.most(afterRelease.getTime());

      // Verify release_at is different from received_at (should be calculated using business days)
      expect(update.release_at).to.be.a('string');
      assert(update.release_at);
      const releaseAt = new Date(update.release_at);
      expect(releaseAt.getTime()).to.not.equal(receivedAt.getTime());

      // Verify release_at is after received_at
      expect(releaseAt.getTime()).to.be.greaterThan(receivedAt.getTime());
    });

    it('should throw an error if the seller is not found', async () => {
      // ARRANGE
      userRepository.getUserById.onCall(0).resolves(null);

      // ACT
      try {
        await service.transactionsReleaseNow({
          transactionId: transaction.id ?? '',
          sellerId: 'invalid-seller-id',
          settlementAmountCents: 0,
        });

        expect.fail('Expected error to be thrown');
      } catch (error) {
        // ASSERT
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Seller not found');
      }
    });
  });
});
