import { expect } from 'chai';
import { createStubInstance, SinonStubbedInstance } from 'sinon';
import { UserService } from '../../../../../src/ng/domain/services/domain/user.service';
import { UserRepository } from '../../../../../src/ng/domain/repositories/user.repository';
import { ZaiUserManagementGateway } from '../../../../../src/ng/domain/services/gateways/zai_user_management.gateway';
import { UserInstance } from '../../../../../src/models/user';
import { GatewayUser } from '../../../../../src/ng/common/types/gateway.types';
import { createUserFixture } from '../../fixtures/user.fixture';
import { logger } from '../../../../../src/lib/logger';

/**
 * Mock the UserService and its dependencies
 */
function mockUserService(): {
  userRepository: SinonStubbedInstance<UserRepository>;
  zaiUserManagementGateway: SinonStubbedInstance<ZaiUserManagementGateway>;
  userService: UserService;
} {
  const userRepository = createStubInstance(UserRepository);
  const zaiUserManagementGateway = createStubInstance(ZaiUserManagementGateway);

  const userService = new UserService(
    userRepository,
    zaiUserManagementGateway,
    logger
  );

  return {
    userRepository,
    zaiUserManagementGateway,
    userService,
  };
}

describe('UserService', () => {
  describe('getUser', () => {
    it('should return user when found in repository', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const expectedUser = createUserFixture({
        id: inputUserId,
      }) as UserInstance;

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(expectedUser);

      // ACT
      const actualUser = await mockService.userService.getUser(inputUserId);

      // ASSERT
      expect(actualUser).to.equal(expectedUser);
    });

    it('should return null when user not found in repository', async () => {
      // ARRANGE
      const inputUserId = 'nonexistent_user';
      const mockService = mockUserService();

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(null);

      // ACT
      const actualUser = await mockService.userService.getUser(inputUserId);

      // ASSERT
      expect(actualUser).to.be.null;
    });
  });

  describe('safeGetUser', () => {
    it('should return undefined when user not found in database', async () => {
      // ARRANGE
      const inputUserId = 'nonexistent_user';
      const mockService = mockUserService();

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(null);

      // ACT
      const actualUser = await mockService.userService.safeGetUser(inputUserId);

      // ASSERT
      expect(actualUser).to.be.undefined;
    });

    it('should throw error when user has no external_id', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const inputUser = createUserFixture({
        id: inputUserId,
        external_id: '', // Empty external_id should trigger assertion error
      }) as UserInstance;

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(inputUser);

      // ACT & ASSERT
      await expect(
        mockService.userService.safeGetUser(inputUserId)
      ).to.be.rejectedWith('User has no external ID');
    });

    it('should return user when user exists in database and gateway', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const expectedUser = createUserFixture({
        id: inputUserId,
        external_id: 'ext_user123',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: '1234567890',
      }) as UserInstance;

      const expectedGatewayUser: GatewayUser = {
        id: expectedUser.id,
        email: expectedUser.email,
        firstName: expectedUser.first_name,
        lastName: expectedUser.last_name,
        mobile: expectedUser.mobile_number,
      };

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(expectedUser);

      mockService.zaiUserManagementGateway.getUser
        .withArgs(expectedUser.external_id)
        .resolves(expectedGatewayUser);

      // ACT
      const actualUser = await mockService.userService.safeGetUser(inputUserId);

      // ASSERT
      expect(actualUser).to.equal(expectedUser);
    });

    it('should create gateway user when user exists in database but not in gateway', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const expectedUser = createUserFixture({
        id: inputUserId,
        external_id: 'ext_user123',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: '1234567890',
      }) as UserInstance;

      const expectedCreateUserPayload: GatewayUser = {
        id: expectedUser.id,
        email: expectedUser.email,
        firstName: expectedUser.first_name,
        lastName: expectedUser.last_name,
        mobile: expectedUser.mobile_number,
      };

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(expectedUser);

      mockService.zaiUserManagementGateway.getUser
        .withArgs(expectedUser.external_id)
        .rejects(new Error('User not found in gateway'));

      mockService.zaiUserManagementGateway.createUser
        .withArgs(expectedCreateUserPayload)
        .resolves();

      mockService.userRepository.updateUser
        .withArgs(expectedUser.id, { external_id: expectedUser.id })
        .resolves();

      // ACT
      const actualUser = await mockService.userService.safeGetUser(inputUserId);

      // ASSERT
      expect(actualUser).to.equal(expectedUser);
      expect(mockService.zaiUserManagementGateway.createUser.calledOnce).to.be
        .true;
      expect(mockService.userRepository.updateUser.calledOnce).to.be.true;
    });

    it('should handle undefined mobile number when creating gateway user', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const expectedUser = createUserFixture({
        id: inputUserId,
        external_id: 'ext_user123',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: undefined, // Test undefined mobile number
      }) as UserInstance;

      const expectedCreateUserPayload: GatewayUser = {
        id: expectedUser.id,
        email: expectedUser.email,
        firstName: expectedUser.first_name,
        lastName: expectedUser.last_name,
        mobile: undefined,
      };

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(expectedUser);

      mockService.zaiUserManagementGateway.getUser
        .withArgs(expectedUser.external_id)
        .rejects(new Error('User not found in gateway'));

      mockService.zaiUserManagementGateway.createUser
        .withArgs(expectedCreateUserPayload)
        .resolves();

      mockService.userRepository.updateUser
        .withArgs(expectedUser.id, { external_id: expectedUser.id })
        .resolves();

      // ACT
      const actualUser = await mockService.userService.safeGetUser(inputUserId);

      // ASSERT
      expect(actualUser).to.equal(expectedUser);
      expect(mockService.zaiUserManagementGateway.createUser.calledOnce).to.be
        .true;
    });

    it('should throw error when gateway user creation fails', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const inputUser = createUserFixture({
        id: inputUserId,
        external_id: 'ext_user123',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: '1234567890',
      }) as UserInstance;

      const expectedCreateUserPayload: GatewayUser = {
        id: inputUser.id,
        email: inputUser.email,
        firstName: inputUser.first_name,
        lastName: inputUser.last_name,
        mobile: inputUser.mobile_number,
      };

      const expectedError = new Error('Gateway creation failed');

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(inputUser);

      mockService.zaiUserManagementGateway.getUser
        .withArgs(inputUser.external_id)
        .rejects(new Error('User not found in gateway'));

      mockService.zaiUserManagementGateway.createUser
        .withArgs(expectedCreateUserPayload)
        .rejects(expectedError);

      // ACT & ASSERT
      await expect(
        mockService.userService.safeGetUser(inputUserId)
      ).to.be.rejectedWith('Gateway creation failed');

      expect(mockService.zaiUserManagementGateway.createUser.calledOnce).to.be
        .true;
      expect(mockService.userRepository.updateUser.called).to.be.false;
    });

    it('should not attempt to create gateway user when getUser returns undefined', async () => {
      // ARRANGE
      const inputUserId = 'user123';
      const mockService = mockUserService();
      const expectedUser = createUserFixture({
        id: inputUserId,
        external_id: 'ext_user123',
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        mobile_number: '1234567890',
      }) as UserInstance;

      mockService.userRepository.getUserById
        .withArgs(inputUserId)
        .resolves(expectedUser);

      mockService.zaiUserManagementGateway.getUser
        .withArgs(expectedUser.external_id)
        .resolves(undefined); // Gateway returns undefined (user not found)

      mockService.zaiUserManagementGateway.createUser.resolves();
      mockService.userRepository.updateUser.resolves();

      // ACT
      const actualUser = await mockService.userService.safeGetUser(inputUserId);

      // ASSERT
      expect(actualUser).to.equal(expectedUser);
      expect(mockService.zaiUserManagementGateway.createUser.calledOnce).to.be
        .true;
      expect(mockService.userRepository.updateUser.calledOnce).to.be.true;
    });
  });
});
