import sinon from 'sinon';
import { expect } from 'chai';
import { ZaiWalletsGateway } from '../../../../../src/ng/domain/services/gateways/zai_wallets.gateway';
import { ZaiClient } from '../../../../../src/ng/domain/services/gateways/zai_fund_transfer.gateway';
import { logger } from '../../../../../src/lib/logger';
import {
  zaiClientFixture,
  ZaiClientOverrides,
} from '../../fixtures/zai.fixture';

describe('ZaiWalletsGateway', () => {
  let sandbox: sinon.SinonSandbox;
  let gateway: ZaiWalletsGateway;
  let mockLogger: typeof logger;
  let mockZaiClient: ZaiClientOverrides;
  let showUserWalletAccountsStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stub for the showUserWalletAccounts method
    showUserWalletAccountsStub = sandbox.stub();

    // Create ZaiClient mock using the fixture
    mockZaiClient = zaiClientFixture(sandbox, {
      users: {
        showUserWalletAccounts: showUserWalletAccountsStub,
      },
    });

    // Stub logger methods
    mockLogger = {
      ...logger,
      error: sandbox.stub(),
      info: sandbox.stub(),
      warn: sandbox.stub(),
      debug: sandbox.stub(),
    } as unknown as typeof logger;

    gateway = new ZaiWalletsGateway(
      mockLogger,
      mockZaiClient as unknown as ZaiClient
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getUserWallet', () => {
    const walletId = 'test-wallet-123';

    it('should successfully return wallet account when valid response received', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: 'wallet-account-456',
          balance: 5000,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT
      const result = await gateway.getUserWallet(walletId);

      // ASSERT
      expect(showUserWalletAccountsStub).to.have.been.calledOnceWith(walletId);
      expect(result).to.deep.equal({
        accountId: 'wallet-account-456',
        balance: 5000,
      });
    });

    it('should handle zero balance correctly', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: 'wallet-account-456',
          balance: 0,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT
      const result = await gateway.getUserWallet(walletId);

      // ASSERT
      expect(result).to.deep.equal({
        accountId: 'wallet-account-456',
        balance: 0,
      });
    });

    it('should handle null balance by defaulting to 0', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: 'wallet-account-456',
          balance: null,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT
      const result = await gateway.getUserWallet(walletId);

      // ASSERT
      expect(result).to.deep.equal({
        accountId: 'wallet-account-456',
        balance: 0,
      });
    });

    it('should handle undefined balance by defaulting to 0', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: 'wallet-account-456',
          balance: undefined,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT
      const result = await gateway.getUserWallet(walletId);

      // ASSERT
      expect(result).to.deep.equal({
        accountId: 'wallet-account-456',
        balance: 0,
      });
    });

    it('should throw error and log when wallet_accounts.id is missing', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          balance: 5000,
          // id is missing
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should throw error and log when wallet_accounts.id is null', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: null,
          balance: 5000,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should throw error and log when wallet_accounts.id is empty string', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: {
          id: '',
          balance: 5000,
        },
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should throw error and log when wallet_accounts is null', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: null,
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should throw error and log when wallet_accounts is undefined', async () => {
      // ARRANGE
      const mockResponse = {
        wallet_accounts: undefined,
      };

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should throw error and log when response is empty object', async () => {
      // ARRANGE
      const mockResponse = {};

      showUserWalletAccountsStub.resolves(mockResponse);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Failed to load wallet'
      );

      expect(mockLogger.error).to.have.been.calledOnceWith(
        { walletId },
        'Failed to load wallet'
      );
    });

    it('should propagate ZaiClient errors', async () => {
      // ARRANGE
      const zaiError = new Error('ZAI API Error');
      showUserWalletAccountsStub.rejects(zaiError);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'ZAI API Error'
      );

      expect(showUserWalletAccountsStub).to.have.been.calledOnceWith(walletId);
      expect(mockLogger.error).to.not.have.been.called;
    });

    it('should handle API timeout errors', async () => {
      // ARRANGE
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'TimeoutError';
      showUserWalletAccountsStub.rejects(timeoutError);

      // ACT & ASSERT
      await expect(gateway.getUserWallet(walletId)).to.be.rejectedWith(
        'Request timeout'
      );

      expect(showUserWalletAccountsStub).to.have.been.calledOnceWith(walletId);
    });
  });
});
