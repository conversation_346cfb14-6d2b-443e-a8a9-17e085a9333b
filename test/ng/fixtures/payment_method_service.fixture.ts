import { stubInterface, ObjectMethodsMap } from 'ts-sinon';
import { Sync } from 'factory.ts';
import { PaymentMethodInstance } from '../../../src/models/payment_methods';
import { PaymentMethodService } from '../../../src/ng/domain/services/domain/payment_method.service';
import { cardPaymentMethodFactory } from '../domain/fixtures/payment_method.fixtures';
import { Backend } from '../../../src/models/transaction';

/**
 * Create a payment method fixture
 * @param overrides - The overrides
 * @returns The payment method fixture
 */
export const paymentMethodFactory = Sync.makeFactory<PaymentMethodInstance>({
  id: 'pm123',
  accountId: 'user123',
  type: 'trade-account',
} as PaymentMethodInstance);

export const paymentMethodFixture = (
  overrides?: Partial<PaymentMethodInstance>
): PaymentMethodInstance =>
  ({
    id: 'pm123',
    user_id: 'user123',
    type: 'card',
    ...(overrides ?? {}),
  } as PaymentMethodInstance);

/**
 * Create a payment method service fixture
 * @param props - The props
 * @returns The payment method service fixture
 */
export const paymentMethodServiceFactory = (
  overrides?: ObjectMethodsMap<PaymentMethodService>
) =>
  stubInterface<PaymentMethodService>({
    getBackendAccountId: Promise.resolve('pm123'),
    selectUpfrontHoldingPaymentMethod: Promise.resolve({
      paymentMethod: paymentMethodFactory.build(),
      cardPaymentMethod: cardPaymentMethodFactory.build(),
    }),
    validatePaymentMethod: Promise.resolve({
      type: 'visa',
      backend: Backend.PROMISEPAY,
    }),
    ...(overrides ?? {}),
  });
