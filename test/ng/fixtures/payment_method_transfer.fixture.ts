import { v4 as uuidv4 } from 'uuid';
import { Sync } from 'factory.ts';
import { CurrencyCodes } from '../../../src/types/currency_codes';
import { PaymentMethodTransferParams } from '../../../src/ng/workflow/types/payment_methods_transfer.types';

export const holdFundsInEscrowFactory =
  Sync.makeFactory<PaymentMethodTransferParams>({
    description: 'Test upfront payment',
    amountCents: 1000,
    sellerId: 'seller123',
    sellerText: 'Seller',
    buyerId: 'buyer123',
    buyerText: 'Buyer',
    currencyCode: CurrencyCodes.AUD,
    paymentMethodId: 'pm123',
    orderIds: ['order123'],
    invoiceIds: ['invoice123'],
    transactionId: uuidv4(),
    relatedTransactions: [],
  });
