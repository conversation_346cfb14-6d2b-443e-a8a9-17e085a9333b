import { Sync } from 'factory.ts';
import { stubInterface, ObjectMethodsMap } from 'ts-sinon';
import { Backend } from '../../../src/models/transaction';
import { UserRepository } from '../../../src/ng/domain/repositories/user.repository';
import { UserInstance } from '../../../src/models/user';
import { UserService } from '../../../src/ng/domain/services/domain/user.service';

/**
 * Create a user fixture
 * @returns
 */
export const userFactory = Sync.makeFactory<UserInstance>({
  id: 'user123',
  external_id: 'ext_user123',
  backend: Backend.PROMISEPAY,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  deleted_at: undefined,
  first_name: 'Test',
  last_name: 'User',
  email: '<EMAIL>',
  mobile_number: '',
  address_line_1: '123 Test St',
  address_line_2: '',
  city: 'Test City',
  state: 'TS',
  postcode: '1234',
  country: 'AU',
  dob: '',
  settlement_webhook: '',
  company_name: '',
  company_tax_number: '',
  company_address_line_1: '',
  company_address_line_2: '',
  company_city: '',
  company_state: '',
  company_postcode: '',
  company_country: '',
  dd_settlement_delay_hours: 0,
  batch_settlement_payments: true,
  company_legal_name: '',
  ordermentum_id: 'ordermentum123',
  configuration: {},
  stripe_customer_id: '',
  status: 'active',
  payment_method_id: '',
  payment_method_type: undefined,
} as UserInstance);

/**
 *
 * @returns
 */
export const userRepositoryFactory = (props?: {
  users?: Partial<UserInstance>[];
  overrides?: ObjectMethodsMap<UserRepository>;
}) => {
  const userRepository = stubInterface<UserRepository>({
    ...(props?.overrides ?? {}),
  });

  props?.users?.forEach((user, index) => {
    userRepository.getUserById.onCall(index).resolves(userFactory.build(user));
  });

  return userRepository;
};

/**
 * Create a user service ficture factory
 * @param props
 * @returns
 */
export const userServiceFactory = (props?: {
  overrides?: ObjectMethodsMap<UserService>;
}) =>
  stubInterface<UserService>({
    safeGetUser: Promise.resolve(userFactory.build()),
    ...(props?.overrides ?? {}),
  });
