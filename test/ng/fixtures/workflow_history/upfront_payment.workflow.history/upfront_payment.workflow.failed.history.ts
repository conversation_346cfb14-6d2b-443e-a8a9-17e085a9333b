export const upfrontFailedWorkflowHistory = {
  workflowId: 'upfront-payment-order-mock-order-id',
  history: {
    events: [
      {
        eventId: '1',
        eventTime: '2025-08-10T23:10:09.400581083Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '184809295',
        workflowExecutionStartedEventAttributes: {
          workflowType: {
            name: 'upfrontPaymentWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NDAwMCwiZGVzY3JpcHRpb24iOiJTV0VBVCA3ZmM2NiIsInNlbGxlclVzZXJJZCI6Im1vY2stc2VsbGVyLXVzZXItaWQiLCJidXllclVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkIiwiaW52b2ljZUlkIjoibW9jay1pbnZvaWNlLWlkIiwidXNlcklkIjoibW9jay1idXllci11c2VyLWlkIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZCJ9',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'Im1vY2stdHJhbnNhY3Rpb24taWQi',
              },
            ],
          },
          workflowTaskTimeout: '10s',
          originalExecutionRunId: 'mock-execution-run-id',
          identity: 'mock-worker-identity-1',
          firstExecutionRunId: 'mock-execution-run-id',
          retryPolicy: {
            initialInterval: '1s',
            backoffCoefficient: 2,
            maximumInterval: '100s',
            maximumAttempts: 1,
          },
          attempt: 1,
          firstWorkflowTaskBackoff: '2s',
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkIl0=',
              },
            },
          },
          header: {},
          workflowId: 'upfront-payment-order-mock-order-id',
        },
      },
      {
        eventId: '2',
        eventTime: '2025-08-10T23:10:11.405257850Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809300',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '3',
        eventTime: '2025-08-10T23:10:11.413605562Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '184809303',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '2',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-1',
          historySizeBytes: '902',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '4',
        eventTime: '2025-08-10T23:10:11.635338893Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '184809308',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '2',
          startedEventId: '3',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {
            coreUsedFlags: [2, 3, 1],
            sdkName: 'temporal-typescript',
            sdkVersion: '1.12.1',
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '5',
        eventTime: '2025-08-10T23:10:11.635473834Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809309',
        activityTaskScheduledEventAttributes: {
          activityId: '1',
          activityType: {
            name: 'upfrontStart',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NDAwMCwiZGVzY3JpcHRpb24iOiJTV0VBVCA3ZmM2NiIsInNlbGxlclVzZXJJZCI6Im1vY2stc2VsbGVyLXVzZXItaWQiLCJidXllclVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkIiwiaW52b2ljZUlkIjoibW9jay1pbnZvaWNlLWlkIiwidXNlcklkIjoibW9jay1idXllci11c2VyLWlkIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZCJ9',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '4',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '6',
        eventTime: '2025-08-10T23:10:11.635529201Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '184809313',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '5',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-2',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '7',
        eventTime: '2025-08-10T23:10:12.277457560Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '184809314',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '5',
          startedEventId: '6',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '8',
        eventTime: '2025-08-10T23:10:12.277465871Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809315',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '9',
        eventTime: '2025-08-10T23:10:12.287088001Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '184809319',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '8',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-3',
          historySizeBytes: '2254',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '10',
        eventTime: '2025-08-10T23:10:12.507549557Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '184809324',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '8',
          startedEventId: '9',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '11',
        eventTime: '2025-08-10T23:10:12.507710983Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809325',
        activityTaskScheduledEventAttributes: {
          activityId: '2',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NDAwMCwiYnV5ZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZCIsImJ1eWVyVGV4dCI6IlNXRUFUIDdmYzY2IiwiZGVzY3JpcHRpb24iOiJTV0VBVCA3ZmM2NiIsIm9yZGVySWRzIjpbIm1vY2stb3JkZXItaWQiXSwiaW52b2ljZUlkcyI6WyJtb2NrLWludm9pY2UtaWQiXSwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZCIsInNlbGxlcklkIjoibW9jay1zZWxsZXItdXNlci1pZCIsInNlbGxlclRleHQiOiJTV0VBVCA3ZmM2NiIsInRyYW5zYWN0aW9uSWQiOiJtb2NrLXRyYW5zYWN0aW9uLWlkIiwicmVsYXRlZFRyYW5zYWN0aW9ucyI6W10sImNvbnRleHQiOnsib3JpZ2luYWxQYXlJbkFtb3VudENlbnRzIjo0MDAwfX0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '10',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '12',
        eventTime: '2025-08-10T23:10:42.954888626Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '184809336',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '11',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-4',
          attempt: 3,
          lastFailure: {
            message: 'Payment method not found',
            source: 'TypeScriptSDK',
            stackTrace:
              'Error: Payment method not found\n    at PaymentMethodService.selectUpfrontHoldingPaymentMethod (/app/build/ng/domain/services/domain/payment_method.service.js:62:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async paymentMethodTransfer (/app/build/ng/workflow/activities/wallet.activities.js:35:29)\n    at async Activity.execute (/app/node_modules/@temporalio/worker/lib/activity.js:95:20)\n    at async /app/node_modules/@temporalio/worker/lib/activity.js:130:32\n    at async /app/node_modules/@temporalio/worker/lib/worker.js:676:30',
            applicationFailureInfo: {
              type: 'Error',
            },
          },
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '13',
        eventTime: '2025-08-10T23:10:43.178728442Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_FAILED',
        version: '1991',
        taskId: '184809337',
        activityTaskFailedEventAttributes: {
          failure: {
            message: 'Payment method not found',
            source: 'TypeScriptSDK',
            stackTrace:
              'Error: Payment method not found\n    at PaymentMethodService.selectUpfrontHoldingPaymentMethod (/app/build/ng/domain/services/domain/payment_method.service.js:62:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async paymentMethodTransfer (/app/build/ng/workflow/activities/wallet.activities.js:35:29)\n    at async Activity.execute (/app/node_modules/@temporalio/worker/lib/activity.js:95:20)\n    at async /app/node_modules/@temporalio/worker/lib/activity.js:130:32\n    at async /app/node_modules/@temporalio/worker/lib/worker.js:676:30',
            applicationFailureInfo: {
              type: 'Error',
            },
          },
          scheduledEventId: '11',
          startedEventId: '12',
          identity: 'mock-worker-identity-2',
          retryState: 'RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED',
        },
      },
      {
        eventId: '14',
        eventTime: '2025-08-10T23:10:43.178734834Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809338',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '15',
        eventTime: '2025-08-10T23:10:43.190232466Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '184809342',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '14',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-5',
          historySizeBytes: '4951',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '16',
        eventTime: '2025-08-10T23:10:43.405509121Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '184809347',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '14',
          startedEventId: '15',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '17',
        eventTime: '2025-08-10T23:10:43.405601920Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809348',
        activityTaskScheduledEventAttributes: {
          activityId: '3',
          activityType: {
            name: 'upfrontHoldingTransactionFailed',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: '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',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '16',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '18',
        eventTime: '2025-08-10T23:10:43.405671655Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '184809351',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '17',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-6',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '19',
        eventTime: '2025-08-10T23:10:44.199101613Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '184809352',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '17',
          startedEventId: '18',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '20',
        eventTime: '2025-08-10T23:10:44.199108456Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '184809353',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '21',
        eventTime: '2025-08-10T23:10:44.206688127Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '184809357',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '20',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-7',
          historySizeBytes: '8280',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '22',
        eventTime: '2025-08-10T23:10:44.427172299Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '184809361',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '20',
          startedEventId: '21',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '23',
        eventTime: '2025-08-10T23:10:44.427258353Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_FAILED',
        version: '1991',
        taskId: '184809362',
        workflowExecutionFailedEventAttributes: {
          failure: {
            message: 'Activity task failed',
            cause: {
              message: 'Payment method not found',
              source: 'TypeScriptSDK',
              stackTrace:
                'Error: Payment method not found\n    at PaymentMethodService.selectUpfrontHoldingPaymentMethod (/app/build/ng/domain/services/domain/payment_method.service.js:62:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async paymentMethodTransfer (/app/build/ng/workflow/activities/wallet.activities.js:35:29)\n    at async Activity.execute (/app/node_modules/@temporalio/worker/lib/activity.js:95:20)\n    at async /app/node_modules/@temporalio/worker/lib/activity.js:130:32\n    at async /app/node_modules/@temporalio/worker/lib/worker.js:676:30',
              applicationFailureInfo: {
                type: 'Error',
              },
            },
            activityFailureInfo: {
              scheduledEventId: '11',
              startedEventId: '12',
              identity: 'mock-worker-identity-2',
              activityType: {
                name: 'paymentMethodTransfer',
              },
              activityId: '2',
              retryState: 'RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED',
            },
          },
          retryState: 'RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED',
          workflowTaskCompletedEventId: '22',
        },
      },
    ],
  },
};
