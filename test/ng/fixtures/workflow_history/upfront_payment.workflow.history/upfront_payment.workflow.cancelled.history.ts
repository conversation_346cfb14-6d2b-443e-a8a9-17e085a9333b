export const upfrontCancelledWorkflowHistory = {
  workflowId: 'upfront-payment-order-mock-order-id-3',
  history: {
    events: [
      {
        eventId: '1',
        eventTime: '2025-08-07T23:28:37.340424774Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '185778618',
        workflowExecutionStartedEventAttributes: {
          workflowType: {
            name: 'upfrontPaymentWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTIxMCwiZGVzY3JpcHRpb24iOiJNQlRMIDA2ZjZiIiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0zIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMyIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTMiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMyIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0zIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0zIn0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0zIg==',
              },
            ],
          },
          workflowTaskTimeout: '10s',
          originalExecutionRunId: 'mock-execution-run-id-3',
          identity: 'mock-worker-identity-1',
          firstExecutionRunId: 'mock-execution-run-id-3',
          retryPolicy: {
            initialInterval: '1s',
            backoffCoefficient: 2,
            maximumInterval: '100s',
            maximumAttempts: 1,
          },
          attempt: 1,
          firstWorkflowTaskBackoff: '2s',
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTMiXQ==',
              },
            },
          },
          header: {},
          workflowId: 'upfront-payment-order-mock-order-id-3',
        },
      },
      {
        eventId: '2',
        eventTime: '2025-08-07T23:28:39.343958263Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778623',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '3',
        eventTime: '2025-08-07T23:28:39.354112719Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778626',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '2',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-1',
          historySizeBytes: '901',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '4',
        eventTime: '2025-08-07T23:28:39.573065068Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778631',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '2',
          startedEventId: '3',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {
            coreUsedFlags: [1, 3, 2],
            sdkName: 'temporal-typescript',
            sdkVersion: '1.12.1',
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '5',
        eventTime: '2025-08-07T23:28:39.573172609Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778632',
        activityTaskScheduledEventAttributes: {
          activityId: '1',
          activityType: {
            name: 'upfrontStart',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTIxMCwiZGVzY3JpcHRpb24iOiJNQlRMIDA2ZjZiIiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0zIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMyIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTMiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMyIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0zIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0zIn0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '4',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '6',
        eventTime: '2025-08-07T23:28:39.573218983Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '185778636',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '5',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-2',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '7',
        eventTime: '2025-08-07T23:28:40.175771103Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '185778637',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '5',
          startedEventId: '6',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '8',
        eventTime: '2025-08-07T23:28:40.175777495Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778638',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '9',
        eventTime: '2025-08-07T23:28:40.185027838Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778642',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '8',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-3',
          historySizeBytes: '2250',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '10',
        eventTime: '2025-08-07T23:28:40.400987167Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778647',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '8',
          startedEventId: '9',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '11',
        eventTime: '2025-08-07T23:28:40.401084354Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778648',
        activityTaskScheduledEventAttributes: {
          activityId: '2',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTIxMCwiYnV5ZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0zIiwiYnV5ZXJUZXh0IjoiTUJUTCAwNmY2YiIsImRlc2NyaXB0aW9uIjoiTUJUTCAwNmY2YiIsIm9yZGVySWRzIjpbIm1vY2stb3JkZXItaWQtMyJdLCJpbnZvaWNlSWRzIjpbIm1vY2staW52b2ljZS1pZC0zIl0sInBheW1lbnRNZXRob2RJZCI6Im1vY2stcGF5bWVudC1tZXRob2QtaWQtMyIsInNlbGxlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0zIiwic2VsbGVyVGV4dCI6Ik1CVEwgMDZmNmIiLCJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTMiLCJyZWxhdGVkVHJhbnNhY3Rpb25zIjpbXSwiY29udGV4dCI6eyJvcmlnaW5hbFBheUluQW1vdW50Q2VudHMiOjEyMTB9fQ==',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '10',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '12',
        eventTime: '2025-08-07T23:28:40.401124443Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '185778743',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '11',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-4',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '13',
        eventTime: '2025-08-07T23:28:46.677841957Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '185778744',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJqb3VybmFsSWQiOiJtb2NrLWpvdXJuYWwtaWQtMyIsInJldHVybmVkIjp7fX0=',
              },
            ],
          },
          scheduledEventId: '11',
          startedEventId: '12',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '14',
        eventTime: '2025-08-07T23:28:46.677850794Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778745',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '15',
        eventTime: '2025-08-07T23:28:46.688488879Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778749',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '14',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-5',
          historySizeBytes: '3769',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '16',
        eventTime: '2025-08-07T23:28:46.905323523Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778754',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '14',
          startedEventId: '15',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '17',
        eventTime: '2025-08-07T23:28:46.905421260Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778755',
        activityTaskScheduledEventAttributes: {
          activityId: '3',
          activityType: {
            name: 'upfrontHoldingTransactionSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0zIiwidXBmcm9udCI6eyJhbW91bnRDZW50cyI6MTIxMCwiZGVzY3JpcHRpb24iOiJNQlRMIDA2ZjZiIiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0zIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMyIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTMiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMyIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0zIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0zIn19',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '16',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '18',
        eventTime: '2025-08-07T23:28:46.905473476Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '185778758',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '17',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-6',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '19',
        eventTime: '2025-08-07T23:28:48.004720067Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '185778759',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '17',
          startedEventId: '18',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '20',
        eventTime: '2025-08-07T23:28:48.004725457Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778760',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '21',
        eventTime: '2025-08-07T23:28:48.013249667Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778764',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '20',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-7',
          historySizeBytes: '5180',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '22',
        eventTime: '2025-08-07T23:28:48.229534403Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778768',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '20',
          startedEventId: '21',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '23',
        eventTime: '2025-08-07T23:29:30.084579813Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED',
        version: '1991',
        taskId: '185778881',
        workflowExecutionSignaledEventAttributes: {
          signalName: 'upfront-payment:cancelled',
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJyZWFzb24iOiJVc2VyIGNhbmNlbGxlZCB0aGUgb3JkZXIiLCJ0eXBlIjoiY2FuY2VsbGVkIn0=',
              },
            ],
          },
          identity: 'mock-worker-identity-3',
          header: {},
        },
      },
      {
        eventId: '24',
        eventTime: '2025-08-07T23:29:30.084583678Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185778882',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '25',
        eventTime: '2025-08-07T23:29:30.092992847Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778886',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '24',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-8',
          historySizeBytes: '5859',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '26',
        eventTime: '2025-08-07T23:29:30.320085705Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778890',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '24',
          startedEventId: '25',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {
            langUsedFlags: [2],
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '27',
        eventTime: '2025-08-07T23:29:30.320266344Z',
        eventType: 'EVENT_TYPE_START_CHILD_WORKFLOW_EXECUTION_INITIATED',
        version: '1991',
        taskId: '*********',
        startChildWorkflowExecutionInitiatedEventAttributes: {
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          workflowId: 'refund-transaction-mock-holding-transaction-id-3',
          workflowType: {
            name: 'refundWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTMiLCJyZWZ1bmRBbW91bnRDZW50cyI6MTIxMH0=',
              },
            ],
          },
          workflowRunTimeout: '0s',
          workflowTaskTimeout: '10s',
          parentClosePolicy: 'PARENT_CLOSE_POLICY_TERMINATE',
          workflowTaskCompletedEventId: '26',
          workflowIdReusePolicy: 'WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE',
          header: {},
          memo: {},
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTMiXQ==',
              },
            },
          },
          inheritBuildId: true,
        },
      },
      {
        eventId: '28',
        eventTime: '2025-08-07T23:29:30.341926036Z',
        eventType: 'EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '*********',
        childWorkflowExecutionStartedEventAttributes: {
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          initiatedEventId: '27',
          workflowExecution: {
            workflowId: 'refund-transaction-mock-holding-transaction-id-3',
            runId: 'mock-child-run-id-3',
          },
          workflowType: {
            name: 'refundWorkflow',
          },
          header: {},
        },
      },
      {
        eventId: '29',
        eventTime: '2025-08-07T23:29:30.341934635Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '*********',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '30',
        eventTime: '2025-08-07T23:29:30.350673509Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185778900',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '29',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-9',
          historySizeBytes: '6994',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '31',
        eventTime: '2025-08-07T23:29:30.568448917Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185778904',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '29',
          startedEventId: '30',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '32',
        eventTime: '2025-08-08T00:12:51.274983587Z',
        eventType: 'EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '*********',
        childWorkflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          workflowExecution: {
            workflowId: 'refund-transaction-mock-holding-transaction-id-3',
            runId: 'mock-child-run-id-3',
          },
          workflowType: {
            name: 'refundWorkflow',
          },
          initiatedEventId: '27',
          startedEventId: '28',
        },
      },
      {
        eventId: '33',
        eventTime: '2025-08-08T00:12:51.274991316Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '*********',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '34',
        eventTime: '2025-08-08T00:12:51.286969665Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185789611',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '33',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-10',
          historySizeBytes: '7741',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '35',
        eventTime: '2025-08-08T00:12:51.520810227Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185789616',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '33',
          startedEventId: '34',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '36',
        eventTime: '2025-08-08T00:12:51.520937625Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '185789617',
        activityTaskScheduledEventAttributes: {
          activityId: '4',
          activityType: {
            name: 'upfrontFinaliseCancelled',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0zIiwidG9wVXBUcmFuc2FjdGlvbklkIjoibW9jay10b3B1cC10cmFuc2FjdGlvbi1pZC0zIiwiaG9sZGluZ0Ftb3VudENlbnRzIjoxMjEwLCJmbG93U3RlcCI6ImNhbmNlbGxlZCJ9',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '35',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '37',
        eventTime: '2025-08-08T00:12:51.520977935Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '185789620',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '36',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-11',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '38',
        eventTime: '2025-08-08T00:12:51.760682543Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '185789621',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '36',
          startedEventId: '37',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '39',
        eventTime: '2025-08-08T00:12:51.760690896Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '185789622',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-3',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '40',
        eventTime: '2025-08-08T00:12:51.769314092Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '185789626',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '39',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-12',
          historySizeBytes: '8883',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
        },
      },
      {
        eventId: '41',
        eventTime: '2025-08-08T00:12:51.980889656Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '185789630',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '39',
          startedEventId: '40',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+f17dbfc891dd76175f6394a2a2de4686744be99f5a1342105442a2573cb03107',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '42',
        eventTime: '2025-08-08T00:12:51.980956551Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '185789631',
        workflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0zIiwiZmluYWxpc2F0aW9uIjoiY2FuY2VsbGVkIiwicmVmdW5kQW1vdW50Q2VudHMiOjEyMTAsInJlYXNvbiI6IlVzZXIgY2FuY2VsbGVkIHRoZSBvcmRlciJ9',
              },
            ],
          },
          workflowTaskCompletedEventId: '41',
        },
      },
    ],
  },
};
