export const upfrontNoChangeFromHoldingWorkflowHistory = {
  workflowId: 'upfront-payment-order-mock-order-id-2',
  history: {
    events: [
      {
        eventId: '1',
        eventTime: '2025-08-09T03:25:24.285355829Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '206029051',
        workflowExecutionStartedEventAttributes: {
          workflowType: {
            name: 'upfrontPaymentWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MjU5NSwiZGVzY3JpcHRpb24iOiJTbm9vcHkncyBCIGQ3NTc1Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMiIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTIiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMiIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0yIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0yIn0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0yIg==',
              },
            ],
          },
          workflowTaskTimeout: '10s',
          originalExecutionRunId: 'mock-execution-run-id-2',
          identity: 'mock-worker-identity-1',
          firstExecutionRunId: 'mock-execution-run-id-2',
          retryPolicy: {
            initialInterval: '1s',
            backoffCoefficient: 2,
            maximumInterval: '100s',
            maximumAttempts: 1,
          },
          attempt: 1,
          firstWorkflowTaskBackoff: '2s',
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTIiXQ==',
              },
            },
          },
          header: {},
          workflowId: 'upfront-payment-order-mock-order-id-2',
        },
      },
      {
        eventId: '2',
        eventTime: '2025-08-09T03:25:26.289163706Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029056',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '3',
        eventTime: '2025-08-09T03:25:26.303751875Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206029059',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '2',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-1',
          historySizeBytes: '907',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '4',
        eventTime: '2025-08-09T03:25:26.526789515Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206029064',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '2',
          startedEventId: '3',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {
            coreUsedFlags: [1, 2, 3],
            sdkName: 'temporal-typescript',
            sdkVersion: '1.12.1',
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '5',
        eventTime: '2025-08-09T03:25:26.526890937Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029065',
        activityTaskScheduledEventAttributes: {
          activityId: '1',
          activityType: {
            name: 'upfrontStart',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MjU5NSwiZGVzY3JpcHRpb24iOiJTbm9vcHkncyBCIGQ3NTc1Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMiIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTIiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMiIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0yIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0yIn0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '4',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '6',
        eventTime: '2025-08-09T03:25:26.526929213Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '206029069',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '5',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-2',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '7',
        eventTime: '2025-08-09T03:25:27.125256058Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '206029070',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '5',
          startedEventId: '6',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '8',
        eventTime: '2025-08-09T03:25:27.125264271Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029071',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '9',
        eventTime: '2025-08-09T03:25:27.135248209Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206029075',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '8',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-3',
          historySizeBytes: '2262',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '10',
        eventTime: '2025-08-09T03:25:27.353595712Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206029080',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '8',
          startedEventId: '9',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '11',
        eventTime: '2025-08-09T03:25:27.353679600Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029081',
        activityTaskScheduledEventAttributes: {
          activityId: '2',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MjU5NSwiYnV5ZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0yIiwiYnV5ZXJUZXh0IjoiU25vb3B5J3MgQiBkNzU3NSIsImRlc2NyaXB0aW9uIjoiU25vb3B5J3MgQiBkNzU3NSIsIm9yZGVySWRzIjpbIm1vY2stb3JkZXItaWQtMiJdLCJpbnZvaWNlSWRzIjpbIm1vY2staW52b2ljZS1pZC0yIl0sInBheW1lbnRNZXRob2RJZCI6Im1vY2stcGF5bWVudC1tZXRob2QtaWQtMiIsInNlbGxlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIiwic2VsbGVyVGV4dCI6IlNub29weSdzIEIgZDc1NzUiLCJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTIiLCJyZWxhdGVkVHJhbnNhY3Rpb25zIjpbXSwiY29udGV4dCI6eyJvcmlnaW5hbFBheUluQW1vdW50Q2VudHMiOjI1OTV9fQ==',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '10',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '12',
        eventTime: '2025-08-09T03:25:27.353719099Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '206029084',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '11',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-4',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '13',
        eventTime: '2025-08-09T03:25:32.880677070Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '206029085',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJqb3VybmFsSWQiOiJtb2NrLWpvdXJuYWwtaWQtMiIsInJldHVybmVkIjp7fX0=',
              },
            ],
          },
          scheduledEventId: '11',
          startedEventId: '12',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '14',
        eventTime: '2025-08-09T03:25:32.880683339Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029086',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '15',
        eventTime: '2025-08-09T03:25:32.891424475Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206029090',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '14',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-5',
          historySizeBytes: '3799',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '16',
        eventTime: '2025-08-09T03:25:33.109947155Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206029095',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '14',
          startedEventId: '15',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '17',
        eventTime: '2025-08-09T03:25:33.110030509Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029096',
        activityTaskScheduledEventAttributes: {
          activityId: '3',
          activityType: {
            name: 'upfrontHoldingTransactionSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0yIiwidXBmcm9udCI6eyJhbW91bnRDZW50cyI6MjU5NSwiZGVzY3JpcHRpb24iOiJTbm9vcHkncyBCIGQ3NTc1Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMiIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTIiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMiIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0yIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0yIn19',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '16',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '18',
        eventTime: '2025-08-09T03:25:33.110062369Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '206029099',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '17',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-6',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '19',
        eventTime: '2025-08-09T03:25:33.677929674Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '206029100',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '17',
          startedEventId: '18',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '20',
        eventTime: '2025-08-09T03:25:33.677936386Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206029101',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '21',
        eventTime: '2025-08-09T03:25:33.688534238Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206029105',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '20',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-7',
          historySizeBytes: '5215',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '22',
        eventTime: '2025-08-09T03:25:33.922982993Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206029109',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '20',
          startedEventId: '21',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '23',
        eventTime: '2025-08-10T02:01:10.436301522Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED',
        version: '1991',
        taskId: '206378749',
        workflowExecutionSignaledEventAttributes: {
          signalName: 'upfront-payment:finalised',
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJvcmRlcklkIjoibW9jay1vcmRlci1pZC0yIiwiZmluYWxBbW91bnRDZW50cyI6MjU5NSwiZGVzY3JpcHRpb24iOiJTbm9vcHkncyBCIE9NSTE3MzE0IiwiZGlmZmVyZW5jZUNlbnRzIjowLCJ0eXBlIjoiZmluYWxpc2VkIn0=',
              },
            ],
          },
          identity: 'mock-worker-identity-3',
          header: {},
        },
      },
      {
        eventId: '24',
        eventTime: '2025-08-10T02:01:10.436305223Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206378750',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '25',
        eventTime: '2025-08-10T02:01:10.446922579Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206378754',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '24',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-8',
          historySizeBytes: '5994',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '26',
        eventTime: '2025-08-10T02:01:10.672244732Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206378759',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '24',
          startedEventId: '25',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {
            langUsedFlags: [2],
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '27',
        eventTime: '2025-08-10T02:01:10.672345711Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '206378760',
        activityTaskScheduledEventAttributes: {
          activityId: '4',
          activityType: {
            name: 'upfrontFinaliseSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MjU5NSwiZGVzY3JpcHRpb24iOiJTbm9vcHkncyBCIGQ3NTc1Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtMiIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTIiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtMiIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC0yIiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC0yIn0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0yIiwidG9wVXBUcmFuc2FjdGlvbklkIjoibW9jay10b3B1cC10cmFuc2FjdGlvbi1pZC0yIiwiaG9sZGluZ0Ftb3VudENlbnRzIjoyNTk1LCJmbG93U3RlcCI6IndhaXRpbmctZm9yLWZpbmFsaXNlLXBheW1lbnQifQ==',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '26',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '28',
        eventTime: '2025-08-10T02:01:10.672386875Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '206378763',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '27',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-9',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '29',
        eventTime: '2025-08-10T02:01:12.541911341Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '206378764',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '27',
          startedEventId: '28',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '30',
        eventTime: '2025-08-10T02:01:12.541917397Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206378765',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '31',
        eventTime: '2025-08-10T02:01:12.551905307Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206378769',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '30',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-10',
          historySizeBytes: '7553',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '32',
        eventTime: '2025-08-10T02:01:12.766293670Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206378774',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '30',
          startedEventId: '31',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '33',
        eventTime: '2025-08-10T02:01:12.766375761Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '206378775',
        activityTaskScheduledEventAttributes: {
          activityId: '5',
          activityType: {
            name: 'transactionsReleaseNow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTIiLCJzZXR0bGVtZW50QW1vdW50Q2VudHMiOjI1OTUsInNlbGxlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC0yIn0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '32',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '34',
        eventTime: '2025-08-10T02:01:12.766416671Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '206378778',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '33',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-10',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '35',
        eventTime: '2025-08-10T02:01:12.996409654Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '206378779',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '33',
          startedEventId: '34',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '36',
        eventTime: '2025-08-10T02:01:12.996415274Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '206378780',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '37',
        eventTime: '2025-08-10T02:01:13.002562478Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '206378784',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '36',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-11',
          historySizeBytes: '8656',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
        },
      },
      {
        eventId: '38',
        eventTime: '2025-08-10T02:01:13.217182246Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '206378788',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '36',
          startedEventId: '37',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.12.1+773e7146d22a019eb3da973d906c65da1725b8d5c81a68f2240dd7395186a50f',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '39',
        eventTime: '2025-08-10T02:01:13.217260513Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '206378789',
        workflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC0yIiwiZmluYWxpc2F0aW9uIjoiZXhhY3QifQ==',
              },
            ],
          },
          workflowTaskCompletedEventId: '38',
        },
      },
    ],
  },
};
