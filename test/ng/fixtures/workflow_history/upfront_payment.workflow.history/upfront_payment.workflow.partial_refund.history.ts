export const upfrontPartialRefundWorkflowHistory = {
  workflowId: 'upfront-payment-order-mock-order-id-4',
  history: {
    events: [
      {
        eventId: '1',
        eventTime: '2025-08-01T03:06:12.896111968Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '181783121',
        workflowExecutionStartedEventAttributes: {
          workflowType: {
            name: 'upfrontPaymentWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NjE1LCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIGRkMmU5Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC00IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTQiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNCIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC00IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC00In0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC00Ig==',
              },
            ],
          },
          workflowTaskTimeout: '10s',
          originalExecutionRunId: 'mock-execution-run-id-4',
          identity: 'mock-worker-identity-1',
          firstExecutionRunId: 'mock-execution-run-id-4',
          retryPolicy: {
            initialInterval: '1s',
            backoffCoefficient: 2,
            maximumInterval: '100s',
            maximumAttempts: 1,
          },
          attempt: 1,
          firstWorkflowTaskBackoff: '2s',
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTQiXQ==',
              },
            },
          },
          header: {},
          workflowId: 'upfront-payment-order-mock-order-id-4',
        },
      },
      {
        eventId: '2',
        eventTime: '2025-08-01T03:06:14.900645837Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783127',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '3',
        eventTime: '2025-08-01T03:06:14.910051254Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783130',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '2',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-1',
          historySizeBytes: '906',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '4',
        eventTime: '2025-08-01T03:06:15.139714754Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783135',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '2',
          startedEventId: '3',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {
            coreUsedFlags: [2, 3, 1],
            sdkName: 'temporal-typescript',
            sdkVersion: '1.11.8',
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '5',
        eventTime: '2025-08-01T03:06:15.139816135Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783136',
        activityTaskScheduledEventAttributes: {
          activityId: '1',
          activityType: {
            name: 'upfrontStart',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NjE1LCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIGRkMmU5Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC00IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTQiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNCIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC00IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC00In0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '4',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '6',
        eventTime: '2025-08-01T03:06:15.139858047Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '181783140',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '5',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-2',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '7',
        eventTime: '2025-08-01T03:06:15.777606351Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '181783141',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '5',
          startedEventId: '6',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '8',
        eventTime: '2025-08-01T03:06:15.777612767Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783142',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '9',
        eventTime: '2025-08-01T03:06:15.785706958Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783146',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '8',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-3',
          historySizeBytes: '2255',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '10',
        eventTime: '2025-08-01T03:06:16.002075081Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783151',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '8',
          startedEventId: '9',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '11',
        eventTime: '2025-08-01T03:06:16.002186415Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783152',
        activityTaskScheduledEventAttributes: {
          activityId: '2',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NjE1LCJidXllcklkIjoibW9jay1idXllci11c2VyLWlkLTQiLCJidXllclRleHQiOiJtZi1zYW5kYm94cyBkZDJlOSIsImRlc2NyaXB0aW9uIjoibWYtc2FuZGJveHMgZGQyZTkiLCJvcmRlcklkcyI6WyJtb2NrLW9yZGVyLWlkLTQiXSwiaW52b2ljZUlkcyI6WyJtb2NrLWludm9pY2UtaWQtNCJdLCJwYXltZW50TWV0aG9kSWQiOiJtb2NrLXBheW1lbnQtbWV0aG9kLWlkLTQiLCJzZWxsZXJJZCI6Im1vY2stc2VsbGVyLXVzZXItaWQtNCIsInNlbGxlclRleHQiOiJtZi1zYW5kYm94cyBkZDJlOSIsInRyYW5zYWN0aW9uSWQiOiJtb2NrLWhvbGRpbmctdHJhbnNhY3Rpb24taWQtNCIsInJlbGF0ZWRUcmFuc2FjdGlvbnMiOltdLCJjb250ZXh0Ijp7Im9yaWdpbmFsUGF5SW5BbW91bnRDZW50cyI6NjE1fX0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '10',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '12',
        eventTime: '2025-08-01T03:06:16.002241044Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '181783156',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '11',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-4',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '13',
        eventTime: '2025-08-01T03:06:21.614617350Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '181783157',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJqb3VybmFsSWQiOiJtb2NrLWpvdXJuYWwtaWQtNCIsInJldHVybmVkIjp7fX0=',
              },
            ],
          },
          scheduledEventId: '11',
          startedEventId: '12',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '14',
        eventTime: '2025-08-01T03:06:21.614623972Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783158',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '15',
        eventTime: '2025-08-01T03:06:21.625020330Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783162',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '14',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-5',
          historySizeBytes: '3785',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '16',
        eventTime: '2025-08-01T03:06:21.840995364Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783167',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '14',
          startedEventId: '15',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '17',
        eventTime: '2025-08-01T03:06:21.841098214Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783168',
        activityTaskScheduledEventAttributes: {
          activityId: '3',
          activityType: {
            name: 'upfrontHoldingTransactionSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC00IiwidXBmcm9udCI6eyJhbW91bnRDZW50cyI6NjE1LCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIGRkMmU5Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC00IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTQiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNCIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC00IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC00In19',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '16',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '18',
        eventTime: '2025-08-01T03:06:21.841142916Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '181783183',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '17',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-6',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '19',
        eventTime: '2025-08-01T03:06:23.132164978Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '181783184',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '17',
          startedEventId: '18',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '20',
        eventTime: '2025-08-01T03:06:23.132177196Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783185',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '21',
        eventTime: '2025-08-01T03:06:23.143908509Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783189',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '20',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-7',
          historySizeBytes: '5197',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '22',
        eventTime: '2025-08-01T03:06:23.361807383Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783193',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '20',
          startedEventId: '21',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '23',
        eventTime: '2025-08-01T03:07:37.045820462Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED',
        version: '1991',
        taskId: '181783543',
        workflowExecutionSignaledEventAttributes: {
          signalName: 'upfront-payment:finalised',
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJvcmRlcklkIjoibW9jay1vcmRlci1pZC00IiwiZmluYWxBbW91bnRDZW50cyI6MjIyLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIE9NSTcwMiIsImRpZmZlcmVuY2VDZW50cyI6LTM5MywidHlwZSI6ImZpbmFsaXNlZCJ9',
              },
            ],
          },
          identity: 'mock-worker-identity-1',
          header: {},
        },
      },
      {
        eventId: '24',
        eventTime: '2025-08-01T03:07:37.045823974Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783544',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '25',
        eventTime: '2025-08-01T03:07:37.055626165Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783548',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '24',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-8',
          historySizeBytes: '5970',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '26',
        eventTime: '2025-08-01T03:07:37.278811211Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783553',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '24',
          startedEventId: '25',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {
            langUsedFlags: [2],
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '27',
        eventTime: '2025-08-01T03:07:37.278922209Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783554',
        activityTaskScheduledEventAttributes: {
          activityId: '4',
          activityType: {
            name: 'upfrontFinaliseSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6NjE1LCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIGRkMmU5Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC00IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNCIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTQiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNCIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC00IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC00In0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC00IiwidG9wVXBUcmFuc2FjdGlvbklkIjoibW9jay10b3B1cC10cmFuc2FjdGlvbi1pZC00IiwiaG9sZGluZ0Ftb3VudENlbnRzIjo2MTUsImZsb3dTdGVwIjoid2FpdGluZy1mb3ItZmluYWxpc2UtcGF5bWVudCJ9',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '26',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '28',
        eventTime: '2025-08-01T03:07:37.278967936Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '181783557',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '27',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-9',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '29',
        eventTime: '2025-08-01T03:07:38.220060937Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '181783558',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '27',
          startedEventId: '28',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '30',
        eventTime: '2025-08-01T03:07:38.220068781Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783559',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '31',
        eventTime: '2025-08-01T03:07:38.230959807Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783563',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '30',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-10',
          historySizeBytes: '7520',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '32',
        eventTime: '2025-08-01T03:07:38.447304188Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783568',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '30',
          startedEventId: '31',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '33',
        eventTime: '2025-08-01T03:07:38.447384902Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783569',
        activityTaskScheduledEventAttributes: {
          activityId: '5',
          activityType: {
            name: 'transactionsReleaseNow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTQiLCJzZXR0bGVtZW50QW1vdW50Q2VudHMiOjIyMiwic2VsbGVySWQiOiJtb2NrLXNlbGxlci11c2VyLWlkLTQifQ==',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '32',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '34',
        eventTime: '2025-08-01T03:07:38.447427568Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '181783572',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '33',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-11',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '35',
        eventTime: '2025-08-01T03:07:38.674033410Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '181783573',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '33',
          startedEventId: '34',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '36',
        eventTime: '2025-08-01T03:07:38.674040417Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '181783574',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '37',
        eventTime: '2025-08-01T03:07:38.681548604Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783578',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '36',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-12',
          historySizeBytes: '8616',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '38',
        eventTime: '2025-08-01T03:07:38.911475952Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783582',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '36',
          startedEventId: '37',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '39',
        eventTime: '2025-08-01T03:07:38.911651262Z',
        eventType: 'EVENT_TYPE_START_CHILD_WORKFLOW_EXECUTION_INITIATED',
        version: '1991',
        taskId: '*********',
        startChildWorkflowExecutionInitiatedEventAttributes: {
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          workflowId: 'refund-transaction-mock-holding-transaction-id-4',
          workflowType: {
            name: 'refundWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTQiLCJyZWZ1bmRBbW91bnRDZW50cyI6MzkzfQ==',
              },
            ],
          },
          workflowRunTimeout: '0s',
          workflowTaskTimeout: '10s',
          parentClosePolicy: 'PARENT_CLOSE_POLICY_TERMINATE',
          workflowTaskCompletedEventId: '38',
          workflowIdReusePolicy: 'WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE',
          header: {},
          memo: {},
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTQiXQ==',
              },
            },
          },
          inheritBuildId: true,
        },
      },
      {
        eventId: '40',
        eventTime: '2025-08-01T03:07:38.931499514Z',
        eventType: 'EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '*********',
        childWorkflowExecutionStartedEventAttributes: {
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          initiatedEventId: '39',
          workflowExecution: {
            workflowId: 'refund-transaction-mock-holding-transaction-id-4',
            runId: 'mock-child-run-id-4',
          },
          workflowType: {
            name: 'refundWorkflow',
          },
          header: {},
        },
      },
      {
        eventId: '41',
        eventTime: '2025-08-01T03:07:38.931509303Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '*********',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '42',
        eventTime: '2025-08-01T03:07:38.938992301Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181783592',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '41',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-13',
          historySizeBytes: '9745',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '43',
        eventTime: '2025-08-01T03:07:39.156560105Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181783596',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '41',
          startedEventId: '42',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '44',
        eventTime: '2025-08-01T03:50:59.044826574Z',
        eventType: 'EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '*********',
        childWorkflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          namespace: 'account-sandbox.qefiw',
          namespaceId: 'fc6caadb-61a3-4a1d-9b6c-0d4bc83597eb',
          workflowExecution: {
            workflowId: 'refund-transaction-mock-holding-transaction-id-4',
            runId: 'mock-child-run-id-4',
          },
          workflowType: {
            name: 'refundWorkflow',
          },
          initiatedEventId: '39',
          startedEventId: '40',
        },
      },
      {
        eventId: '45',
        eventTime: '2025-08-01T03:50:59.044833343Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '*********',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-4',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '46',
        eventTime: '2025-08-01T03:50:59.055439662Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '181810853',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '45',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-14',
          historySizeBytes: '10486',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '47',
        eventTime: '2025-08-01T03:50:59.278539228Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '181810857',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '45',
          startedEventId: '46',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '48',
        eventTime: '2025-08-01T03:50:59.278619170Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '181810858',
        workflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC00IiwiZmluYWxpc2F0aW9uIjoicmVmdW5kIiwicmVmdW5kQW1vdW50Q2VudHMiOjM5M30=',
              },
            ],
          },
          workflowTaskCompletedEventId: '47',
        },
      },
    ],
  },
};
