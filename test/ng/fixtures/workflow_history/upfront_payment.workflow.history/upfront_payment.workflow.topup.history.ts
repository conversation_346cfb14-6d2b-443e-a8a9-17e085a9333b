export const upfrontTopupWorkflowHistory = {
  workflowId: 'upfront-payment-order-mock-order-id-5',
  history: {
    events: [
      {
        eventId: '1',
        eventTime: '2025-08-01T03:17:01.372279532Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_STARTED',
        version: '1991',
        taskId: '187972567',
        workflowExecutionStartedEventAttributes: {
          workflowType: {
            name: 'upfrontPaymentWorkflow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTExLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIDRjN2Q2Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC01IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNSIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTUiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNSIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC01IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC01In0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC01Ig==',
              },
            ],
          },
          workflowTaskTimeout: '10s',
          originalExecutionRunId: 'mock-execution-run-id-5',
          identity: 'mock-worker-identity-1',
          firstExecutionRunId: 'mock-execution-run-id-5',
          retryPolicy: {
            initialInterval: '1s',
            backoffCoefficient: 2,
            maximumInterval: '100s',
            maximumAttempts: 1,
          },
          attempt: 1,
          firstWorkflowTaskBackoff: '2s',
          searchAttributes: {
            indexedFields: {
              CustomKeywordField: {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                  type: 'S2V5d29yZA==',
                },
                data: 'WyJtb2NrLW9yZGVyLWlkLTUiXQ==',
              },
            },
          },
          header: {},
          workflowId: 'upfront-payment-order-mock-order-id-5',
        },
      },
      {
        eventId: '2',
        eventTime: '2025-08-01T03:17:03.375932447Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972572',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '3',
        eventTime: '2025-08-01T03:17:03.384152814Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187972575',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '2',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-1',
          historySizeBytes: '906',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '4',
        eventTime: '2025-08-01T03:17:03.643729466Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187972580',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '2',
          startedEventId: '3',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {
            coreUsedFlags: [2, 1, 3],
            sdkName: 'temporal-typescript',
            sdkVersion: '1.11.8',
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '5',
        eventTime: '2025-08-01T03:17:03.643833744Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972581',
        activityTaskScheduledEventAttributes: {
          activityId: '1',
          activityType: {
            name: 'upfrontStart',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTExLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIDRjN2Q2Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC01IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNSIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTUiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNSIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC01IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC01In0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '4',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '6',
        eventTime: '2025-08-01T03:17:03.643875040Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187972585',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '5',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-2',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '7',
        eventTime: '2025-08-01T03:17:04.299597002Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187972586',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '5',
          startedEventId: '6',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '8',
        eventTime: '2025-08-01T03:17:04.299603287Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972587',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '9',
        eventTime: '2025-08-01T03:17:04.307475258Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187972591',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '8',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-3',
          historySizeBytes: '2258',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '10',
        eventTime: '2025-08-01T03:17:04.526555676Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187972596',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '8',
          startedEventId: '9',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '11',
        eventTime: '2025-08-01T03:17:04.526644028Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972597',
        activityTaskScheduledEventAttributes: {
          activityId: '2',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTExLCJidXllcklkIjoibW9jay1idXllci11c2VyLWlkLTUiLCJidXllclRleHQiOiJtZi1zYW5kYm94cyA0YzdkNiIsImRlc2NyaXB0aW9uIjoibWYtc2FuZGJveHMgNGM3ZDYiLCJvcmRlcklkcyI6WyJtb2NrLW9yZGVyLWlkLTUiXSwiaW52b2ljZUlkcyI6WyJtb2NrLWludm9pY2UtaWQtNSJdLCJwYXltZW50TWV0aG9kSWQiOiJtb2NrLXBheW1lbnQtbWV0aG9kLWlkLTUiLCJzZWxsZXJJZCI6Im1vY2stc2VsbGVyLXVzZXItaWQtNSIsInNlbGxlclRleHQiOiJtZi1zYW5kYm94cyA0YzdkNiIsInRyYW5zYWN0aW9uSWQiOiJtb2NrLWhvbGRpbmctdHJhbnNhY3Rpb24taWQtNSIsInJlbGF0ZWRUcmFuc2FjdGlvbnMiOltdLCJjb250ZXh0Ijp7Im9yaWdpbmFsUGF5SW5BbW91bnRDZW50cyI6MTExfX0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '10',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '12',
        eventTime: '2025-08-01T03:17:04.526685046Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187972601',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '11',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-4',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '13',
        eventTime: '2025-08-01T03:17:11.794169704Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187972602',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJqb3VybmFsSWQiOiJtb2NrLWpvdXJuYWwtaWQtNSIsInJldHVybmVkIjp7fX0=',
              },
            ],
          },
          scheduledEventId: '11',
          startedEventId: '12',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '14',
        eventTime: '2025-08-01T03:17:11.794176194Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972603',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '15',
        eventTime: '2025-08-01T03:17:11.802989090Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187972607',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '14',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-5',
          historySizeBytes: '3792',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '16',
        eventTime: '2025-08-01T03:17:12.019449235Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187972612',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '14',
          startedEventId: '15',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '17',
        eventTime: '2025-08-01T03:17:12.019529316Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972613',
        activityTaskScheduledEventAttributes: {
          activityId: '3',
          activityType: {
            name: 'upfrontHoldingTransactionSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC01IiwidXBmcm9udCI6eyJhbW91bnRDZW50cyI6MTExLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIDRjN2Q2Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC01IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNSIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTUiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNSIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC01IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC01In19',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '16',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '18',
        eventTime: '2025-08-01T03:17:12.019564393Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187972616',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '17',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-6',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '19',
        eventTime: '2025-08-01T03:17:13.322277352Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187972617',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '17',
          startedEventId: '18',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '20',
        eventTime: '2025-08-01T03:17:13.322285163Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187972618',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '21',
        eventTime: '2025-08-01T03:17:13.331904758Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187972622',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '20',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-7',
          historySizeBytes: '5203',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '22',
        eventTime: '2025-08-01T03:17:13.547376808Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187972626',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '20',
          startedEventId: '21',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '23',
        eventTime: '2025-08-01T03:18:55.564192488Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED',
        version: '1991',
        taskId: '187973040',
        workflowExecutionSignaledEventAttributes: {
          signalName: 'upfront-payment:finalised',
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJvcmRlcklkIjoibW9jay1vcmRlci1pZC01IiwiZmluYWxBbW91bnRDZW50cyI6MjIyLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIE9NSTcwNCIsImRpZmZlcmVuY2VDZW50cyI6MTExLCJ0eXBlIjoiZmluYWxpc2VkIn0=',
              },
            ],
          },
          identity: 'mock-worker-identity-3',
          header: {},
        },
      },
      {
        eventId: '24',
        eventTime: '2025-08-01T03:18:55.564196927Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973041',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '25',
        eventTime: '2025-08-01T03:18:55.574689581Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187973045',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '24',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-8',
          historySizeBytes: '5978',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '26',
        eventTime: '2025-08-01T03:18:55.797689847Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187973050',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '24',
          startedEventId: '25',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {
            langUsedFlags: [2],
          },
          meteringMetadata: {},
        },
      },
      {
        eventId: '27',
        eventTime: '2025-08-01T03:18:55.797785173Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973051',
        activityTaskScheduledEventAttributes: {
          activityId: '4',
          activityType: {
            name: 'paymentMethodTransfer',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTExLCJidXllcklkIjoibW9jay1idXllci11c2VyLWlkLTUiLCJidXllclRleHQiOiJtZi1zYW5kYm94cyBPTUk3MDQiLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIDRjN2Q2Iiwib3JkZXJJZHMiOlsibW9jay1vcmRlci1pZC01Il0sImludm9pY2VJZHMiOlsibW9jay1pbnZvaWNlLWlkLTUiXSwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC01Iiwic2VsbGVySWQiOiJtb2NrLXNlbGxlci11c2VyLWlkLTUiLCJzZWxsZXJUZXh0IjoibWYtc2FuZGJveHMgT01JNzA0IiwidHJhbnNhY3Rpb25JZCI6Im1vY2stdG9wdXAtdHJhbnNhY3Rpb24taWQtNSIsInJlbGF0ZWRUcmFuc2FjdGlvbnMiOlsibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTUiXSwiY29udGV4dCI6eyJvcmlnaW5hbFBheUluVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC01Iiwib3JpZ2luYWxQYXlJbkFtb3VudENlbnRzIjoxMTEsInRvcFVwQW1vdW50Q2VudHMiOjExMX19',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '26',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '28',
        eventTime: '2025-08-01T03:18:55.797822990Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187973054',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '27',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-9',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '29',
        eventTime: '2025-08-01T03:19:01.506360153Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187973055',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJqb3VybmFsSWQiOiJtb2NrLWpvdXJuYWwtaWQtNWIiLCJyZXR1cm5lZCI6e319',
              },
            ],
          },
          scheduledEventId: '27',
          startedEventId: '28',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '30',
        eventTime: '2025-08-01T03:19:01.506366987Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973056',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '31',
        eventTime: '2025-08-01T03:19:01.516828865Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187973060',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '30',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-10',
          historySizeBytes: '7646',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '32',
        eventTime: '2025-08-01T03:19:01.734289607Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187973065',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '30',
          startedEventId: '31',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '33',
        eventTime: '2025-08-01T03:19:01.734389783Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973066',
        activityTaskScheduledEventAttributes: {
          activityId: '5',
          activityType: {
            name: 'upfrontFinaliseSucceeded',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJhbW91bnRDZW50cyI6MTExLCJkZXNjcmlwdGlvbiI6Im1mLXNhbmRib3hzIDRjN2Q2Iiwic2VsbGVyVXNlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC01IiwiYnV5ZXJVc2VySWQiOiJtb2NrLWJ1eWVyLXVzZXItaWQtNSIsIm9yZGVySWQiOiJtb2NrLW9yZGVyLWlkLTUiLCJpbnZvaWNlSWQiOiJtb2NrLWludm9pY2UtaWQtNSIsInVzZXJJZCI6Im1vY2stYnV5ZXItdXNlci1pZC01IiwicGF5bWVudE1ldGhvZElkIjoibW9jay1wYXltZW50LW1ldGhvZC1pZC01In0=',
              },
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC01IiwidG9wVXBUcmFuc2FjdGlvbklkIjoibW9jay10b3B1cC10cmFuc2FjdGlvbi1pZC01IiwiaG9sZGluZ0Ftb3VudENlbnRzIjoxMTEsImZsb3dTdGVwIjoid2FpdGluZy1mb3ItZmluYWxpc2UtcGF5bWVudCJ9',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '32',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '34',
        eventTime: '2025-08-01T03:19:01.734434541Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187973069',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '33',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-11',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '35',
        eventTime: '2025-08-01T03:19:02.780698398Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187973070',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '33',
          startedEventId: '34',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '36',
        eventTime: '2025-08-01T03:19:02.780704175Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973071',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '37',
        eventTime: '2025-08-01T03:19:02.791320447Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187973075',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '36',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-12',
          historySizeBytes: '9196',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '38',
        eventTime: '2025-08-01T03:19:03.008859662Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187973081',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '36',
          startedEventId: '37',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '39',
        eventTime: '2025-08-01T03:19:03.008957335Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973082',
        activityTaskScheduledEventAttributes: {
          activityId: '6',
          activityType: {
            name: 'transactionsReleaseNow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay1ob2xkaW5nLXRyYW5zYWN0aW9uLWlkLTUiLCJzZXR0bGVtZW50QW1vdW50Q2VudHMiOjExMSwic2VsbGVySWQiOiJtb2NrLXNlbGxlci11c2VyLWlkLTUifQ==',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '38',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '40',
        eventTime: '2025-08-01T03:19:03.009012219Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973083',
        activityTaskScheduledEventAttributes: {
          activityId: '7',
          activityType: {
            name: 'transactionsReleaseNow',
          },
          taskQueue: {
            name: 'payments:upfront-payment',
            kind: 'TASK_QUEUE_KIND_NORMAL',
          },
          header: {},
          input: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJ0cmFuc2FjdGlvbklkIjoibW9jay10b3B1cC10cmFuc2FjdGlvbi1pZC01Iiwic2V0dGxlbWVudEFtb3VudENlbnRzIjoxMTEsInNlbGxlcklkIjoibW9jay1zZWxsZXItdXNlci1pZC01In0=',
              },
            ],
          },
          scheduleToCloseTimeout: '0s',
          scheduleToStartTimeout: '0s',
          startToCloseTimeout: '60s',
          heartbeatTimeout: '0s',
          workflowTaskCompletedEventId: '38',
          retryPolicy: {
            initialInterval: '10s',
            backoffCoefficient: 2,
            maximumInterval: '3600s',
            maximumAttempts: 3,
          },
          useWorkflowBuildId: true,
        },
      },
      {
        eventId: '41',
        eventTime: '2025-08-01T03:19:03.009033388Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187973086',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '39',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-13',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '42',
        eventTime: '2025-08-01T03:19:03.237342155Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187973087',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '39',
          startedEventId: '41',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '43',
        eventTime: '2025-08-01T03:19:03.237348842Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_SCHEDULED',
        version: '1991',
        taskId: '187973088',
        workflowTaskScheduledEventAttributes: {
          taskQueue: {
            name: 'mock-sticky-queue-5',
            kind: 'TASK_QUEUE_KIND_STICKY',
            normalName: 'payments:upfront-payment',
          },
          startToCloseTimeout: '10s',
          attempt: 1,
        },
      },
      {
        eventId: '44',
        eventTime: '2025-08-01T03:19:03.009060120Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_STARTED',
        version: '1991',
        taskId: '187973093',
        activityTaskStartedEventAttributes: {
          scheduledEventId: '40',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-14',
          attempt: 1,
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '45',
        eventTime: '2025-08-01T03:19:03.245219697Z',
        eventType: 'EVENT_TYPE_ACTIVITY_TASK_COMPLETED',
        version: '1991',
        taskId: '187973094',
        activityTaskCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'YmluYXJ5L251bGw=',
                },
              },
            ],
          },
          scheduledEventId: '40',
          startedEventId: '44',
          identity: 'mock-worker-identity-2',
        },
      },
      {
        eventId: '46',
        eventTime: '2025-08-01T03:19:03.253597683Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_STARTED',
        version: '1991',
        taskId: '187973096',
        workflowTaskStartedEventAttributes: {
          scheduledEventId: '43',
          identity: 'mock-worker-identity-2',
          requestId: 'mock-request-id-15',
          historySizeBytes: '10880',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
        },
      },
      {
        eventId: '47',
        eventTime: '2025-08-01T03:19:03.469993177Z',
        eventType: 'EVENT_TYPE_WORKFLOW_TASK_COMPLETED',
        version: '1991',
        taskId: '187973101',
        workflowTaskCompletedEventAttributes: {
          scheduledEventId: '43',
          startedEventId: '46',
          identity: 'mock-worker-identity-2',
          workerVersion: {
            buildId:
              '@temporalio/worker@1.11.8+79aa974fae3f1a278d5783be0968f16c01c3647a615830b416eb7662c71c3bb6',
          },
          sdkMetadata: {},
          meteringMetadata: {},
        },
      },
      {
        eventId: '48',
        eventTime: '2025-08-01T03:19:03.470109131Z',
        eventType: 'EVENT_TYPE_WORKFLOW_EXECUTION_COMPLETED',
        version: '1991',
        taskId: '187973102',
        workflowExecutionCompletedEventAttributes: {
          result: {
            payloads: [
              {
                metadata: {
                  encoding: 'anNvbi9wbGFpbg==',
                },
                data: 'eyJob2xkaW5nVHJhbnNhY3Rpb25JZCI6Im1vY2staG9sZGluZy10cmFuc2FjdGlvbi1pZC01IiwiZmluYWxpc2F0aW9uIjoidG9wdXAiLCJ0b3BVcFRyYW5zYWN0aW9uSWQiOiJtb2NrLXRvcHVwLXRyYW5zYWN0aW9uLWlkLTUiLCJ0b3BVcEFtb3VudENlbnRzIjoxMTF9',
              },
            ],
          },
          workflowTaskCompletedEventId: '47',
        },
      },
    ],
  },
};
