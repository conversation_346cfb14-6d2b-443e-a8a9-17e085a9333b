import { stubInterface, ObjectMethodsMap } from 'ts-sinon';
import { ZaiFundTransferGateway } from '../../../src/ng/domain/services/gateways/zai_fund_transfer.gateway';

/**
 * Create a Zai fund transfer gateway fixture
 * @param props - The props
 * @returns The Zai fund transfer gateway fixture
 */
export const zaiFundTransferGatewayFixture = (
  overrides?: ObjectMethodsMap<ZaiFundTransferGateway>
) => {
  const stub = stubInterface<ZaiFundTransferGateway>({
    getOrCreateItem: Promise.resolve('item123'),
    makePayment: Promise.resolve(),
    partialRefund: Promise.resolve(),
    ...(overrides ?? {}),
  });

  return stub;
};
