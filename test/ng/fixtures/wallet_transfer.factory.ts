import { Sync } from 'factory.ts';
import { WalletTransferParams } from '../../../src/ng/workflow/types/wallet_transfer.types';
import { CurrencyCodes } from '../../../src/ng/common/types/currency.types';

/**
 * Fixture factory for WalletTransferParams
 */
export const walletTransferArgsFactory = Sync.makeFactory<WalletTransferParams>(
  {
    description: 'Test wallet transfer',
    amountCents: 0,
    recipientId: 'recipient-123',
    recipientText: 'Test Recipient',
    senderId: 'sender-456',
    senderText: 'Test Sender',
    currencyCode: CurrencyCodes.AUD,
    orderIds: ['order-789'],
    invoiceIds: ['invoice-101'],
    transactionId: 'txn-wallet-123',
    transactionType: 'escrow',
    relatedTransactions: [],
  }
);
