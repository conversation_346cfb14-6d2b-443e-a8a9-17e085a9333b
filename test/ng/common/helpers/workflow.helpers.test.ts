import sinon from 'sinon';
import { expect } from 'chai';
import * as workflow from '@temporalio/workflow';
import { compensate } from '../../../../src/ng/common/helpers/workflow.helpers';

describe('Workflow Helpers', () => {
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sandbox.stub(workflow, 'log').value({
      info: sandbox.stub(),
      error: sandbox.stub(),
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('compensate', () => {
    it('should compensate for all compensations', async () => {
      const compensations = [
        {
          message: 'Compensation 1',
          fn: sandbox.stub().resolves(),
        },
        {
          message: 'Compensation 2',
          fn: sandbox.stub().resolves(),
        },
      ];

      await compensate(compensations);

      expect(compensations[0].fn.called).to.be.true;
      expect(compensations[1].fn.called).to.be.true;
    });

    it('should not log anything when compensations array is empty', async () => {
      const compensations: any[] = [];

      await compensate(compensations);
    });

    it('should continue compensating even if one compensation fails', async () => {
      const error = new Error('Compensation failed');
      const compensations = [
        {
          message: 'Compensation 1',
          fn: sandbox.stub().rejects(error),
        },
        {
          message: 'Compensation 2',
          fn: sandbox.stub().resolves(),
        },
      ];

      await compensate(compensations);

      expect(compensations[0].fn.called).to.be.true;
      expect(compensations[1].fn.called).to.be.true;
    });

    it('should log error messages for each compensation', async () => {
      const compensations = [
        {
          message: 'Failed to rollback payment',
          fn: sandbox.stub().resolves(),
        },
      ];

      await compensate(compensations);
    });
  });
});
