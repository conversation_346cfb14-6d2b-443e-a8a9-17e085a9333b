import sinon, { createStubInstance, SinonSandbox } from 'sinon';
import { StubbedInstance, stubInterface } from 'ts-sinon';
import { expect } from 'chai';
import { MockActivityEnvironment } from '@temporalio/testing';
import { uuid4 } from '@temporalio/workflow';
import { Sequelize, Transaction } from 'sequelize';
import { createFundingActivities } from '../../../../src/ng/workflow/activities/wallet.activities';
import logger from '../../../../src/lib/logger';
import {
  userFactory,
  userRepositoryFactory,
} from '../../fixtures/user.fixture';
import { paymentMethodServiceFactory } from '../../fixtures/payment_method_service.fixture';
import { zaiFundTransferGatewayFixture } from '../../fixtures/zai_fund_transfer_gateway.fixture';
import { OperationStates } from '../../../../src/types/journal_types';
import { JournalV3 } from '../../../../src/models/journal_v3_model';
import { holdFundsInEscrowFactory } from '../../fixtures/payment_method_transfer.fixture';
import { PaymentMethodService } from '../../../../src/ng/domain/services/domain/payment_method.service';
import { UserRepository } from '../../../../src/ng/domain/repositories/user.repository';
import { ZaiFundTransferGateway } from '../../../../src/ng/domain/services/gateways/zai_fund_transfer.gateway';
import { JournalEntryV3 } from '../../../../src/models/journal_v3_entry_model';
import { CurrencyCodes } from '../../../../src/types/currency_codes';
import { TransactionService } from '../../../../src/ng/domain/services/domain/transaction.service';
import { TransactionsRepository } from '../../../../src/ng/domain/repositories/transactions.repository';
import { ZaiWalletsGateway } from '../../../../src/ng/domain/services/gateways/zai_wallets.gateway';
import { zaiWalletsGatewayFixture } from '../../fixtures/zai_wallet_gateway.fixture';
import {
  transactionFactory,
  transactionServiceFactory,
  transactionsRepositoryFactory,
} from '../../fixtures/transaction.fixture';
import { TransactionAttributes } from '../../../../src/models/transaction';
import { silentLogger } from '../../../helpers/temporal_test.helpers';

interface HoldFundsResult {
  journalId?: string;
  returned: Record<string, unknown>;
}

describe('Wallet Activities', () => {
  const buyerId = uuid4();
  const sellerId = uuid4();

  let sandbox: SinonSandbox;
  let sequelize: StubbedInstance<Sequelize>;
  let zaiFundsGateway: StubbedInstance<ZaiFundTransferGateway>;
  let userRepository: StubbedInstance<UserRepository>;
  let paymentMethodService: StubbedInstance<PaymentMethodService>;
  let transaction: TransactionAttributes;
  let transactionService: StubbedInstance<TransactionService>;
  let transactionsRepository: StubbedInstance<TransactionsRepository>;
  let zaiWalletsGateway: StubbedInstance<ZaiWalletsGateway>;
  let activities: ReturnType<typeof createFundingActivities>;
  let request: {
    description: string;
    amountCents: number;
    sellerId: string;
    sellerText: string;
    buyerId: string;
    buyerText: string;
    currencyCode?: CurrencyCodes;
    paymentMethodId: string;
    orderIds: string[];
    invoiceIds: string[];
    transactionId: string;
    relatedTransactions: string[];
  };

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    sandbox.restore();
    transaction = transactionFactory.build({
      card_payment_method_id: uuid4(),
    });
    sequelize = stubInterface<Sequelize>();
    sequelize.transaction.callsFake(((
      callback: (txn: Transaction) => Promise<unknown>
    ) => {
      const mockTxn = {} as Transaction;
      return callback(mockTxn);
    }) as typeof sequelize.transaction);
    zaiFundsGateway = zaiFundTransferGatewayFixture();
    userRepository = userRepositoryFactory({
      users: [{ id: buyerId }, { id: sellerId }],
    });
    paymentMethodService = paymentMethodServiceFactory();
    transactionsRepository = transactionsRepositoryFactory({
      transactions: [transaction],
    });
    transactionService = transactionServiceFactory();
    zaiWalletsGateway = zaiWalletsGatewayFixture();

    activities = createFundingActivities(
      transactionsRepository,
      transactionService,
      zaiFundsGateway,
      zaiWalletsGateway,
      userRepository,
      paymentMethodService,
      logger
    );

    request = holdFundsInEscrowFactory.build({ sellerId, buyerId });
  });

  describe('paymentMethodTransfer', () => {
    it('should succeed with valid data', async () => {
      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );
      const result = (await env.run(
        activities.paymentMethodTransfer,
        request
      )) as HoldFundsResult;

      // ASSERT
      expect(result).to.be.ok;
      expect(result.journalId).to.be.ok;
      expect(result.returned).to.be.ok;

      const journal = await JournalV3.findByPk(result.journalId, {
        include: [
          {
            model: JournalEntryV3,
            as: 'entries',
          },
        ],
      });

      expect(journal).to.be.ok;
      expect(journal?.state).to.equal(OperationStates.Succeeded);
      expect(journal?.totalCents).to.equal(request.amountCents);

      expect(journal?.entries.length).to.equal(2);
      expect(journal?.entries[0].transactionId).to.equal(request.transactionId);
      expect(journal?.entries[0].completedAt).to.be.ok;
      expect(journal?.entries[1].transactionId).to.equal(request.transactionId);
      expect(journal?.entries[1].completedAt).to.be.ok;

      expect(zaiFundsGateway.getOrCreateItem.calledOnce).to.be.true;
      expect(zaiFundsGateway.getOrCreateItem.firstCall.args).to.deep.equal([
        {
          amount: request.amountCents,
          currency: CurrencyCodes.AUD,
          description: 'Buyer',
          externalBuyerId: 'ext_user123',
          externalSellerId: 'ext_user123',
          id: request.transactionId,
          name: 'Buyer',
        },
      ]);
      expect(zaiFundsGateway.makePayment.calledOnce).to.be.true;
    });

    it('should throw an error if the payment methods are not found', async () => {
      // ARRANGE
      paymentMethodService.getBackendAccountId.resolves(undefined);

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.paymentMethodTransfer, request);

        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Payment method not found');
      }
    });

    it('should throw an error if the buyer is not found', async () => {
      // ARRANGE
      userRepository.getUserById.onCall(0).resolves(null);
      userRepository.getUserById.onCall(1).resolves(userFactory.build());

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.paymentMethodTransfer, request);

        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Sender not found');
      }
    });

    it('should throw an error if the seller is not found', async () => {
      // ARRANGE
      userRepository.getUserById.onCall(0).resolves(userFactory.build());
      userRepository.getUserById.onCall(1).resolves(null);

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.paymentMethodTransfer, request);

        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Recipient not found');
      }
    });
  });

  describe('refundTransaction', () => {
    it('should successfully process a partial refund', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'completed',
        net_amount: 1000,
        refunded_amount: 0,
      });

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );
      await env.run(activities.refundTransaction, payload);

      // ASSERT
      expect(zaiFundsGateway.partialRefund.calledOnce).to.be.true;
      expect(zaiFundsGateway.partialRefund.firstCall.args).to.deep.equal([
        transaction.id,
        refundAmountCents,
      ]);

      expect(transactionsRepository.getTransactionById.calledOnce).to.be.true;
      expect(
        transactionsRepository.getTransactionById.firstCall.args
      ).to.deep.equal([transaction.id]);
    });

    it('should skip refund if transaction is already refunded', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'refunded',
        net_amount: 1000,
        refunded_amount: refundAmountCents,
      });

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );
      const result = await env.run(activities.refundTransaction, payload);

      // ASSERT
      expect(zaiFundsGateway.partialRefund.called).to.be.false;
    });

    it('should skip refund if refund is pending', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'refund_pending',
        net_amount: 1000,
        refunded_amount: refundAmountCents,
      });

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );
      await env.run(activities.refundTransaction, payload);

      // ASSERT
      expect(zaiFundsGateway.partialRefund.called).to.be.false;
    });

    it('should throw an error if refund amount exceeds remaining amount', async () => {
      // ARRANGE
      const refundAmountCents = 1500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'completed',
        net_amount: 1000,
        refunded_amount: 0,
      });

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.refundTransaction, payload);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal(
          'Refund amount exceeds remaining amount of provider item'
        );
      }
    });

    it('should throw an error if provider item is not found', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves(undefined);

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.refundTransaction, payload);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal('Provider item not found');
      }
    });

    it('should throw an error if provider item has no net amount', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'completed',
        net_amount: undefined,
        refunded_amount: 0,
      });

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.refundTransaction, payload);
        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal(
          'No remaining amount found on provider item'
        );
      }
    });

    it('should throw an error if the transaction is not found', async () => {
      // ARRANGE
      const nonExistentTransactionId = 'non-existent-id';
      const payload = {
        transactionId: nonExistentTransactionId,
        refundAmountCents: 500,
      };

      transactionsRepository.getTransactionById
        .withArgs(nonExistentTransactionId)
        .resolves(null);

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.refundTransaction, payload);

        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(error.message).to.equal(
          `Refund transaction ${nonExistentTransactionId} not found`
        );
      }
    });

    it('should handle errors from the Zai gateway', async () => {
      // ARRANGE
      const refundAmountCents = 500;
      const payload = {
        transactionId: transaction.id ?? '',
        refundAmountCents,
      };

      zaiFundsGateway.getItem.resolves({
        id: transaction.id ?? '',
        name: transaction.name,
        amount: 1000,
        state: 'completed',
        net_amount: 1000,
        refunded_amount: 0,
      });

      const gatewayError = new Error('Zai gateway error');
      zaiFundsGateway.partialRefund.rejects(gatewayError);

      // ACT
      const env = new MockActivityEnvironment(
        { attempt: 2 },
        { logger: silentLogger }
      );

      try {
        await env.run(activities.refundTransaction, payload);

        expect.fail('Expected error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
        expect(zaiFundsGateway.partialRefund.calledOnce).to.be.true;
      }
    });
  });
});
