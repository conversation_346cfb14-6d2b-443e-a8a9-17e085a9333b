import { Worker } from '@temporalio/worker';
import { createSandbox, SinonSandbox } from 'sinon';
import { expect } from 'chai';
import { upfrontNoChangeFromHoldingWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.no_change_from_holding.history';
import { upfrontPartialRefundWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.partial_refund.history';
import { upfrontTopupWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.topup.history';
import { upfrontFailedWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.failed.history';
import { upfrontCancelledWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.cancelled.history';
import logger from '../../../../src/lib/logger';

describe('upfrontPaymentWorkflow', () => {
  let sandbox: SinonSandbox;

  beforeEach(async () => {
    sandbox = createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('replay histories', () => {
    it('should replay the upfront failed workflow history', async () => {
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        [upfrontFailedWorkflowHistory]
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });

    it('should replay the upfront no change from holding workflow history', async () => {
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        [upfrontNoChangeFromHoldingWorkflowHistory]
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });

    it('should replay the upfront partial refund workflow history', async () => {
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        [upfrontPartialRefundWorkflowHistory]
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });

    it('should replay the upfront topup workflow history', async () => {
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        [upfrontTopupWorkflowHistory]
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });

    it('should replay the upfront cancelled workflow history', async () => {
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        [upfrontCancelledWorkflowHistory]
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });
  });
});
