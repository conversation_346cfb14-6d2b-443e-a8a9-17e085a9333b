import { Worker } from '@temporalio/worker';
import { createSandbox, SinonSandbox } from 'sinon';
import { expect } from 'chai';
import { upfrontNoChangeFromHoldingWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.no_change_from_holding.history';
import { upfrontPartialRefundWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.partial_refund.history';
import { upfrontTopupWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.topup.history';
import { upfrontFailedWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.failed.history';
import { upfrontCancelledWorkflowHistory } from '../../fixtures/workflow_history/upfront_payment.workflow.history/upfront_payment.workflow.cancelled.history';
import logger from '../../../../src/lib/logger';

describe('upfrontPaymentWorkflow', () => {
  let sandbox: SinonSandbox;

  beforeEach(async () => {
    sandbox = createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('replay histories', () => {
    it(`should replay the workflow history`, async () => {
      const histories = [
        upfrontFailedWorkflowHistory,
        upfrontNoChangeFromHoldingWorkflowHistory,
        upfrontPartialRefundWorkflowHistory,
        upfrontTopupWorkflowHistory,
        upfrontCancelledWorkflowHistory,
      ];
      const replayResult = await Worker.runReplayHistories(
        {
          workflowsPath: require.resolve('../../../helpers/temporal_workflows'),
        },
        histories
      );

      for await (const result of replayResult) {
        if (result.error) {
          logger.error(result.error);
          expect.fail(result.error.message);
        }
      }
    });
  });
});
