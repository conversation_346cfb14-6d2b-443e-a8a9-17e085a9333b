import { createSandbox, SinonSandbox } from 'sinon';
import { TestWorkflowEnvironment } from '@temporalio/testing';
import { ApplicationFailure } from '@temporalio/common';
import { randomUUID } from 'crypto';
import { Sync } from 'factory.ts';
import { createTestWorker } from '../../../helpers/temporal_test.helpers';
import { expect } from '../../../setup';
import {
  refundWorkflowDefinition,
  RefundWorkflowInput,
} from '../../../../src/ng/workflow/definitions/refund.workflow';
import { WorkflowActivities } from '../../../../src/ng/workflow/activities.factory';

// Fixture factory for RefundWorkflowInput
const refundWorkflowArgsFactory = Sync.makeFactory<RefundWorkflowInput>({
  transactionId: 'txn-123',
  refundAmountCents: 1000,
});

describe('RefundWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: SinonSandbox;

  /**
   * Starts a worker and returns the workflow handle.
   * @param activities - The activities to use for the worker.
   * @param args - The arguments to pass to the workflow.
   * @returns The workflow handle.
   */
  async function startWorker(
    activities: Partial<WorkflowActivities>,
    args: RefundWorkflowInput
  ) {
    const taskQueue = `test-queue-${randomUUID()}`;
    const worker = await createTestWorker({
      connection: testEnv.nativeConnection,
      taskQueue,
      activities,
    });
    const handle = await testEnv.client.workflow.start(
      refundWorkflowDefinition.workflow,
      {
        args: [args],
        taskQueue,
        workflowId: `workflow-id-${randomUUID()}`,
        retry: {
          maximumAttempts: 1,
          backoffCoefficient: 1,
          initialInterval: '100ms',
        },
      }
    );

    return { handle, worker };
  }

  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    sandbox = createSandbox();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  after(async () => {
    await testEnv.teardown();
  });

  describe('Successful refund', () => {
    it('should complete refund workflow successfully', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-456',
        refundAmountCents: 2500,
      });

      const refundTransactionStub = sandbox.stub().resolves({
        returned: {},
        details: {
          refundedToId: 'buyer-123',
          refundedFromId: 'seller-456',
          amountCents: 2500,
          refundedFromText: 'Test Supplier',
          refundedToText: 'Test Buyer',
          refundTxId: 'txn-456',
        },
      });

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: args.transactionId,
        refundAmountCents: args.refundAmountCents,
      });
      expect(result).to.deep.equal({
        returned: {},
        details: {
          refundedToId: 'buyer-123',
          refundedFromId: 'seller-456',
          amountCents: 2500,
          refundedFromText: 'Test Supplier',
          refundedToText: 'Test Buyer',
          refundTxId: 'txn-456',
        },
      });
    });
  });

  describe('Failed refund scenarios', () => {
    it('should fail workflow when refundTransaction throws error', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-789',
        refundAmountCents: 1500,
      });

      const refundTransactionStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Transaction not found',
            'TransactionNotFound'
          )
        );

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Transaction not found'
          );
        }
      });

      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: args.transactionId,
        refundAmountCents: args.refundAmountCents,
      });
    });

    it('should fail workflow when refund amount exceeds transaction amount', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-101',
        refundAmountCents: 10000, // Large amount that would exceed original
      });

      const refundTransactionStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Refund amount exceeds transaction amount',
            'InvalidRefundAmount'
          )
        );

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Refund amount exceeds transaction amount'
          );
        }
      });

      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: args.transactionId,
        refundAmountCents: args.refundAmountCents,
      });
    });

    it('should fail workflow when Zai gateway returns error', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-202',
        refundAmountCents: 750,
      });

      const refundTransactionStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Zai gateway error: Item not found',
            'ZaiGatewayError'
          )
        );

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Zai gateway error: Item not found'
          );
        }
      });

      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: args.transactionId,
        refundAmountCents: args.refundAmountCents,
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle zero refund amount', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-zero',
        refundAmountCents: 0,
      });

      const refundTransactionStub = sandbox.stub().resolves({
        returned: {},
        details: {
          refundedToId: 'buyer-123',
          refundedFromId: 'seller-456',
          amountCents: 0,
          refundedFromText: 'Test Supplier',
          refundedToText: 'Test Buyer',
          refundTxId: 'txn-zero',
        },
      });

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: args.transactionId,
        refundAmountCents: 0,
      });
      expect(result.details.amountCents).to.equal(0);
    });

    it('should handle valid refund with different transaction ID format', async () => {
      // ARRANGE
      const args = refundWorkflowArgsFactory.build({
        transactionId: 'txn-uuid-123-456',
        refundAmountCents: 3000,
      });

      const refundTransactionStub = sandbox.stub().resolves({
        returned: {},
        details: {
          refundedToId: 'buyer-uuid-123',
          refundedFromId: 'seller-uuid-456',
          amountCents: 3000,
          refundedFromText: 'UUID Supplier',
          refundedToText: 'UUID Buyer',
          refundTxId: 'txn-uuid-123-456',
        },
      });

      const { handle, worker } = await startWorker(
        {
          refundTransaction: refundTransactionStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(refundTransactionStub).to.have.been.calledOnceWith({
        transactionId: 'txn-uuid-123-456',
        refundAmountCents: 3000,
      });
      expect(result.details?.refundTxId).to.equal('txn-uuid-123-456');
    });
  });
});
