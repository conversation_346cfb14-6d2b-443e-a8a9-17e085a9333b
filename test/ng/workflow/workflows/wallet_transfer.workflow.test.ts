import { createSandbox, SinonSandbox } from 'sinon';
import { TestWorkflowEnvironment } from '@temporalio/testing';
import { ApplicationFailure } from '@temporalio/common';
import { randomUUID } from 'crypto';
import { createTestWorker } from '../../../helpers/temporal_test.helpers';
import { expect } from '../../../setup';
import { walletTransferWorkflowDefinition } from '../../../../src/ng/workflow/definitions/wallet_transfer.workflow';
import { WalletTransferParams } from '../../../../src/ng/workflow/types/wallet_transfer.types';
import { WorkflowActivities } from '../../../../src/ng/workflow/activities.factory';
import { states } from '../../../../src/models/transaction';
import { CurrencyCodes } from '../../../../src/ng/common/types/currency.types';
import { walletTransferArgsFactory } from '../../fixtures/wallet_transfer.factory';

describe('WalletTransferWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: SinonSandbox;

  /**
   * Starts a worker and returns the workflow handle.
   * @param activities - The activities to use for the worker.
   * @param args - The arguments to pass to the workflow.
   * @returns The workflow handle.
   */
  async function createWorker(
    activities: Partial<WorkflowActivities>,
    args: WalletTransferParams
  ) {
    const taskQueue = `test-queue-${randomUUID()}`;
    const worker = await createTestWorker({
      connection: testEnv.nativeConnection,
      taskQueue,
      activities,
    });
    const handle = await testEnv.client.workflow.start(
      walletTransferWorkflowDefinition.workflow,
      {
        args: [args],
        taskQueue,
        workflowId: `workflow-id-${randomUUID()}`,
        retry: {
          maximumAttempts: 1,
          backoffCoefficient: 1,
          initialInterval: '100ms',
        },
      }
    );

    return { handle, worker };
  }

  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    sandbox = createSandbox();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  after(async () => {
    await testEnv.teardown();
  });

  describe('Successful wallet transfer', () => {
    it('should complete wallet transfer workflow successfully', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        amountCents: 2500,
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {},
        details: {
          buyerId: 'buyer-456',
          sellerId: 'seller-789',
          totalCents: 2500,
          fundedByPrinciple: false,
          walletFundedCents: 0,
          walletFundTxId: undefined,
          feeCents: 0,
          sellerText: 'Test Supplier Store',
          buyerText: 'Test Buyer Account',
          purchaseTxId: undefined,
          purchaseTxState: states.completed,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(result).to.deep.equal({
        returned: {},
        details: {
          buyerId: 'buyer-456',
          sellerId: 'seller-789',
          totalCents: 2500,
          fundedByPrinciple: false,
          walletFundedCents: 0,
          feeCents: 0,
          sellerText: 'Test Supplier Store',
          buyerText: 'Test Buyer Account',
          purchaseTxState: states.completed,
        },
      });
    });

    it('should handle transfer with multiple order and invoice IDs', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        orderIds: ['order-1', 'order-2', 'order-3'],
        invoiceIds: ['invoice-1', 'invoice-2'],
        relatedTransactions: ['txn-related-1', 'txn-related-2'],
        amountCents: 7500,
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {
          totalCents: 7500,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(result.returned.totalCents).to.equal(7500);
    });
  });

  describe('Failed wallet transfer scenarios', () => {
    it('should fail workflow when sender not found', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        senderId: 'non-existent-sender',
        amountCents: 1000,
      });

      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable('Sender not found', 'UserNotFound')
        );

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq('Sender not found');
        }
      });

      expect(walletTransferStub).to.have.been.calledOnceWith(args);
    });

    it('should fail workflow when recipient not found', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        recipientId: 'non-existent-recipient',
        amountCents: 1500,
      });

      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Recipient not found',
            'RecipientNotFound'
          )
        );

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Recipient not found'
          );
        }
      });

      expect(walletTransferStub).to.have.been.calledOnceWith(args);
    });

    it('should fail workflow when insufficient wallet balance', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        amountCents: 100000, // Large amount that would exceed wallet balance
      });

      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Insufficient wallet balance',
            'InsufficientFunds'
          )
        );

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Insufficient wallet balance'
          );
        }
      });

      expect(walletTransferStub).to.have.been.calledOnceWith(args);
    });

    it('should fail workflow when Zai gateway error occurs', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        transactionId: 'txn-zai-error',
        amountCents: 3000,
      });

      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Failed to make wallet to wallet payment',
            'ZaiGatewayError'
          )
        );

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Failed to make wallet to wallet payment'
          );
        }
      });

      expect(walletTransferStub).to.have.been.calledOnceWith(args);
    });

    it('should fail workflow when transaction creation fails', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        transactionId: 'txn-duplicate-123',
        amountCents: 2000,
      });

      const walletTransferStub = sandbox
        .stub()
        .rejects(
          ApplicationFailure.nonRetryable(
            'Failed to create transaction',
            'TransactionCreationError'
          )
        );

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT & ASSERT
      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Expected workflow to throw error');
        } catch (error) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.failure?.cause?.message).eq(
            'Failed to create transaction'
          );
        }
      });

      expect(walletTransferStub).to.have.been.calledOnceWith(args);
    });
  });

  describe('Edge cases', () => {
    it('should handle transfer with minimal amount', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        amountCents: 1, // 1 cent
        description: 'Minimal transfer test',
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {
          totalCents: 1,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      const result = await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(result.returned.totalCents).to.equal(1);
    });

    it('should handle transfer with different currency code', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        currencyCode: CurrencyCodes.USD,
        amountCents: 5000,
        description: 'USD transfer test',
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {},
        details: {
          buyerId: args.senderId,
          sellerId: args.recipientId,
          totalCents: 5000,
          fundedByPrinciple: false,
          walletFundedCents: 0,
          walletFundTxId: undefined,
          feeCents: 0,
          sellerText: args.recipientText,
          buyerText: args.senderText,
          purchaseTxId: undefined,
          purchaseTxState: states.completed,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(args.currencyCode).to.equal(CurrencyCodes.USD);
    });

    it('should handle transfer with empty order and invoice arrays', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        orderIds: [],
        invoiceIds: [],
        relatedTransactions: [],
        description: 'Transfer with empty arrays',
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {},
        details: {
          buyerId: args.senderId,
          sellerId: args.recipientId,
          totalCents: args.amountCents,
          fundedByPrinciple: false,
          walletFundedCents: 0,
          walletFundTxId: undefined,
          feeCents: 0,
          sellerText: args.recipientText,
          buyerText: args.senderText,
          purchaseTxId: undefined,
          purchaseTxState: states.completed,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(args.orderIds).to.deep.equal([]);
      expect(args.invoiceIds).to.deep.equal([]);
      expect(args.relatedTransactions).to.deep.equal([]);
    });

    it('should handle transfer with different transaction types', async () => {
      // ARRANGE
      const args = walletTransferArgsFactory.build({
        transactionType: 'refund',
        description: 'Refund wallet transfer',
        amountCents: 1200,
      });

      const walletTransferStub = sandbox.stub().resolves({
        returned: {},
        details: {
          buyerId: args.senderId,
          sellerId: args.recipientId,
          totalCents: 1200,
          fundedByPrinciple: false,
          walletFundedCents: 0,
          walletFundTxId: undefined,
          feeCents: 0,
          sellerText: args.recipientText,
          buyerText: args.senderText,
          purchaseTxId: undefined,
          purchaseTxState: states.completed,
        },
      });

      const { handle, worker } = await createWorker(
        {
          walletTransfer: walletTransferStub,
        },
        args
      );

      // ACT
      await worker.runUntil(async () => handle.result());

      // ASSERT
      expect(walletTransferStub).to.have.been.calledOnceWith(args);
      expect(args.transactionType).to.equal('refund');
    });
  });
});
