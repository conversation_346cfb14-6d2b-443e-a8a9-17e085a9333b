import { TestWorkflowEnvironment } from '@temporalio/testing';
import { createSandbox, SinonSandbox } from 'sinon';
import { randomUUID } from 'crypto';
import {
  makeZaiPaymentWorkflowDefinition,
  MakeZaiPaymentWorkflowInput,
} from '../../../../src/ng/workflow/definitions/make_zai_payment.workflow';
import { expect } from '../../../setup';
import { WorkflowActivities } from '../../../../src/ng/workflow/activities.factory';
import { createTestWorker } from '../../../helpers/temporal_test.helpers';

describe('MakeZaiPaymentWorkflow', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: SinonSandbox;
  let request: MakeZaiPaymentWorkflowInput;

  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    sandbox = createSandbox();
  });

  /**
   * Starts a worker and returns the workflow handle.
   * @param activities - The activities to use for the worker.
   * @param args - The arguments to pass to the workflow.
   * @returns The workflow handle.
   */
  async function createWorker(
    activities: Partial<WorkflowActivities>,
    args: MakeZaiPaymentWorkflowInput
  ) {
    const taskQueue = `test-queue-${randomUUID()}`;
    const worker = await createTestWorker({
      connection: testEnv.nativeConnection,
      taskQueue,
      activities,
    });
    const handle = await testEnv.client.workflow.start(
      makeZaiPaymentWorkflowDefinition.workflow,
      {
        args: [args],
        taskQueue,
        workflowId: `workflow-id-${randomUUID()}`,
        retry: {
          maximumAttempts: 1,
          backoffCoefficient: 1,
          initialInterval: '100ms',
        },
      }
    );

    return { handle, worker };
  }

  beforeEach(async () => {
    request = {
      transactionId: randomUUID(),
      body: {
        account_id: 'test-account-123',
        merchant_phone: '+***********',
      },
      releasePolicy: 'immediate',
    };
  });

  afterEach(async () => {
    sandbox.restore();
  });

  after(async () => {
    await testEnv.teardown();
  });

  describe('workflow identifier', () => {
    it('should format workflow ID correctly with valid inputs', () => {
      const identifier = '123e4567-e89b-12d3-a456-************';

      const result =
        makeZaiPaymentWorkflowDefinition.generateWorkflowId(identifier);

      expect(result).to.equal(`make-zai-payment-${identifier}`);
    });

    it('should work with different identifiers', () => {
      const testCases = [
        {
          identifier: 'transaction1',
          expected: 'make-zai-payment-transaction1',
        },
        {
          identifier: '123',
          expected: 'make-zai-payment-123',
        },
        {
          identifier: 'test-transaction',
          expected: 'make-zai-payment-test-transaction',
        },
      ];

      testCases.forEach(({ identifier, expected }) => {
        const result =
          makeZaiPaymentWorkflowDefinition.generateWorkflowId(identifier);
        expect(result).to.equal(expected);
      });
    });
  });

  describe('workflow validation', () => {
    it('should throw non-retryable error when account_id is empty string', async () => {
      const invalidRequest = {
        ...request,
        body: {
          account_id: '',
          merchant_phone: '+***********',
        },
      };

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: sandbox.stub().resolves(),
          finalizeTransaction: sandbox.stub().resolves(),
        },
        invalidRequest
      );

      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.message).to.include('Missing required fields');
        }
      });
    });

    it('should throw non-retryable error when merchant_phone is empty string', async () => {
      const invalidRequest = {
        ...request,
        body: {
          account_id: 'test-account-123',
          merchant_phone: '',
        },
      };

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: sandbox.stub().resolves(),
          finalizeTransaction: sandbox.stub().resolves(),
        },
        invalidRequest
      );

      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.message).to.include('Missing required fields');
        }
      });
    });

    it('should throw non-retryable error when account_id is whitespace only', async () => {
      const invalidRequest = {
        ...request,
        body: {
          account_id: '   ',
          merchant_phone: '+***********',
        },
      };

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: sandbox.stub().resolves(),
          finalizeTransaction: sandbox.stub().resolves(),
        },
        invalidRequest
      );

      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.message).to.include('Missing required fields');
        }
      });
    });

    it('should throw non-retryable error when merchant_phone is whitespace only', async () => {
      const invalidRequest = {
        ...request,
        body: {
          account_id: 'test-account-123',
          merchant_phone: '   ',
        },
      };

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: sandbox.stub().resolves(),
          finalizeTransaction: sandbox.stub().resolves(),
        },
        invalidRequest
      );

      await worker.runUntil(async () => {
        try {
          await handle.result();
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error.message).to.include('Workflow execution failed');
          expect(error.cause?.message).to.include('Missing required fields');
        }
      });
    });
  });

  describe('successful payment workflow', () => {
    it('should complete workflow successfully with immediate release policy', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: true,
        reason: 'Payment processed successfully',
        action: 'payment_completed',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result).to.deep.equal({
        success: true,
        message: 'Payment completed successfully',
        reason: 'Payment processed successfully',
        action: 'payment_completed',
        transactionId: request.transactionId,
      });

      expect(makePaymentStub).to.be.calledOnceWith(
        request.transactionId,
        request.body.account_id,
        request.body.merchant_phone
      );

      expect(finalizeTransactionStub).to.be.calledOnceWith(
        request.transactionId,
        {
          success: true,
          reason: 'Payment processed successfully',
          action: 'payment_completed',
        },
        'immediate'
      );
    });

    it('should complete workflow successfully with delayed release policy', async () => {
      const delayedRequest = {
        ...request,
        releasePolicy: 'delayed' as const,
      };

      const makePaymentStub = sandbox.stub().resolves({
        success: true,
        reason: 'Payment processed successfully',
        action: 'payment_completed',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        delayedRequest
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result.success).to.be.true;
      expect(finalizeTransactionStub).to.have.been.calledWith(
        request.transactionId,
        sandbox.match.any,
        'delayed'
      );
    });

    it('should verify finalizeTransaction is called with correct parameters for immediate policy', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: true,
        reason: 'Payment processed successfully',
        action: 'payment_completed',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      await worker.runUntil(async () => handle.result());

      expect(finalizeTransactionStub).to.have.been.calledOnceWith(
        request.transactionId,
        {
          success: true,
          reason: 'Payment processed successfully',
          action: 'payment_completed',
        },
        'immediate'
      );
    });
  });

  describe('failed payment scenarios', () => {
    it('should handle payment failure correctly', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: false,
        reason: 'Insufficient funds',
        action: 'payment_failed',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result).to.deep.equal({
        success: false,
        message: 'Payment failed: Insufficient funds',
        reason: 'Insufficient funds',
        action: 'payment_failed',
        transactionId: request.transactionId,
      });

      expect(finalizeTransactionStub).to.have.been.calledOnceWith(
        request.transactionId,
        {
          success: false,
          reason: 'Insufficient funds',
          action: 'payment_failed',
        },
        'immediate'
      );
    });

    it('should handle sync_state action correctly', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: false,
        reason: 'Transaction state mismatch',
        action: 'sync_state',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result).to.deep.equal({
        success: false,
        message: 'Transaction state was out of sync and has been corrected',
        reason: 'Transaction state mismatch',
        action: 'sync_state',
        transactionId: request.transactionId,
      });

      expect(finalizeTransactionStub).to.have.been.calledOnceWith(
        request.transactionId,
        {
          success: false,
          reason: 'Transaction state mismatch',
          action: 'sync_state',
        },
        'immediate'
      );
    });

    it('should handle no_action_needed correctly', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: false,
        reason: 'Transaction already completed',
        action: 'no_action_needed',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result).to.deep.equal({
        success: false,
        message: 'No payment action required: Transaction already completed',
        reason: 'Transaction already completed',
        action: 'no_action_needed',
        transactionId: request.transactionId,
      });

      expect(finalizeTransactionStub).to.have.been.calledOnceWith(
        request.transactionId,
        {
          success: false,
          reason: 'Transaction already completed',
          action: 'no_action_needed',
        },
        'immediate'
      );
    });

    it('should handle unknown action with default message', async () => {
      const makePaymentStub = sandbox.stub().resolves({
        success: false,
        reason: 'Unknown error occurred',
        action: 'unknown_action',
      });

      const finalizeTransactionStub = sandbox.stub().resolves();

      const { worker, handle } = await createWorker(
        {
          makePaymentOnTransaction: makePaymentStub,
          finalizeTransaction: finalizeTransactionStub,
        },
        request
      );

      const result = await worker.runUntil(async () => handle.result());

      expect(result).to.deep.equal({
        success: false,
        message: 'Payment processing completed: Unknown error occurred',
        reason: 'Unknown error occurred',
        action: 'unknown_action',
        transactionId: request.transactionId,
      });

      expect(finalizeTransactionStub).to.have.been.calledOnceWith(
        request.transactionId,
        {
          success: false,
          reason: 'Unknown error occurred',
          action: 'unknown_action',
        },
        'immediate'
      );
    });
  });
});
