import { expect } from 'chai';
import {
  createHoldingPaymentArgs,
  createTopUpPaymentArgs,
} from '../../../../src/ng/workflow/factories/upfront.factory';
import { UpfrontPayment } from '../../../../src/ng/common/requests/upfront_payments.request';
import { UpfrontPaymentState } from '../../../../src/ng/workflow/types/upfront.types';
import { UpfrontFlowStep } from '../../../../src/types/upfront.types';

describe('Upfront Factory', () => {
  let mockUpfrontPayment: UpfrontPayment;
  let mockUpfrontState: UpfrontPaymentState;

  beforeEach(() => {
    mockUpfrontPayment = {
      amountCents: 15000,
      description: 'Test upfront payment',
      sellerUserId: 'seller-123',
      buyerUserId: 'buyer-456',
      orderId: 'order-789',
      invoiceId: 'invoice-101',
      userId: 'user-123',
      paymentMethodId: 'pm-456',
    };

    mockUpfrontState = {
      holdingTransactionId: 'holding-txn-123',
      topUpTransactionId: 'topup-txn-456',
      holdingAmountCents: 15000,
      flowStep: UpfrontFlowStep.WaitingForHoldingPayment,
    };
  });

  describe('createHoldingPaymentArgs', () => {
    it('should create PaymentMethodTransferParams for holding payment', () => {
      // ACT
      const actualResult = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );

      // ASSERT
      expect(actualResult.amountCents).to.equal(15000);
      expect(actualResult.buyerId).to.equal('buyer-456');
      expect(actualResult.sellerId).to.equal('seller-123');
      expect(actualResult.paymentMethodId).to.equal('pm-456');
      expect(actualResult.transactionId).to.equal('holding-txn-123');
      expect(actualResult.relatedTransactions).to.deep.equal([]);
    });

    it('should use the description for all text fields', () => {
      // ACT
      const actualResult = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );

      // ASSERT
      expect(actualResult.description).to.equal('Test upfront payment');
      expect(actualResult.buyerText).to.equal('Test upfront payment');
      expect(actualResult.sellerText).to.equal('Test upfront payment');
    });

    it('should preserve order and invoice IDs as arrays', () => {
      // ACT
      const actualResult = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });

    it('should include context with original payment amount', () => {
      // ACT
      const actualResult = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );

      // ASSERT
      expect(actualResult.context).to.deep.equal({
        originalPayInAmountCents: 15000,
      });
    });

    it('should handle different amount values correctly', () => {
      // ARRANGE
      const customUpfront = { ...mockUpfrontPayment, amountCents: 25000 };

      // ACT
      const actualResult = createHoldingPaymentArgs(
        customUpfront,
        mockUpfrontState
      );

      // ASSERT
      expect(actualResult.amountCents).to.equal(25000);
      expect(actualResult.context?.originalPayInAmountCents).to.equal(25000);
    });
  });

  describe('createTopUpPaymentArgs', () => {
    const topupAmountCents = 5000;
    const topupDescription = 'Additional payment for order completion';

    it('should create PaymentMethodTransferParams for top-up payment', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        topupAmountCents,
        topupDescription
      );

      // ASSERT
      expect(actualResult.amountCents).to.equal(5000);
      expect(actualResult.buyerId).to.equal('buyer-456');
      expect(actualResult.sellerId).to.equal('seller-123');
      expect(actualResult.paymentMethodId).to.equal('pm-456');
      expect(actualResult.transactionId).to.equal('topup-txn-456');
      expect(actualResult.relatedTransactions).to.deep.equal([
        'holding-txn-123',
      ]);
    });

    it('should use the provided description for buyer and seller text', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        topupAmountCents,
        topupDescription
      );

      // ASSERT
      expect(actualResult.description).to.equal('Test upfront payment');
      expect(actualResult.buyerText).to.equal(
        'Additional payment for order completion'
      );
      expect(actualResult.sellerText).to.equal(
        'Additional payment for order completion'
      );
    });

    it('should preserve order and invoice IDs as arrays', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        topupAmountCents,
        topupDescription
      );

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });

    it('should include comprehensive context for top-up payment', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        topupAmountCents,
        topupDescription
      );

      // ASSERT
      expect(actualResult.context).to.deep.equal({
        originalPayInTransactionId: 'holding-txn-123',
        originalPayInAmountCents: 15000,
        topUpAmountCents: 5000,
      });
    });

    it('should link top-up payment to holding transaction', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        topupAmountCents,
        topupDescription
      );

      // ASSERT
      expect(actualResult.relatedTransactions).to.contain('holding-txn-123');
      expect(actualResult.context?.originalPayInTransactionId).to.equal(
        'holding-txn-123'
      );
    });

    it('should handle zero top-up amount correctly', () => {
      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        0,
        'No additional payment required'
      );

      // ASSERT
      expect(actualResult.amountCents).to.equal(0);
      expect(actualResult.context?.topUpAmountCents).to.equal(0);
    });

    it('should handle large top-up amounts correctly', () => {
      // ARRANGE
      const largeTopUpAmount = 50000;

      // ACT
      const actualResult = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        largeTopUpAmount,
        'Large additional payment'
      );

      // ASSERT
      expect(actualResult.amountCents).to.equal(50000);
      expect(actualResult.context?.topUpAmountCents).to.equal(50000);
    });
  });

  describe('Factory method consistency', () => {
    it('should ensure both methods use same buyer and seller IDs', () => {
      // ACT
      const holdingArgs = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );
      const topUpArgs = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        5000,
        'Top up payment'
      );

      // ASSERT
      expect(holdingArgs.buyerId).to.equal(topUpArgs.buyerId);
      expect(holdingArgs.sellerId).to.equal(topUpArgs.sellerId);
      expect(holdingArgs.paymentMethodId).to.equal(topUpArgs.paymentMethodId);
    });

    it('should ensure both methods use same order and invoice IDs', () => {
      // ACT
      const holdingArgs = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );
      const topUpArgs = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        5000,
        'Top up payment'
      );

      // ASSERT
      expect(holdingArgs.orderIds).to.deep.equal(topUpArgs.orderIds);
      expect(holdingArgs.invoiceIds).to.deep.equal(topUpArgs.invoiceIds);
    });

    it('should ensure top-up payment references holding transaction', () => {
      // ACT
      const holdingArgs = createHoldingPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState
      );
      const topUpArgs = createTopUpPaymentArgs(
        mockUpfrontPayment,
        mockUpfrontState,
        5000,
        'Top up payment'
      );

      // ASSERT
      expect(topUpArgs.relatedTransactions).to.contain(
        holdingArgs.transactionId
      );
      expect(topUpArgs.context?.originalPayInTransactionId).to.equal(
        holdingArgs.transactionId
      );
    });
  });
});
