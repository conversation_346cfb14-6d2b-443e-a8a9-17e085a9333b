import { expect } from 'chai';
import {
  getPromoWalletFundingArgs,
  getPromoWalletReversalArgs,
  getPromoTakePaymentArgs,
  getPromoSucceededArgs,
} from '../../../../src/ng/workflow/factories/promotional_payment.factory';
import { PromotionalPaymentWorkflowArgs } from '../../../../src/ng/workflow/types/promotional_payment.types';

describe('Promotional Payment Factory', () => {
  let mockWorkflowArgs: PromotionalPaymentWorkflowArgs;

  beforeEach(() => {
    mockWorkflowArgs = {
      promoAmountCents: 5000,
      purchaseAmountCents: 10000,
      senderId: 'sender-123',
      senderPaymentMethodId: 'pm-123',
      recipientId: 'recipient-456',
      orderIds: ['order-789'],
      invoiceIds: ['invoice-101'],
      description: '#INV-TEST123',
      purchaseTransactionId: 'purchase-txn-123',
      promoTransactionId: 'promo-txn-456',
      fundingWalletId: 'funding-wallet-123',
    };
  });

  describe('getPromoWalletFundingArgs', () => {
    it('should create WalletTransferParams with capture transaction type', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.transactionType).to.equal('capture');
      expect(actualResult.description).to.equal('#INV-TEST123');
      expect(actualResult.amountCents).to.equal(5000);
      expect(actualResult.senderId).to.equal('funding-wallet-123');
      expect(actualResult.recipientId).to.equal('recipient-456');
      expect(actualResult.transactionId).to.equal('promo-txn-456');
      expect(actualResult.relatedTransactions).to.deep.equal([
        'purchase-txn-123',
      ]);
    });

    it('should use the correct text fields for wallet transfer', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.senderText).to.equal('#INV-TEST123');
      expect(actualResult.recipientText).to.equal('#INV-TEST123');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('getPromoWalletReversalArgs', () => {
    it('should create WalletTransferParams with capture transaction type', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.transactionType).to.equal('capture');
      expect(actualResult.description).to.equal('#INV-TEST123');
      expect(actualResult.amountCents).to.equal(5000);
      expect(actualResult.senderId).to.equal('recipient-456');
      expect(actualResult.recipientId).to.equal('funding-wallet-123');
      expect(actualResult.transactionId).to.equal('purchase-txn-123');
      expect(actualResult.relatedTransactions).to.deep.equal(['promo-txn-456']);
    });

    it('should reverse the sender and recipient from funding operation', () => {
      // ARRANGE
      const fundingResult = getPromoWalletFundingArgs(mockWorkflowArgs);

      // ACT
      const reversalResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(reversalResult.senderId).to.equal(fundingResult.recipientId);
      expect(reversalResult.recipientId).to.equal(fundingResult.senderId);
      expect(reversalResult.transactionType).to.equal('capture');
    });

    it('should use the correct text fields for wallet reversal', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.senderText).to.equal('#INV-TEST123');
      expect(actualResult.recipientText).to.equal('#INV-TEST123');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('getPromoTakePaymentArgs', () => {
    it('should create PaymentMethodTransferParams for the remaining amount', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.amountCents).to.equal(5000); // 10000 - 5000
      expect(actualResult.buyerId).to.equal('sender-123');
      expect(actualResult.sellerId).to.equal('recipient-456');
      expect(actualResult.paymentMethodId).to.equal('pm-123');
      expect(actualResult.transactionId).to.equal('purchase-txn-123');
      expect(actualResult.relatedTransactions).to.deep.equal(['promo-txn-456']);
    });

    it('should use the provided description for all text fields', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.description).to.equal('#INV-TEST123');
      expect(actualResult.buyerText).to.equal('#INV-TEST123');
      expect(actualResult.sellerText).to.equal('#INV-TEST123');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoTakePaymentArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });
  });

  describe('getPromoSucceededArgs', () => {
    it('should calculate purchase disbursement as purchase amount minus promo amount', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.purchaseDisbursementCents).to.equal(5000); // 10000 - 5000
    });

    it('should set promo disbursement to the promo amount', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.promoDisbursementCents).to.equal(5000);
    });

    it('should map transaction IDs correctly', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.purchaseTransactionId).to.equal('purchase-txn-123');
      expect(actualResult.promoTransactionId).to.equal('promo-txn-456');
    });

    it('should set disbursement recipient to the recipient ID', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.disbursementRecipientId).to.equal('recipient-456');
    });

    it('should preserve order and invoice IDs', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal(['order-789']);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-101']);
    });

    it('should handle edge case where promo amount equals purchase amount', () => {
      // ARRANGE
      const edgeCaseArgs = {
        ...mockWorkflowArgs,
        promoAmountCents: 10000,
        purchaseAmountCents: 10000,
      };

      // ACT
      const actualResult = getPromoSucceededArgs(edgeCaseArgs);

      // ASSERT
      expect(actualResult.purchaseDisbursementCents).to.equal(0);
      expect(actualResult.promoDisbursementCents).to.equal(10000);
    });

    it('should handle multiple order and invoice IDs', () => {
      // ARRANGE
      const multipleIdsArgs = {
        ...mockWorkflowArgs,
        orderIds: ['order-1', 'order-2', 'order-3'],
        invoiceIds: ['invoice-1', 'invoice-2'],
      };

      // ACT
      const actualResult = getPromoSucceededArgs(multipleIdsArgs);

      // ASSERT
      expect(actualResult.orderIds).to.deep.equal([
        'order-1',
        'order-2',
        'order-3',
      ]);
      expect(actualResult.invoiceIds).to.deep.equal(['invoice-1', 'invoice-2']);
    });

    it('should handle minimum promo amount (1 cent)', () => {
      // ARRANGE
      const minPromoArgs = {
        ...mockWorkflowArgs,
        promoAmountCents: 1,
        purchaseAmountCents: 10000,
      };

      // ACT
      const actualResult = getPromoSucceededArgs(minPromoArgs);

      // ASSERT
      expect(actualResult.purchaseDisbursementCents).to.equal(9999);
      expect(actualResult.promoDisbursementCents).to.equal(1);
    });

    it('should create correct object structure with all required properties', () => {
      // ACT
      const actualResult = getPromoSucceededArgs(mockWorkflowArgs);

      // ASSERT
      expect(actualResult).to.have.all.keys([
        'purchaseDisbursementCents',
        'purchaseTransactionId',
        'promoDisbursementCents',
        'promoTransactionId',
        'disbursementRecipientId',
        'orderIds',
        'invoiceIds',
      ]);
    });
  });

  describe('Transaction type consistency', () => {
    it('should ensure all wallet transfer operations use capture transaction type', () => {
      // ACT
      const fundingArgs = getPromoWalletFundingArgs(mockWorkflowArgs);
      const reversalArgs = getPromoWalletReversalArgs(mockWorkflowArgs);

      // ASSERT
      expect(fundingArgs.transactionType).to.equal('capture');
      expect(reversalArgs.transactionType).to.equal('capture');
    });
  });
});
