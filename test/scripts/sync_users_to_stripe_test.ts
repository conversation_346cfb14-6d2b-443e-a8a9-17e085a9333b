import nock, { cleanAll as clearNockScopes } from 'nock';
import sinon from 'sinon';
import moment from 'moment';
import { randomUUID } from 'crypto';
import { expect } from 'chai';
import { runner } from '../../src/scripts/sync_users_to_stripe';
import { create as createUser } from '../fixtures/user';
import { User } from '../../src/models';

describe('Sync users to stripe', () => {
  let sandbox: sinon.SinonSandbox;
  let retrieveCustomerScope: nock.Scope;
  let updatePaymentMethodScope: nock.Scope;
  let updateCustomerScope: nock.Scope;
  let stripeCustomerId: string;
  let paymentMethodId: string;
  let ordermentumId: string;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    ordermentumId = randomUUID();
    stripeCustomerId = `cus_${randomUUID()}`;

    paymentMethodId = `pm_${randomUUID()}`;
    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        email: '<EMAIL>',
        name: '<PERSON>',
        description: 'Test Customer',
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

    updatePaymentMethodScope = nock('https://api.stripe.com')
      .post(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        allow_redisplay: 'always',
      });

    updateCustomerScope = nock('https://api.stripe.com')
      .post(`/v1/customers/${stripeCustomerId}`)
      .reply(200);

    nock('https://api.stripe.com')
      .get(
        `/v1/customers/search?query=metadata[%27ordermentumId%27]%3A%27${ordermentumId}%27`
      )
      .reply(200, {
        total_count: 0,
        data: [],
      });
  });

  afterEach(() => {
    clearNockScopes();
    sandbox.restore();
  });

  it('should not process if no users found in db', async () => {
    const retailerIds = [randomUUID()];
    await runner({ entityIds: retailerIds });
    expect(retrieveCustomerScope.isDone()).to.be.false;
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.be.false;
  });

  it('should not process if no active users in DB', async () => {
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
      status: 'archived',
    });
    const retailerIds = [user.ordermentum_id];
    await runner({ entityIds: retailerIds });
    expect(retrieveCustomerScope.isDone()).to.be.false;
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.be.false;
  });

  it('should not re-process already processed user', async () => {
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: {
        supplier: false,
        stripeSyncedAt: new Date().toISOString(),
      },
    });
    const retailerIds = [user.ordermentum_id];
    await runner({ entityIds: retailerIds });
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.be.false;
  });

  it('should fetch the latest payment method if customer has no default payment method and they have multiple payment methods', async () => {
    // clean all scopes
    clearNockScopes();
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
    });

    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        invoice_settings: {
          default_payment_method: null,
        },
      });

    const latestPaymentMethodId = `pm_${randomUUID()}`;
    const getCustomerPaymentMethodsScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}/payment_methods`)
      .query({ type: 'card' })
      .reply(200, {
        object: 'list',
        data: [
          {
            id: 'pm_1',
            created: 1681000000,
          },
          {
            id: latestPaymentMethodId,
            created: 1682000000,
          },
        ],
      });

    const retrieveExistingPaymentMethod = nock('https://api.stripe.com')
      .get(`/v1/payment_methods/${latestPaymentMethodId}`)
      .reply(200, {
        id: latestPaymentMethodId,
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        billing_details: {
          address: {
            country: 'AU',
          },
          name: 'sample_method',
        },
      });

    updatePaymentMethodScope = nock('https://api.stripe.com')
      .post(`/v1/payment_methods/${latestPaymentMethodId}`)
      .reply(200, {
        id: latestPaymentMethodId,
        allow_redisplay: 'always',
      });

    updateCustomerScope = nock('https://api.stripe.com')
      .post(`/v1/customers/${stripeCustomerId}`)
      .reply(200);

    const retailerIds = [user.ordermentum_id];
    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });

    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(getCustomerPaymentMethodsScope.isDone()).to.be.true;
    expect(retrieveExistingPaymentMethod.isDone()).to.be.true;

    // should update calls to stripe and since it was done
    // we know that the expected latest Id was used
    expect(updatePaymentMethodScope.isDone()).to.be.true;
    expect(updateCustomerScope.isDone()).to.be.true;
  });

  it('should not update payment method if customer does not have a default card or no payment methods listed either', async () => {
    // clean all scopes
    clearNockScopes();
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
    });

    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        invoice_settings: {
          default_payment_method: null,
        },
      });

    const getCustomerPaymentMethodsScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}/payment_methods`)
      .query({ type: 'card' })
      .reply(200, {
        object: 'list',
        data: [],
      });

    updatePaymentMethodScope = nock('https://api.stripe.com')
      .post(`/v1/payment_methods/${randomUUID()}`)
      .reply(200, {
        id: randomUUID(),
        allow_redisplay: 'always',
      });

    updateCustomerScope = nock('https://api.stripe.com')
      .post(`/v1/customers/${stripeCustomerId}`)
      .reply(200);

    const retailerIds = [user.ordermentum_id];
    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });

    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(getCustomerPaymentMethodsScope.isDone()).to.be.true;

    // should not call the update calls to stripe
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.be.false;
  });

  it('should update payment method in stripe if customer exist in stripe and payment methods found', async () => {
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
    });

    const retrieveExistingPaymentMethod = nock('https://api.stripe.com')
      .get(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        billing_details: {
          address: {
            country: 'AU',
          },
          name: 'sample_method',
        },
      });

    const retailerIds = [user.ordermentum_id];
    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });
    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(updatePaymentMethodScope.isDone()).to.be.true;
    expect(updateCustomerScope.isDone()).to.be.true;
    expect(retrieveExistingPaymentMethod.isDone()).to.be.true;
  });

  it('should create a customer if user stripe_customer_id property is null and no customer found in stripe', async () => {
    const user = await createUser({
      stripe_customer_id: null,
      configuration: { supplier: false },
      ordermentum_id: ordermentumId,
    });

    const searchCustomerScope = nock('https://api.stripe.com')
      .get(
        `/v1/customers/search?query=metadata[%27ordermentumId%27]%3A%27${ordermentumId}%27&limit=1`
      )
      .reply(200, {
        total_count: 0,
        data: [],
      });

    const stripeId = `cus_${randomUUID()}`;
    const createCustomerScope = nock('https://api.stripe.com')
      .post('/v1/customers')
      .reply(200, {
        id: stripeId,
        metadata: {
          ordermentumId: user.ordermentum_id,
          paymentUserId: user.id,
        },
      });

    const retailerIds = [user.ordermentum_id];
    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });

    expect(createCustomerScope.isDone()).to.be.true;
    expect(searchCustomerScope.isDone()).to.be.true;
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.false;

    await user.reload();
    expect(user.stripe_customer_id).to.equal(stripeId);
  });

  it('should create customer in stripe if customer has stripe_customer_id but customer is deleted in stripe', async () => {
    clearNockScopes();

    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
      ordermentum_id: ordermentumId,
    });

    nock('https://api.stripe.com')
      .get(
        `/v1/customers/search?query=metadata[%27ordermentumId%27]%3A%27${ordermentumId}%27`
      )
      .reply(200, {
        total_count: 0,
        data: [],
      });

    const retailerIds = [user.ordermentum_id];

    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        object: 'customer',
        deleted: true,
      });

    const stripeId = `cus_${randomUUID()}`;
    const createCustomerScope = nock('https://api.stripe.com')
      .post('/v1/customers')
      .reply(200, {
        id: stripeId,
        metadata: {
          ordermentumId: user.ordermentum_id,
          paymentUserId: user.id,
        },
      });

    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });
    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(createCustomerScope.isDone()).to.be.true;

    await user.reload();
    expect(user.stripe_customer_id).to.equal(stripeId);
  });

  it('should not process users with mobile numbers longer than 20 characters', async () => {
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
      mobile_number: '+613 ************** 9012 3456', // 25 characters
    });
    const retailerIds = [user.ordermentum_id];
    await runner({ entityIds: retailerIds, entityType: 'retailer' });
    expect(retrieveCustomerScope.isDone()).to.be.false;
    expect(updatePaymentMethodScope.isDone()).to.be.false;
    expect(updateCustomerScope.isDone()).to.be.false;
  });

  it('should process users with mobile numbers less than or equal to 20 characters', async () => {
    clearNockScopes();
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
      mobile_number: '+613 **************', // 19 characters
    });

    // Re-setup the scopes after clearNockScopes
    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

    // Add payment method retrieval mock
    const retrievePaymentMethodScope = nock('https://api.stripe.com')
      .get(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        billing_details: {
          address: {
            country: 'AU',
          },
          name: 'John Doe',
        },
      });

    updatePaymentMethodScope = nock('https://api.stripe.com')
      .post(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        allow_redisplay: 'always',
      });

    updateCustomerScope = nock('https://api.stripe.com')
      .post(`/v1/customers/${stripeCustomerId}`)
      .reply(200);

    const retailerIds = [user.ordermentum_id];
    await runner({ entityIds: retailerIds, entityType: 'retailer' });
    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(retrievePaymentMethodScope.isDone()).to.be.true;
    expect(updatePaymentMethodScope.isDone()).to.be.true;
    expect(updateCustomerScope.isDone()).to.be.true;
  });

  [
    {
      description: 'Contains alphabets',
      number: '+6183472064 REBECCA & Depac',
    },
    {
      description: 'Multiple numbers',
      number: '+611300 881 763  0408 768 545',
    },
    { description: 'Contains email', number: '+<EMAIL>' },
    {
      description: 'Exceeds 20 chars',
      number: '+613 ************** 9012 3456',
    },
    { description: 'Too long', number: '+61 4 123 456 789 012' },
  ].forEach(({ description, number }) => {
    it(`should not process users with invalid mobile number: ${description}`, async () => {
      const user = await createUser({
        stripe_customer_id: stripeCustomerId,
        configuration: { supplier: false },
        mobile_number: number,
      });
      const retailerIds = [user.ordermentum_id];
      await runner({ entityIds: retailerIds, entityType: 'retailer' });
      expect(retrieveCustomerScope.isDone()).to.be.false;
      expect(updatePaymentMethodScope.isDone()).to.be.false;
      expect(updateCustomerScope.isDone()).to.be.false;
    });
  });

  [
    { description: 'Mobile no spaces', number: '+61412345678' },
    { description: 'Mobile with spaces', number: '+61 4 1234 5678' },
    { description: 'Mobile starting with 0', number: '0412345678' },
    {
      description: 'Mobile starting with 0 and spaces',
      number: '04 1234 5678',
    },
    { description: 'Landline NSW/ACT', number: '+61 2 9569 1013' },
    { description: 'Landline VIC/TAS', number: '+61 3 9889 1234' },
    { description: 'Landline QLD', number: '+61 7 1234 5678' },
    { description: 'Landline SA/NT/WA', number: '+61 8 1234 5678' },
    { description: 'Landline with 0 prefix', number: '02 9569 1013' },
    {
      description: 'Mobile with parentheses and hyphens',
      number: '+61(043)187-0690',
    },
  ].forEach(({ description, number }) => {
    it(`should process users with valid phone number: ${description}`, async () => {
      clearNockScopes();

      // Re-setup the scopes for each iteration
      retrieveCustomerScope = nock('https://api.stripe.com')
        .get(`/v1/customers/${stripeCustomerId}`)
        .reply(200, {
          id: stripeCustomerId,
          object: 'customer',
          email: '<EMAIL>',
          name: 'John Doe',
          description: 'Test Customer',
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });

      const retrievePaymentMethodScope = nock('https://api.stripe.com')
        .get(`/v1/payment_methods/${paymentMethodId}`)
        .reply(200, {
          id: paymentMethodId,
          object: 'payment_method',
          type: 'card',
          billing_details: {
            address: {
              country: 'AU',
            },
            name: 'John Doe',
          },
        });

      updatePaymentMethodScope = nock('https://api.stripe.com')
        .post(`/v1/payment_methods/${paymentMethodId}`)
        .reply(200, {
          id: paymentMethodId,
          object: 'payment_method',
          type: 'card',
          allow_redisplay: 'always',
          billing_details: {
            address: {
              country: 'AU',
            },
            name: 'John Doe',
          },
        });

      updateCustomerScope = nock('https://api.stripe.com')
        .post(`/v1/customers/${stripeCustomerId}`)
        .reply(200, {
          id: stripeCustomerId,
          object: 'customer',
          metadata: {
            updatedAt: moment().format('dddd, DD, MMM YYYY h:mm a'),
            updatedBy: 'payments_user_sync_script',
          },
        });

      const user = await createUser({
        stripe_customer_id: stripeCustomerId,
        configuration: { supplier: false },
        mobile_number: number,
      });
      const retailerIds = [user.ordermentum_id];
      await runner({ entityIds: retailerIds, entityType: 'retailer' });
      expect(retrieveCustomerScope.isDone()).to.be.true;
      expect(retrievePaymentMethodScope.isDone()).to.be.true;
      expect(updatePaymentMethodScope.isDone()).to.be.true;
      expect(updateCustomerScope.isDone()).to.be.true;
    });
  });

  it('should handle Stripe 429 rate limit errors with retries', async () => {
    // clean all scopes
    clearNockScopes();
    const user = await createUser({
      stripe_customer_id: stripeCustomerId,
      configuration: { supplier: false },
    });

    // First attempt returns 429 with Retry-After header
    retrieveCustomerScope = nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(
        429,
        {
          error: {
            type: 'rate_limit_error',
            message: 'Too many requests',
            code: 'rate_limit_exceeded',
          },
        },
        {
          'retry-after': '2',
        }
      );

    // Second attempt succeeds
    nock('https://api.stripe.com')
      .get(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        object: 'customer',
        email: '<EMAIL>',
        name: 'John Doe',
        description: 'Test Customer',
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
        metadata: {
          ordermentumId: user.ordermentum_id,
          paymentUserId: user.id,
        },
      });

    // Payment method retrieval also succeeds
    nock('https://api.stripe.com')
      .get(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        object: 'payment_method',
        billing_details: {
          address: {
            country: 'AU',
          },
          name: 'sample_method',
        },
        type: 'card',
      });

    // Payment method update succeeds
    updatePaymentMethodScope = nock('https://api.stripe.com')
      .post(`/v1/payment_methods/${paymentMethodId}`)
      .reply(200, {
        id: paymentMethodId,
        object: 'payment_method',
        allow_redisplay: 'always',
        billing_details: {
          address: {
            country: 'AU',
          },
          name: 'sample_method',
        },
        type: 'card',
      });

    // Customer update succeeds
    updateCustomerScope = nock('https://api.stripe.com')
      .post(`/v1/customers/${stripeCustomerId}`)
      .reply(200, {
        id: stripeCustomerId,
        object: 'customer',
        metadata: {
          updatedAt: moment().format('dddd, DD, MMM YYYY h:mm a'),
          updatedBy: 'payments_user_sync_script',
        },
      });

    const retailerIds = [user.ordermentum_id];
    await runner({
      entityIds: retailerIds,
      entityType: 'retailer',
    });

    // Verify all expected calls were made
    expect(retrieveCustomerScope.isDone()).to.be.true;
    expect(updatePaymentMethodScope.isDone()).to.be.true;
    expect(updateCustomerScope.isDone()).to.be.true;

    // Verify the user was updated in the database
    const updatedUser = await User.findByPk(user.id);
    expect(updatedUser?.configuration.stripeSyncedAt).to.not.be.null;
  });

  context('batching', () => {
    it('should fetch user in a batch rather than a single query', async () => {
      const fakeRetailerIds = Array.from({ length: 5 }, () => randomUUID());
      sandbox.stub(User, 'count').resolves(fakeRetailerIds.length);
      const userFindAllStub = sandbox
        .stub(User, 'findAll')
        // @ts-expect-error - returning fake users here
        .callsFake(({ limit }) => {
          const results = fakeRetailerIds.splice(0, limit); // Fetch by batch size
          return Promise.resolve(results);
        });

      try {
        await runner({
          entityIds: fakeRetailerIds,
          entityType: 'retailer',
          batchSize: 2,
        });
      } catch (err) {
        // log will fail here but is fine as we only want to test the number of calls we make
      }

      expect(userFindAllStub.callCount).to.eql(3);
    });

    it('should not attempt to call findAll if count is zero', async () => {
      const fakeRetailerIds = Array.from({ length: 5 }, () => randomUUID());
      sandbox.stub(User, 'count').resolves(0);
      const userFindAllStub = sandbox.stub(User, 'findAll').resolves([]);

      await runner({
        entityIds: fakeRetailerIds,
        entityType: 'retailer',
        batchSize: 100,
      });

      expect(userFindAllStub.callCount).to.eql(0);
    });
  });
});
