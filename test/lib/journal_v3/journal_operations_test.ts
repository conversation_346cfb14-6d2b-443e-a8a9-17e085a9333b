import assert from 'node:assert';
import chai, { expect } from 'chai';
import { journalv3 } from '../../../src/lib/journal_v3/journal_operations';
import { getJournalById } from '../../../src/lib/journal_v3/journal_repo';
import {
  testUserRefundFn,
  testUserSaleFn,
} from '../../fixtures/journal/journal_operations_fixture';
import { JournalTestPlugin } from '../../helpers/journal_test_extentions';
import logger from '../../../src/lib/logger';

chai.use(JournalTestPlugin);

describe('journal v3 operations tests', () => {
  describe('user sales', () => {
    it('should balance when no funding required and not funded by principle', async () => {
      // ARRANGE
      const fn = testUserSaleFn({
        totalCents: 1000,
        feeCents: 100,
      });

      // ACT
      const { journalId } = await journalv3.userSale(fn, undefined, logger);

      // ASSERT
      assert(journalId);

      const journal = await getJournalById(journalId, undefined, logger);

      expect(journal.entries.length).eq(3);
      expect(journal).to.have.balancedEntries;

      // Transfer from buyer to seller
      expect(journal.entries[0]).to.be.succeededTx(1000);
      expect(journal.entries[1]).to.be.succeededTx(900);
      expect(journal.entries[2]).to.be.succeededTx(100);
    });

    it('should balance when wallet funding is required and not funded by principle', async () => {
      // ARRANGE
      const fn = testUserSaleFn({
        totalCents: 1000,
        feeCents: 100,
        walletFundedCents: 200,
      });

      // ACT
      const { journalId } = await journalv3.userSale(fn, undefined, logger);

      // ASSERT
      assert(journalId);

      const journal = await getJournalById(journalId, undefined, logger);

      expect(journal.entries).has.lengthOf(5);
      expect(journal).to.have.balancedEntries;

      // Wallet funding
      expect(journal.entries[0]).to.have.succeededTx(200);
      expect(journal.entries[1]).to.have.succeededTx(200);

      // Transfer from buyer to seller
      expect(journal.entries[2]).to.be.succeededTx(1000);
      expect(journal.entries[3]).to.be.succeededTx(900);
      expect(journal.entries[4]).to.be.succeededTx(100);
    });

    it('should balance when wallet funding is not required and the sale is funded by principle', async () => {
      // ARRANGE
      const fn = testUserSaleFn({
        totalCents: 1000,
        feeCents: 100,
        fundedByPrinciple: true,
        walletFundedCents: 0,
      });

      // ACT
      const { journalId } = await journalv3.userSale(fn, undefined, logger);

      // ASSERT
      assert(journalId);

      const journal = await getJournalById(journalId, undefined, logger);

      expect(journal.entries).has.lengthOf(5);
      expect(journal).to.have.balancedEntries;

      // Transfer from buyer to seller
      expect(journal.entries[0]).to.have.succeededTx(1000);
      expect(journal.entries[1]).to.have.succeededTx(900);
      expect(journal.entries[2]).to.have.succeededTx(100);

      // Pay out the seller immediately regardless of the async payment gateway transaction
      expect(journal.entries[3]).to.have.succeededTx(1000);
      expect(journal.entries[4]).to.have.succeededTx(1000);
    });
  });

  describe('user refunds', () => {
    it('should balance when refunding a partial amount of a transaction', async () => {
      // ARRANGE
      const fn = testUserRefundFn({
        amountCents: 1000,
      });

      // ACT
      const { journalId } = await journalv3.userRefund(fn, undefined, logger);

      // ASSERT
      assert(journalId);

      const journal = await getJournalById(journalId, undefined, logger);

      expect(journal.entries).has.lengthOf(2);
      expect(journal).to.have.balancedEntries;

      // Transfer from buyer to seller
      expect(journal.entries[0]).to.have.succeededTx(1000);
      expect(journal.entries[1]).to.have.succeededTx(1000);
    });
  });
});
