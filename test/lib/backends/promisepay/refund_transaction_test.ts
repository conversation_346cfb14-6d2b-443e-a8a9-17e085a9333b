// TODO ARCHITECTURE: promisepay/refund_transaction.ts is not used in the codebase
import sinon from 'sinon';
import { expect } from 'chai';
import business from 'moment-business';
import moment from 'moment-timezone';
import { v4 as uuid } from 'uuid';
import { Op } from 'sequelize';
import config from 'config';
import { Transaction, User, Job } from '../../../../src/models';
import refundTransaction from '../../../../src/lib/backends/promisepay/refund_transaction';
import transactionFactory from '../../../fixtures/transaction';
import { logger } from '../../../../src/lib/logger';
import {
  Backend,
  BatchTransactionType,
  TransactionInstance,
} from '../../../../src/models/transaction';
import { client } from '../../../../src/lib/backends/promisepay';
import { client as sdk } from '../../../../src/lib/backends/promisepay/client';
import {
  defaultJournalBalance,
  JournalInstance,
  JournalName,
} from '../../../../src/models/journal_v2';
import { createJournal } from '../../../helpers/journals';
import { JournalTransaction } from '../../../../src/lib/journal_v2/journal_v2';
import { JournalEntryStatus } from '../../../../src/models/journal_v2_entry';
import { systemConfig } from '../../../../src/system_config';

const OM_REFUND_USER_ID = config.get<string>('OM_REFUND_USER_ID');

describe('Refund transaction', () => {
  [true, false].forEach(doubleEntryJournal => {
    describe(`Refund transaction - double entry accounting ${
      doubleEntryJournal ? 'on' : 'off'
    }`, () => {
      let transaction: TransactionInstance;
      let sandbox: sinon.SinonSandbox;
      let time: sinon.SinonFakeTimers;
      let getWalletAccountStub: sinon.SinonStub;
      let refundStub: sinon.SinonStub;
      let createItemStub: sinon.SinonStub;
      let sellerJournal: JournalInstance;
      let buyerJournal: JournalInstance;
      let refundJournal: JournalInstance;

      beforeEach(async () => {
        systemConfig.DOUBLE_ENTRY_JOURNAL = doubleEntryJournal;
        time = sinon.useFakeTimers(new Date('2021-01-01').getTime());
        sandbox = sinon.createSandbox();
        getWalletAccountStub = sandbox
          .stub(client.users, 'showUserWalletAccounts')
          .resolves({});
        createItemStub = sandbox.stub(client.items, 'createItem').resolves({});
        refundStub = sandbox.stub(client.items, 'refund').resolves({});
        sandbox.stub(client.items, 'listItemBatchTransactions').resolves({
          // @ts-ignore
          batch_transactions: [{ type: BatchTransactionType.disbursement }],
        });
        sandbox.stub(client.items, 'makePayment').resolves({
          // @ts-ignore
          items: {
            state: 'payment_pending',
          },
        });
        systemConfig.SKIP_BACKENDS = false;
        const { data } = await transactionFactory();
        transaction = await Transaction.create(data);
        [sellerJournal, buyerJournal, refundJournal] = await Promise.all([
          createJournal(JournalName.WALLET, {
            accountId: transaction.seller_id,
          }),
          createJournal(JournalName.WALLET, {
            accountId: transaction.buyer_id,
          }),
          createJournal(JournalName.WALLET, {
            accountId: OM_REFUND_USER_ID,
          }),
        ]);
      });

      afterEach(async () => {
        systemConfig.SKIP_BACKENDS = config.get<boolean>('SKIP_BACKENDS');
        sandbox.restore();
        time.restore();
      });

      after(() => {
        systemConfig.SKIP_BACKENDS = config.get<boolean>('SKIP_BACKENDS');
        systemConfig.DOUBLE_ENTRY_JOURNAL = config.get<boolean>(
          'DOUBLE_ENTRY_JOURNAL'
        );
      });

      describe('Incompleted/Unsettled transactions', () => {
        it(`Executes refund immediately ${
          doubleEntryJournal
            ? 'and creates journal entries for the buyer and seller journals'
            : ''
        } `, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.payment_pending,
              amount: transaction.amount,
              partial: false,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: transaction.amount,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.state).to.equal(Transaction.states.refunded);
          const reversal = await Transaction.findAll({
            where: {
              seller_id: transaction.buyer_id,
              buyer_id: transaction.seller_id,
              is_reversal: true,
              name: transaction.id,
            },
          });
          expect(reversal.length).to.equal(0);
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal only affecting the available balance
            expect(sellerJournal.balances).to.eqls({
              available: {
                amount: -transaction.amount,
                debits: transaction.amount,
                credits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // Expecting a credit of the refund amount in the buyer's journal only affecting the available balance
            expect(buyerJournal.balances).to.eqls({
              available: {
                amount: transaction.amount,
                credits: transaction.amount,
                debits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // With immediate refunds, refunds journal is not involved
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
        });
      });

      describe('Completed/Settled transactions', () => {
        it(`For a completed transaction, it schedules a job that involves transferring money from the seller to the refunds account ${
          doubleEntryJournal
            ? 'and adds pending journal entries against the refund journal'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          const jobStub = sandbox.stub(Job, 'create').resolves();
          const currentDate = moment().tz('Australia/Sydney');
          const refundAmount = transaction.amount / 2;
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.completed,
              amount: refundAmount,
              partial: true,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: `${refundAmount}`,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.state).to.equal(Transaction.states.refund_flagged);
          const reversal = await Transaction.findAll({
            where: {
              seller_id: OM_REFUND_USER_ID,
              buyer_id: transaction.seller_id,
              name: transaction.id,
            },
          });
          expect(transaction.relatedTransactions).to.eqls([reversal[0].id]);
          expect(reversal.length).to.equal(1);
          expect(reversal[0].relatedTransactions).to.eqls([transaction.id]);
          const stubCall = jobStub.args.find(
            p => p[0]?.name === 'refund-check'
          )?.[0];
          expect(stubCall?.name).to.equal('refund-check');
          expect(stubCall?.data?.transactionId).to.eql(reversal[0].id);
          expect(stubCall?.data?.partial).to.be.true;
          expect(
            business.weekDays(
              currentDate,
              moment(stubCall?.nextRunAt).tz('Australia/Sydney')
            )
          ).to.eql(4);
          expect(createItemStub.called).to.be.true;
          expect(refundStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal affecting pending balances (since bank transfer)
            expect(sellerJournal.balances).to.eqls({
              pending: {
                amount: -refundAmount,
                debits: refundAmount,
                credits: 0,
              },
              available: defaultJournalBalance.pending,
            });

            // Expecting the buyer's journal to be unaffected since this is a 2 phase scheduled refund
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);

            // Expecting a debit of the refund amount in the refunds journal affecting pending balances (since bank transfer)
            expect(refundJournal.balances).to.eqls({
              pending: {
                amount: refundAmount,
                credits: refundAmount,
                debits: 0,
              },
              available: defaultJournalBalance.pending,
            });
          }
        });

        it(`For a disbursed transaction, it schedules a job that involves transferring money from the seller to the refunds account ${
          doubleEntryJournal
            ? ' and adds pending journal entries against the refund journal'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          // marking transaction disbursed
          await transaction.update({ sentAt: new Date().toISOString() });
          const jobStub = sandbox.stub(Job, 'create').resolves();
          const currentDate = moment().tz('Australia/Sydney');
          const refundAmount = transaction.amount / 2;
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.completed,
              amount: refundAmount,
              partial: true,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: `${refundAmount}`,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.state).to.equal(Transaction.states.refund_flagged);
          const reversal = await Transaction.findAll({
            where: {
              seller_id: OM_REFUND_USER_ID,
              buyer_id: transaction.seller_id,
              name: transaction.id,
            },
          });
          expect(transaction.relatedTransactions).to.eqls([reversal[0].id]);
          expect(reversal.length).to.equal(1);
          expect(reversal[0].relatedTransactions).to.eqls([transaction.id]);
          const stubCall = jobStub.args.find(
            p => p[0]?.name === 'refund-check'
          )?.[0];
          expect(stubCall?.name).to.equal('refund-check');
          expect(stubCall?.data?.transactionId).to.eql(reversal[0].id);
          expect(stubCall?.data?.partial).to.be.true;
          expect(
            business.weekDays(
              currentDate,
              moment(stubCall?.nextRunAt).tz('Australia/Sydney')
            )
          ).to.eql(4);
          expect(createItemStub.called).to.be.true;
          expect(refundStub.called).to.be.false;

          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal affecting pending balances (since bank transfer)
            expect(sellerJournal.balances).to.eqls({
              pending: {
                amount: -refundAmount,
                debits: refundAmount,
                credits: 0,
              },
              available: defaultJournalBalance.pending,
            });

            // Expecting the buyer's journal to be unaffected since this is a 2 phase scheduled refund
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);

            // Expecting a debit of the refund amount in the refunds journal affecting pending balances (since bank transfer)
            expect(refundJournal.balances).to.eqls({
              pending: {
                amount: refundAmount,
                credits: refundAmount,
                debits: 0,
              },
              available: defaultJournalBalance.pending,
            });
          }
        });

        it(`For transactions that haven't been disbursed, it checks if the seller payment provider wallet account has enough balance and refunds immediately if it does ${
          doubleEntryJournal
            ? ' and adds journal entries against the seller and buyer journals'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          getWalletAccountStub.restore();
          getWalletAccountStub = sandbox
            .stub(sdk.users, 'showUserWalletAccounts')
            .resolves({
              wallet_accounts: {
                balance: transaction.amount,
              },
            });

          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });

          const refundAmount = transaction.amount / 2;
          const settlementAmount = transaction.amount - refundAmount;
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.completed,
              amount: refundAmount,
              partial: true,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: `${transaction.amount / 2}`,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.settlementAmount).to.equal(settlementAmount);
          expect(transaction.refund_amount).to.equal(refundAmount);
          expect(transaction.refunded_at).to.exist;
          expect(transaction.refundedById).to.exist;
          expect(transaction.state).to.equal(Transaction.states.partial_refund);
          expect(refundStub.called).to.be.true;
          expect(createItemStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal only affecting the available balance
            expect(sellerJournal.balances).to.eqls({
              available: {
                amount: -refundAmount,
                debits: refundAmount,
                credits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // Expecting a credit of the refund amount in the buyer's journal only affecting the available balance
            expect(buyerJournal.balances).to.eqls({
              available: {
                amount: refundAmount,
                credits: refundAmount,
                debits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // With immediate refunds, refunds journal is not involved
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
        });

        it(`For transactions that have not yet disbursed and the seller has only wallet refunds enabled, it fails ${
          doubleEntryJournal
            ? 'and archives the journal entries against the seller and buyer journals'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          getWalletAccountStub.restore();
          getWalletAccountStub = sandbox
            .stub(sdk.users, 'showUserWalletAccounts')
            .resolves({
              wallet_accounts: {
                balance: 0,
              },
            });

          await User.update(
            {
              configuration: {
                walletRefunds: true,
              },
            },
            {
              where: {
                id: transaction.seller_id,
              },
            }
          );
          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          let e;
          try {
            await refundTransaction({
              transaction,
              data: {
                prevState: Transaction.states.completed,
                amount: transaction.amount / 2,
                partial: true,
                userId: uuid(),
                message: 'refund',
                invoices: {
                  [uuid()]: `${transaction.amount / 2}`,
                },
              },
              log: logger,
            });
          } catch (err) {
            e = err;
          }
          await transaction.reload();
          expect(e.message).to.equal(
            'Insufficient Funds Available: Please try again later'
          );
          expect(transaction.state).to.equal(Transaction.states.completed);
          expect(refundStub.called).to.be.false;
          expect(getWalletAccountStub.called).to.be.true;
          expect(createItemStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // The journals involved are unaffected
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);

            // There are however archived journal entries against the seller and buyer journals
            // But no entry against the refunds journal because this is an immediate refund
            const [entries, refundJournalEntries] = await Promise.all([
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: {
                    [Op.in]: [sellerJournal.id, buyerJournal.id],
                  },
                },
              }),
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: refundJournal.id,
                },
              }),
            ]);
            expect(entries.length).to.eqls(2);
            expect(entries.map(entry => entry.status)).to.eqls([
              JournalEntryStatus.ARCHIVED,
              JournalEntryStatus.ARCHIVED,
            ]);
            expect(refundJournalEntries.length).to.eqls(0);
          }
        });

        it(`For transactions that have not yet disbursed and the seller does not have only wallet refunds enabled, it schedules a refund  ${
          doubleEntryJournal
            ? 'and archives the journal entries against the seller and buyer journals while adding pending journal entries against the seller and refund journal'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          getWalletAccountStub.restore();
          getWalletAccountStub = sandbox
            .stub(sdk.users, 'showUserWalletAccounts')
            .resolves({
              wallet_accounts: {
                balance: 0,
              },
            });

          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          const jobStub = sandbox.stub(Job, 'create').resolves();
          const currentDate = moment().tz('Australia/Sydney');
          const refundAmount = transaction.amount / 2;
          const settlementAmount = transaction.amount - refundAmount;
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.completed,
              amount: refundAmount,
              partial: true,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: `${transaction.amount / 2}`,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.state).to.equal(Transaction.states.refund_flagged);
          const reversal = await Transaction.findAll({
            where: {
              seller_id: OM_REFUND_USER_ID,
              buyer_id: transaction.seller_id,
              name: transaction.id,
            },
          });
          expect(transaction.relatedTransactions).to.eqls([reversal[0].id]);
          expect(reversal.length).to.equal(1);
          expect(reversal[0].relatedTransactions).to.eqls([transaction.id]);

          expect(transaction.settlementAmount).to.equal(settlementAmount);
          expect(transaction.refund_amount).to.equal(refundAmount);
          expect(transaction.refunded_at).to.exist;
          expect(transaction.refundedById).to.exist;

          const stubCall = jobStub.args.find(
            p => p[0]?.name === 'refund-check'
          )?.[0];
          expect(stubCall?.name).to.equal('refund-check');
          expect(stubCall?.data?.transactionId).to.eql(reversal[0].id);
          expect(stubCall?.data?.partial).to.be.true;
          expect(
            business.weekDays(
              currentDate,
              moment(stubCall?.nextRunAt).tz('Australia/Sydney')
            )
          ).to.eql(4);
          expect(createItemStub.called).to.be.true;
          expect(refundStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal affecting pending balances (since bank transfer)
            expect(sellerJournal.balances).to.eqls({
              pending: {
                amount: -refundAmount,
                debits: refundAmount,
                credits: 0,
              },
              available: defaultJournalBalance.pending,
            });

            // Expecting the buyer's journal to be unaffected since this is a 2 phase scheduled refund
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);

            // Expecting a debit of the refund amount in the refunds journal affecting pending balances (since bank transfer)
            expect(refundJournal.balances).to.eqls({
              pending: {
                amount: refundAmount,
                credits: refundAmount,
                debits: 0,
              },
              available: defaultJournalBalance.pending,
            });

            // There are
            const [
              sellerJournalEntries,
              buyerJournalEntries,
              refundJournalEntries,
            ] = await Promise.all([
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: sellerJournal.id,
                },
              }),
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: buyerJournal.id,
                },
              }),
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: refundJournal.id,
                },
              }),
            ]);

            // An archived entry for the buyer journal
            expect(buyerJournalEntries.length).to.eqls(1);
            expect(buyerJournalEntries[0].status).to.eqls(
              JournalEntryStatus.ARCHIVED
            );

            // An archived entry and a pending entry for the seller journal
            expect(sellerJournalEntries.length).to.eqls(2);
            expect(sellerJournalEntries.map(e => e.status)).to.have.members([
              JournalEntryStatus.ARCHIVED,
              JournalEntryStatus.PENDING,
            ]);

            // A pending entry for the refund journal
            expect(refundJournalEntries.length).to.eqls(1);
            expect(refundJournalEntries[0].status).to.eqls(
              JournalEntryStatus.PENDING
            );
          }
        });

        it(`If the transaction has disbursed and the seller is configured to have wallet refunds and their wallet account has enough balance, it refunds immediately ${
          doubleEntryJournal
            ? ' and adds entries for the seller and buyer journal'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          getWalletAccountStub.restore();
          getWalletAccountStub = sandbox
            .stub(sdk.users, 'showUserWalletAccounts')
            .resolves({
              wallet_accounts: {
                balance: transaction.amount,
              },
            });
          await User.update(
            {
              configuration: {
                walletRefunds: true,
              },
            },
            {
              where: {
                id: transaction.seller_id,
              },
            }
          );
          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          const refundAmount = transaction.amount / 2;
          await refundTransaction({
            transaction,
            data: {
              prevState: Transaction.states.completed,
              amount: refundAmount,
              partial: true,
              userId: uuid(),
              message: 'refund',
              invoices: {
                [uuid()]: `${refundAmount}`,
              },
            },
            log: logger,
          });

          await transaction.reload();
          expect(transaction.state).to.equal(Transaction.states.partial_refund);
          expect(refundStub.called).to.be.true;
          expect(getWalletAccountStub.called).to.be.true;
          expect(createItemStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // Expecting a debit of the refund amount in the seller's journal only affecting the available balance
            expect(sellerJournal.balances).to.eqls({
              available: {
                amount: -refundAmount,
                debits: refundAmount,
                credits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // Expecting a credit of the refund amount in the buyer's journal only affecting the available balance
            expect(buyerJournal.balances).to.eqls({
              available: {
                amount: refundAmount,
                credits: refundAmount,
                debits: 0,
              },
              pending: defaultJournalBalance.pending,
            });

            // With immediate refunds, refunds journal is not involved
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
        });

        it(`If the transaction has disbursed and the seller is configured to have wallet refunds and their wallet account doesn't have enough balance, it fails the refund ${
          doubleEntryJournal
            ? ' and adds archived entries for the seller and buyer journal'
            : ''
        }`, async () => {
          if (doubleEntryJournal) {
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);
          }
          getWalletAccountStub.restore();
          getWalletAccountStub = sandbox
            .stub(sdk.users, 'showUserWalletAccounts')
            .resolves({
              wallet_accounts: {
                balance: 0,
              },
            });
          await User.update(
            {
              configuration: {
                walletRefunds: true,
              },
            },
            {
              where: {
                id: transaction.seller_id,
              },
            }
          );
          await User.findOrCreate({
            where: {
              id: OM_REFUND_USER_ID,
            },
            // @ts-ignore
            defaults: {
              id: OM_REFUND_USER_ID,
              ordermentum_id: uuid(),
              first_name: '',
              last_name: '',
              email: '',
              address_line_1: '',
              city: '',
              state: '',
              postcode: '',
              country: '',
              backend: Backend.PROMISEPAY,
            },
          });
          let e;
          try {
            await refundTransaction({
              transaction,
              data: {
                prevState: Transaction.states.completed,
                amount: transaction.amount / 2,
                partial: true,
                userId: uuid(),
                message: 'refund',
                invoices: {
                  [uuid()]: `${transaction.amount / 2}`,
                },
              },
              log: logger,
            });
          } catch (err) {
            e = err;
          }
          await transaction.reload();
          expect(e.message).to.equal(
            'Insufficient Funds Available: Please try again later'
          );
          expect(transaction.state).to.equal(Transaction.states.completed);
          expect(refundStub.called).to.be.false;
          expect(createItemStub.called).to.be.false;
          if (doubleEntryJournal) {
            await Promise.all([
              sellerJournal.reload(),
              buyerJournal.reload(),
              refundJournal.reload(),
            ]);

            // The journals involved are unaffected
            expect(sellerJournal.balances).to.eqls(defaultJournalBalance);
            expect(buyerJournal.balances).to.eqls(defaultJournalBalance);
            expect(refundJournal.balances).to.eqls(defaultJournalBalance);

            // There are however archived journal entries against the seller and buyer journals
            // But no entry against the refunds journal because this is an immediate refund
            const [entries, refundJournalEntries] = await Promise.all([
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: {
                    [Op.in]: [sellerJournal.id, buyerJournal.id],
                  },
                },
              }),
              JournalTransaction.JournalEntry.findAll({
                where: {
                  journalId: refundJournal.id,
                },
              }),
            ]);
            expect(entries.length).to.eqls(2);
            expect(entries.map(entry => entry.status)).to.eqls([
              JournalEntryStatus.ARCHIVED,
              JournalEntryStatus.ARCHIVED,
            ]);
            expect(refundJournalEntries.length).to.eqls(0);
          }
        });
      });
    });
  });
});
