import sinon from 'sinon';
import config from 'config';
import { v4 as uuid } from 'uuid';
import { expect } from 'chai';
import { randomUUID } from 'crypto';
import BigNumber from 'bignumber.js';
import nock from 'nock';
import { TestWorkflowEnvironment } from '@temporalio/testing';
import * as temporal from '@ordermentum/temporal';
import { createTestWorker } from '@ordermentum/temporal';
import { TemporalFailure } from '@temporalio/common';
import { Transaction, Job, User } from '../../../../src/models';
import transactionFactory from '../../../fixtures/transaction';
import {
  client,
  createTransaction,
} from '../../../../src/lib/backends/promisepay';
import { cardFixture } from '../../../fixtures/card_payment_method';
import { bankFixture } from '../../../fixtures/bank_payment_method';
import {
  Backend,
  states,
  TransactionInstance,
} from '../../../../src/models/transaction';
import * as holdFundsInWalletAction from '../../../../src/actions/hold_funds_in_wallet_action';
import { asyncClient } from '../../../../src/lib/backends/promisepay/client';
import { makeZaiPaymentWorkflowDefinition } from '../../../../src/ng/workflow/definitions/make_zai_payment.workflow';
import { userFixtureData } from '../../../fixtures/user';
import * as postHoldFundsInWalletAction from '../../../../src/actions/post_hold_funds_in_wallet_action';

const OM_WALLET_USER_ID = config.get<string>('OM_WALLET_USER_ID');

describe('promisepay actions', () => {
  let testEnv: TestWorkflowEnvironment;
  let sandbox: sinon.SinonSandbox;
  let temporalClientWorkflowStartSpy: sinon.SinonSpy;

  // Setup fixture
  // These users are database users that are used for various payments features
  // TODO: Test setup files should live in a global setup file
  before(async () => {
    testEnv = await TestWorkflowEnvironment.createLocal();
    await User.findOrCreate({
      where: { id: OM_WALLET_USER_ID },
      defaults: userFixtureData({
        id: OM_WALLET_USER_ID,
        first_name: 'Wallet',
        last_name: 'User',
        email: '<EMAIL>',
        configuration: {},
        status: 'active',
        backend: Backend.PROMISEPAY,
      }),
    });
  });

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
    temporalClientWorkflowStartSpy = sandbox.spy(
      testEnv.client.workflow,
      'start'
    );

    sandbox.stub(temporal, 'getTemporalClient').resolves(testEnv.client);
  });

  afterEach(async () => {
    nock.cleanAll();
    sandbox.restore();
  });

  after(async () => {
    await testEnv.teardown();
  });

  describe('create_transaction tryAsync: false', () => {
    let transaction: TransactionInstance;
    let holdFundsStub: sinon.SinonStub;

    beforeEach(async () => {
      const { data } = await transactionFactory();
      transaction = await Transaction.create({
        ...data,
        id: randomUUID(),
      });
      await transaction.reload({
        include: [
          {
            model: User,
            as: 'seller',
          },
        ],
      });

      holdFundsStub = sandbox
        .stub(holdFundsInWalletAction, 'holdFundsInWalletAction')
        .resolves({
          fundedAmountCents: 0,
          holdingTransactionId: uuid(),
        });
    });

    it('correctly charge', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });
      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction(transaction.get());
    });

    it('uses wallet if funds available', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: true,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: 10000 },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction(transaction.get());
      expect(walletStub.called).to.equal(true);
      expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
      expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
      expect(createItemStub.args[0][0].seller_id).to.eqls(seller?.external_id);
    });

    it('uses wallet if transaction explicitly states to use wallet account', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: 10000 },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction({ ...transaction.get(), useWalletAccount: true });
      expect(walletStub.called).to.equal(true);
      expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
      expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
      expect(createItemStub.args[0][0].seller_id).to.eqls(seller?.external_id);
    });

    it('uses wallet if transaction explicitly states to use wallet account and replenishes wallet if transaction states replenishment is allowed', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const shortFall = 1000; // $10 short

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .onFirstCall()
        .resolves({
          wallet_accounts: {
            id: randomUUID(),
            balance: transaction.amount - shortFall,
          },
        })
        .onSecondCall()
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: transaction.amount },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction({
        ...transaction.get(),
        useWalletAccount: true,
        replenishBuyerWallet: true,
      });
      expect(walletStub.called).to.equal(true);
      expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
      expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
      expect(createItemStub.args[0][0].seller_id).to.eqls(seller?.external_id);
      expect(holdFundsStub.called).to.equal(true);
      expect(holdFundsStub.args[0][0].amount).to.eqls(
        new BigNumber(shortFall).dividedBy(100).toFixed(2)
      );
    });

    it('uses wallet if transaction explicitly states to use wallet account and does not replenish the wallet of any shortfall if a bank payment method is used', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const shortFall = 1000; // $10 short

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .onFirstCall()
        .resolves({
          wallet_accounts: {
            id: randomUUID(),
            balance: transaction.amount - shortFall,
          },
        })
        .onSecondCall()
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: transaction.amount },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const bank = await bankFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: null,
        bank_payment_method_id: bank.id,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      let e;
      try {
        await createTransaction({
          ...transaction.get(),
          useWalletAccount: true,
          replenishBuyerWallet: true,
        });
      } catch (err) {
        e = err;
      }
      expect(e).to.not.be.undefined;
      expect(walletStub.called).to.equal(true);
      expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
      expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
      expect(createItemStub.args[0][0].seller_id).to.eqls(seller?.external_id);
      expect(holdFundsStub.called).to.equal(false);
    });

    it('fails', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'pending' } });
      sandbox.stub(client.items, 'showItem').resolves({});
      let err;
      try {
        await createTransaction(transaction.get());
      } catch (e) {
        err = e;
      }
      expect(err).to.not.equal(null);
    });

    it.skip('correctly change transaction status for escrow', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_deposited' } });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction(transaction.get());
      const updated = await Transaction.findByPk(transaction.id);
      expect(updated?.invoiceIds.length).to.be.equal(2);
      expect(updated?.state).to.equal('payment_deposited');
      const job = await Job.findOne({
        where: {
          name: 'trigger pp disbursement',
          data: {
            transaction: {
              id: transaction.id,
            },
          },
        },
      });
      expect(job).to.equal(null);
    });

    it('correctly change transaction status for card transactions', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'completed' } });
      sandbox.stub(client.items, 'showItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );

      const updatedTransaction = await Transaction.findByPk(transaction.id);

      await createTransaction(updatedTransaction!);

      const updated = await Transaction.findByPk(transaction.id);
      expect(updated?.state).to.equal('completed');
      const job = await Job.findOne({
        where: {
          name: 'trigger pp disbursement',
          data: {
            transaction: {
              id: transaction.id,
            },
          },
        },
      });
      expect(job).to.equal(null);
    });

    it('default error message', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(client.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: [
                "Credit card authorization failed: MW - 001:Field 'customerCountry' was invalid or not supplied",
              ],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      try {
        await createTransaction(updatedTransaction!);
      } catch (ex) {
        err = ex;
      }
      expect(err).to.not.be.undefined;
    });

    it('marks item as failed if card payment fails', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(client.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          body: { errors: { data: [{ id: 'invvalid number' }] } },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );

      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err = false;
      try {
        await createTransaction(updatedTransaction!);
      } catch (ex) {
        const updated = await Transaction.findByPk(transaction.id);
        expect(updated?.state).to.equal('failed');
        const job = await Job.findOne({
          where: {
            name: 'trigger pp disbursement',
            data: {
              transaction: {
                id: transaction.id,
              },
            },
          },
        });
        err = true;
        expect(job).to.equal(null);
      }
      expect(err).to.equal(true);
    });

    it('Generates correct description of error against known errors', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(client.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: ['Credit card payment failed: Do Not Honor.'],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      try {
        await createTransaction(updatedTransaction!);
      } catch (ex) {
        err = ex;
      }

      expect(err).to.not.be.undefined;
    });

    it('Gives back the error string as-is if error string is unknown', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(client.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: ['This is an unknown message from the backend'],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      try {
        await createTransaction(updatedTransaction!);
      } catch (ex) {
        err = ex;
      }
      expect(err).to.not.be.undefined;
    });

    it('If error returned from AP is of unknown structure, it is returned as-is', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(client.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: {
                base: ['This is an unknown message from the backend'],
              },
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      try {
        await createTransaction(updatedTransaction!);
      } catch (ex) {
        err = ex;
      }
      expect(err).to.not.be.undefined;
    });
  });

  describe('create_transaction tryAsync: true', () => {
    let transaction: TransactionInstance;
    let holdFundsStub: sinon.SinonStub;

    beforeEach(async () => {
      const { data } = await transactionFactory();
      transaction = await Transaction.create(data);
      await transaction.reload({
        include: [
          {
            model: User,
            as: 'seller',
          },
        ],
      });
    });

    it('Adds sellers phone number when making payments via payment method and wallet', async () => {
      // Setup stubs and fixtures
      sandbox.stub(client.items, 'createItem').resolves({});
      const makeAsyncPaymentStub = sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({
        wallet_accounts: { id: randomUUID(), balance: 10000 },
      });

      const { data } = await transactionFactory();
      transaction = await Transaction.create({
        ...data,
        id: randomUUID(),
      });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });
      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      await transaction.reload();

      const buyer = await User.findByPk(transaction.buyer_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: true,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      // Run and assert
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        await transaction.reload();
        expect(makeAsyncPaymentStub.called).to.be.false;
        await transaction.reload({
          include: [{ model: User, as: 'seller' }],
        });
        expect(transaction.seller).to.not.be.null;
        const transactionWithSeller = transaction as TransactionInstance & {
          seller: InstanceType<typeof User>;
        };

        expect(temporalClientWorkflowStartSpy.called).to.be.true;
        // Keeping parity with the old code
        // Ideally we should be using the workflow result to assert this
        expect(
          temporalClientWorkflowStartSpy.args[0][1].args[0].body.merchant_phone
        ).to.eqls(transactionWithSeller.seller.mobile_number);
      });
    });

    it('correctly charge card', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });
      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        await transaction.reload();
        expect(temporalClientWorkflowStartSpy.called).to.be.true;
      });
    });

    it('correctly charge bank (uses sync)', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      const bank = await bankFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: null,
        bank_payment_method_id: bank.id,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        await transaction.reload();
        expect(temporalClientWorkflowStartSpy.called).to.be.false;
      });
    });

    it('uses wallet if funds available, will be sync', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: true,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: 10000 },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        await transaction.reload();
        expect(walletStub.called).to.equal(true);
        expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
        expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
        expect(createItemStub.args[0][0].seller_id).to.eqls(
          seller?.external_id
        );
      });
    });

    it('uses wallet if transaction explicitly states to use wallet account', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: 10000 },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({
          ...transaction.get(),
          useWalletAccount: true,
          tryAsync: true,
        });
        expect(walletStub.called).to.equal(true);
        expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
        expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
        expect(createItemStub.args[0][0].seller_id).to.eqls(
          seller?.external_id
        );
      });
    });

    describe('wallet replenishment', () => {
      it('skips wallet if transactionType is capture and there is no funds in the wallet', async () => {
        const buyer = await User.findByPk(transaction.buyer_id);

        nock('https://au-0000.sandbox.auth.assemblypay.com')
          .post('/tokens')
          .reply(200, {
            access_token: '1',
          })
          .persist();

        nock('https://test.api.promisepay.com/')
          .post('/items')
          .reply(200, {
            items: {
              id: transaction.id,
              state: states.pending,
            },
          });

        nock('https://test.api.promisepay.com/')
          .get(`/users/${buyer?.external_id}/wallet_accounts`)
          .reply(200, {
            wallet_accounts: {
              id: randomUUID(),
              balance: 0,
            },
          });

        buyer?.update({
          configuration: {
            ...buyer.configuration,
            walletFunds: false,
          },
        });

        const card = await cardFixture({
          user_id: transaction.buyer_id,
          backend_id: uuid(),
        });

        await transaction.update({
          card_payment_method_id: card.id,
          transactionType: 'capture',
          bank_payment_method_id: null,
        });
        const worker = await createTestWorker({
          connection: testEnv.nativeConnection,
          taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
          workflowDefinition: makeZaiPaymentWorkflowDefinition,
          activities: {},
        });
        await worker.runUntil(async () => {
          await createTransaction({
            ...transaction.get(),
            useWalletAccount: true,
            tryAsync: true,
          });
          await transaction.reload();
          expect(transaction.transactionType).to.equal('capture');
          expect(temporalClientWorkflowStartSpy.called).to.be.true;
        });
      });

      it('uses wallet if funds available just uses wallet', async () => {
        const buyer = await User.findByPk(transaction.buyer_id);

        nock('https://au-0000.sandbox.auth.assemblypay.com')
          .post('/tokens')
          .reply(200, {
            access_token: '1',
          })
          .persist();

        nock('https://test.api.promisepay.com/')
          .post('/items')
          .reply(200, {
            items: {
              id: transaction.id,
              state: states.pending,
            },
          });

        nock('https://test.api.promisepay.com/')
          .get(`/users/${buyer?.external_id}/wallet_accounts`)
          .reply(200, {
            wallet_accounts: {
              id: randomUUID(),
              balance: 100000,
            },
          });

        await buyer?.update({
          configuration: {
            ...buyer.configuration,
            walletFunds: true,
          },
        });

        const card = await cardFixture({
          user_id: transaction.buyer_id,
          backend_id: uuid(),
        });

        await transaction.update({
          state: states.pending,
          card_payment_method_id: card.id,
          transactionType: 'capture',
          bank_payment_method_id: null,
        });

        await createTransaction({
          ...transaction.get(),
          useWalletAccount: true,
          tryAsync: true,
        });
        await transaction.reload();
        expect(temporalClientWorkflowStartSpy.called).to.be.true;
      });

      it('uses wallet if funds available and replenishes wallet if escrow', async () => {
        sandbox
          .stub(postHoldFundsInWalletAction, 'postHoldFundsInWalletAction')
          .resolves();
        const buyer = await User.findByPk(transaction.buyer_id);
        const seller = await User.findByPk(transaction.seller_id);

        nock('https://au-0000.sandbox.auth.assemblypay.com')
          .post('/tokens')
          .reply(200, {
            access_token: '1',
          })
          .persist();

        nock('https://test.api.promisepay.com/')
          .post('/items')
          .reply(200, {
            items: {
              id: transaction.id,
              state: states.pending,
            },
          });

        nock('https://test.api.promisepay.com/')
          .get(`/users/${buyer?.external_id}/wallet_accounts`)
          .reply(200, {
            wallet_accounts: {
              id: randomUUID(),
              balance: 1,
            },
          })
          .get(`/users/${buyer?.external_id}/wallet_accounts`)
          .reply(200, {
            wallet_accounts: {
              id: randomUUID(),
              balance: 10000,
            },
          })
          .get(`/users/${buyer?.external_id}/wallet_accounts`)
          .reply(200, {
            wallet_accounts: {
              id: randomUUID(),
              balance: 10000,
            },
          });

        // holding transactions - unknown transaction id
        nock('https://test.api.promisepay.com/')
          .post('/items')
          .reply(200, {
            items: {
              id: randomUUID(),
              state: states.pending,
            },
          });

        nock('https://test.api.promisepay.com/')
          .patch(/items\/.*\/make_payment/)
          .reply(200, {
            items: {
              state: 'completed',
              payment_method: 'wallet',
            },
          });

        nock('https://test.api.promisepay.com/')
          .patch(`/items/${transaction.id}/make_payment`)
          .reply(200, {
            items: {
              state: 'completed',
              payment_method: 'wallet',
            },
          });

        buyer?.update({
          configuration: {
            ...buyer.configuration,
            walletFunds: true,
          },
        });

        const card = await cardFixture({
          user_id: transaction.buyer_id,
          backend_id: uuid(),
        });

        await transaction.update({
          state: states.pending,
          card_payment_method_id: card.id,
          transactionType: 'capture',
          bank_payment_method_id: null,
        });

        await createTransaction({
          ...transaction.get(),
          useWalletAccount: true,
          tryAsync: false,
        });
        await transaction.reload();
        expect(transaction.transactionType).to.equal('capture');
        const holdingTransaction = await Transaction.findOne({
          where: {
            buyer_id: transaction.buyer_id,
            seller_id: OM_WALLET_USER_ID,
            transactionType: 'escrow',
          },
        });

        expect(holdingTransaction).to.not.be.null;
      });
    });

    it('uses wallet if transaction explicitly states to use wallet account and replenishes wallet if transaction states replenishment is allowed', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const shortFall = 1000; // $10 short

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .onFirstCall()
        .resolves({
          wallet_accounts: {
            id: randomUUID(),
            balance: transaction.amount - shortFall,
          },
        })
        .onSecondCall()
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: transaction.amount },
        });
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });

      sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      holdFundsStub = sandbox
        .stub(holdFundsInWalletAction, 'holdFundsInWalletAction')
        .resolves({
          fundedAmountCents: 0,
          holdingTransactionId: uuid(),
        });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({
          ...transaction.get(),
          useWalletAccount: true,
          replenishBuyerWallet: true,
          tryAsync: true,
        });
        expect(walletStub.called).to.equal(true);
        expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
        expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
        expect(createItemStub.args[0][0].seller_id).to.eqls(
          seller?.external_id
        );
        expect(holdFundsStub.called).to.equal(true);
        expect(holdFundsStub.args[0][0].amount).to.eqls(
          new BigNumber(shortFall).dividedBy(100).toFixed(2)
        );
      });
    });

    it('uses wallet if transaction explicitly states to use wallet account and does not replenish the wallet of any shortfall if a bank payment method is used: fails with error', async () => {
      const buyer = await User.findByPk(transaction.buyer_id);
      const seller = await User.findByPk(transaction.seller_id);
      await User.update(
        {
          configuration: {
            ...buyer!.configuration,
            walletFunds: false,
          },
        },
        {
          where: {
            id: buyer!.id,
          },
        }
      );

      const shortFall = 1000; // $10 short

      const createItemStub = sandbox
        .stub(client.items, 'createItem')
        .resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .onFirstCall()
        .resolves({
          wallet_accounts: {
            id: randomUUID(),
            balance: transaction.amount - shortFall,
          },
        })
        .onSecondCall()
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: transaction.amount },
        });

      holdFundsStub = sandbox
        .stub(holdFundsInWalletAction, 'holdFundsInWalletAction')
        .resolves({
          fundedAmountCents: 0,
          holdingTransactionId: uuid(),
        });

      const bank = await bankFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await transaction.update({
        card_payment_method_id: null,
        bank_payment_method_id: bank.id,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      let e;
      await worker.runUntil(async () => {
        try {
          await createTransaction({
            ...transaction.get(),
            useWalletAccount: true,
            replenishBuyerWallet: true,
            tryAsync: true,
          });
        } catch (err) {
          e = err;
        }
        expect(e).to.not.be.undefined;
        expect(walletStub.called).to.equal(true);
        expect(createItemStub.args[0][0].amount).to.eqls(transaction.amount);
        expect(createItemStub.args[0][0].buyer_id).to.eqls(buyer?.external_id);
        expect(createItemStub.args[0][0].seller_id).to.eqls(
          seller?.external_id
        );
        expect(holdFundsStub.called).to.equal(false);
      });
    });

    it('fails', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      let err;
      await worker.runUntil(async () => {
        try {
          await createTransaction({ ...transaction.get(), tryAsync: true });
        } catch (e) {
          err = e;
        }
        expect(err).to.not.equal(null);
      });
    });

    it.skip('correctly change transaction status for escrow', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_deposited' } });

      sandbox.stub(client.items, 'showItem').resolves({});
      await createTransaction({ ...transaction.get(), tryAsync: true });
      const updated = await Transaction.findByPk(transaction.id);
      expect(updated?.invoiceIds.length).to.be.equal(2);
      expect(updated?.state).to.equal('payment_deposited');
      const job = await Job.findOne({
        where: {
          name: 'trigger pp disbursement',
          data: {
            transaction: {
              id: transaction.id,
            },
          },
        },
      });
      expect(job).to.equal(null);
    });

    it('correctly change transaction status for card transactions', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });
      sandbox.stub(client.items, 'showItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );

      const updatedTransaction = await Transaction.findByPk(transaction.id);
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({
          ...updatedTransaction!.get(),
          tryAsync: true,
        });
        const updated = await Transaction.findByPk(transaction.id);
        expect(updated?.state).to.equal('completed');
        const job = await Job.findOne({
          where: {
            name: 'trigger pp disbursement',
            data: {
              transaction: {
                id: transaction.id,
              },
            },
          },
        });
        expect(job).to.equal(null);
      });
    });

    it('default error message', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(asyncClient.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: [
                "Credit card authorization failed: MW - 001:Field 'customerCountry' was invalid or not supplied",
              ],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        try {
          await createTransaction({ ...updatedTransaction!, tryAsync: true });
        } catch (ex) {
          err = ex;
        }
        expect(err).to.not.be.undefined;
        await transaction.reload();
        expect(transaction.get().context?.isZaiAsync).to.be.undefined;
      });
    });

    it('marks item as failed if card payment fails', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      temporalClientWorkflowStartSpy.restore();
      sandbox
        .stub(testEnv.client.workflow, 'start')
        .rejects(new TemporalFailure());
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );

      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err = false;
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        try {
          await createTransaction({
            ...updatedTransaction!.get(),
            tryAsync: true,
          });
        } catch (ex) {
          const updated = await Transaction.findByPk(transaction.id);
          expect(updated?.state).to.equal('failed');
          const job = await Job.findOne({
            where: {
              name: 'trigger pp disbursement',
              data: {
                transaction: {
                  id: transaction.id,
                },
              },
            },
          });
          err = true;
          expect(job).to.equal(null);
        }
        expect(err).to.equal(true);
      });
    });

    it('Generates correct description of error against known errors', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(asyncClient.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: ['Credit card payment failed: Do Not Honor.'],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        try {
          await createTransaction({ ...updatedTransaction!, tryAsync: true });
        } catch (ex) {
          err = ex;
        }
        expect(err).to.not.be.undefined;
      });
    });

    it('Gives back the error string as-is if error string is unknown', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(asyncClient.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: ['This is an unknown message from the backend'],
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});
      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        try {
          await createTransaction({ ...updatedTransaction!, tryAsync: true });
        } catch (ex) {
          err = ex;
        }
        expect(err).to.not.be.undefined;
      });
    });

    it('If error returned from AP is of unknown structure, it is returned as-is', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});
      sandbox.stub(asyncClient.items, 'makePayment').rejects({
        response: {
          statusCode: 422,
          data: {
            errors: {
              base: {
                base: ['This is an unknown message from the backend'],
              },
            },
          },
        },
      });
      sandbox.stub(client.items, 'showItem').resolves({});
      sandbox.stub(client.items, 'deleteItem').resolves({});

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });

      await Transaction.update(
        {
          bank_payment_method_id: null,
          card_payment_method_id: card.id,
        },
        {
          where: {
            id: transaction.id,
          },
        }
      );
      const updatedTransaction = await Transaction.findByPk(transaction.id);
      let err;
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        try {
          await createTransaction({ ...updatedTransaction!, tryAsync: true });
        } catch (ex) {
          err = ex;
        }
        expect(err).to.not.be.undefined;
      });
    });

    it('uses workflow when tryAsync is true', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      sandbox.stub(client.users, 'showUserWalletAccounts').resolves({});

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });
      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        expect(temporalClientWorkflowStartSpy.called).to.be.true;
      });
    });

    it('uses workflow with wallet payments when tryAsync is true', async () => {
      sandbox.stub(client.items, 'createItem').resolves({});
      const walletStub = sandbox
        .stub(client.users, 'showUserWalletAccounts')
        .resolves({
          wallet_accounts: { id: randomUUID(), balance: 10000 },
        });
      const makePaymentStub = sandbox
        .stub(client.items, 'makePayment')
        // @ts-expect-error
        .resolves({ items: { state: 'payment_pending' } });
      const makeAsyncPaymentStub = sandbox
        .stub(asyncClient.items, 'makeAsyncPayment')
        .resolves({ last_updated: '2016-04-18T07:37:29.580Z' });

      const card = await cardFixture({
        user_id: transaction.buyer_id,
        backend_id: uuid(),
      });
      await transaction.update({
        card_payment_method_id: card.id,
        bank_payment_method_id: null,
      });

      sandbox.stub(client.items, 'showItem').resolves({});
      const worker = await createTestWorker({
        connection: testEnv.nativeConnection,
        taskQueue: makeZaiPaymentWorkflowDefinition.queueName,
        workflowDefinition: makeZaiPaymentWorkflowDefinition,
        activities: {},
      });
      await worker.runUntil(async () => {
        await createTransaction({ ...transaction.get(), tryAsync: true });
        expect(makeAsyncPaymentStub.called).to.equal(false);
        expect(walletStub.called).to.equal(true);
        expect(temporalClientWorkflowStartSpy.called).to.be.true;
      });
    });
  });
});
