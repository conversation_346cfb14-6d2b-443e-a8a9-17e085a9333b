import sinon from 'sinon';
import moment from 'moment-timezone';
import { expect } from 'chai';
import { calculateRelease } from '../../../../src/lib/calculations';

describe('promisepay actions', () => {
  describe('handle item', () => {
    let sandbox;

    beforeEach(() => {
      sandbox = sinon.createSandbox();
    });

    afterEach(() => {
      sandbox.restore();
    });

    // simplified mock user for testing calculations
    const createMockUser = (
      settings: {
        userSetting?: { settlementDelayDays?: number };
        configuration?: { weeklySettlement?: boolean };
      } = {}
    ) => ({
      userSetting: {
        settlementDelayDays: 0,
        ...settings.userSetting,
      },
      configuration: {
        ...settings.configuration,
      },
    });

    it('should skip weekends for release', () => {
      const user = createMockUser();
      const friday = moment.tz('2017-11-17T14:00:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, friday, 48, false);
      // Should be:
      // 1. Friday after cutoff -> moves to Monday 1:30pm
      // 2. Plus 2 business days -> moves to Tuesday 1:30pm
      expect(releaseAt).to.equal('2017-11-21T02:30:00.000Z');
    });

    it('should be 2 business days for release', () => {
      const user = createMockUser();
      const monday = moment.tz('2017-11-12T14:00:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, monday, 48, false);
      // Should be:
      // 1. Monday after cutoff -> moves to Tuesday 1:30pm
      // 2. Plus 2 business days -> moves to Thursday 1:30pm
      expect(releaseAt).to.equal('2017-11-14T02:30:00.000Z');
    });

    it('should return same day if before cut off', () => {
      const user = createMockUser();
      const monday = moment.tz('2017-11-12T01:30:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, monday, 0, false);
      // Should be:
      // 1. Monday before cutoff -> stays on Monday 1:30pm
      expect(releaseAt).to.equal('2017-11-12T02:30:00.000Z');
    });

    it('should return next day if after cut off', () => {
      const user = createMockUser();
      const monday = moment.tz('2017-11-12T18:30:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, monday, 0, false);
      // Should be:
      // 1. Monday after cutoff -> moves to Tuesday 1:30pm
      expect(releaseAt).to.equal('2017-11-13T02:30:00.000Z');
    });

    it('should return wednesday if weekly settlement set on user configuration', () => {
      const user = createMockUser({
        configuration: { weeklySettlement: true },
      });
      const monday = moment.tz('2017-11-12T18:30:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      let releaseAt = calculateRelease(user, monday, 0, false);
      // Should be:
      // 1. Monday after cutoff -> moves to Tuesday 1:30pm
      // 2. Weekly settlement -> moves to next Wednesday 1:30pm
      expect(releaseAt).to.equal('2017-11-22T02:30:00.000Z');
      const wednesday = moment.tz(
        '2017-11-22T18:30:00.000',
        'Australia/Sydney'
      );
      // @ts-expect-error - Using simplified mock user object for testing calculations
      releaseAt = calculateRelease(user, wednesday, 0, false);
      // Should be:
      // 1. Wednesday after cutoff -> moves to Thursday 1:30pm
      // 2. Weekly settlement -> moves to next Wednesday 1:30pm
      expect(releaseAt).to.equal('2017-11-29T02:30:00.000Z');
    });

    it('should add settlement delay days when user settings has settlementDelayDays for card payments', () => {
      const user = createMockUser({
        userSetting: {
          settlementDelayDays: 1,
        },
      });
      const monday = moment.tz('2017-11-12T14:00:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, monday, 48, true);
      // Should be:
      // 1. Monday after cutoff -> moves to Tuesday 1:30pm
      // 2. Plus 2 business days -> moves to Thursday 1:30pm
      // 3. Plus settlement delay -> moves to Friday 1:30pm
      expect(releaseAt).to.equal('2017-11-15T02:30:00.000Z');
    });

    it('should not add settlement delay for bank payments even when configured', () => {
      const user = createMockUser({
        userSetting: {
          settlementDelayDays: 1,
        },
      });
      const monday = moment.tz('2017-11-12T14:00:00.000', 'Australia/Sydney');
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, monday, 48, false);
      // Should be:
      // 1. Monday after cutoff -> moves to Tuesday 1:30pm
      // 2. Plus 2 business days -> moves to Thursday 1:30pm
      // 3. Settlement delay ignored for bank payments
      expect(releaseAt).to.equal('2017-11-14T02:30:00.000Z');
    });

    it('should handle after cutoff time with settlement delay for card payments', () => {
      const user = createMockUser({
        userSetting: {
          settlementDelayDays: 1,
        },
      });

      // Monday 2:30pm (after cutoff)
      const mondayAfterCutoff = moment.tz(
        '2017-11-12T14:30:00.000',
        'Australia/Sydney'
      );

      // Should be:
      // 1. After cutoff -> moves to Tuesday 1:30pm
      // 2. Plus settlement delay -> moves to Wednesday 1:30pm
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, mondayAfterCutoff, 0, true);
      expect(releaseAt).to.equal('2017-11-14T02:30:00.000Z');
    });

    it('should handle before cutoff time with settlement delay for card payments', () => {
      const user = createMockUser({
        userSetting: {
          settlementDelayDays: 1,
        },
      });

      // Monday 1:00pm (before cutoff)
      const mondayBeforeCutoff = moment.tz(
        '2017-11-12T13:00:00.000',
        'Australia/Sydney'
      );

      // Should be:
      // 1. Before cutoff -> stays on Monday 1:30pm
      // 2. Plus settlement delay -> moves to Tuesday 1:30pm
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, mondayBeforeCutoff, 0, true);
      expect(releaseAt).to.equal('2017-11-13T02:30:00.000Z');
    });

    it('should handle weekend cutoff with settlement delay for card payments', () => {
      const user = createMockUser({
        userSetting: {
          settlementDelayDays: 1,
        },
      });

      // Friday 2:30pm (after cutoff)
      const fridayAfterCutoff = moment.tz(
        '2017-11-17T14:30:00.000',
        'Australia/Sydney'
      );

      // Should be:
      // 1. After cutoff -> moves to Monday 1:30pm
      // 2. Plus settlement delay -> moves to Tuesday 1:30pm
      // @ts-expect-error - Using simplified mock user object for testing calculations
      const releaseAt = calculateRelease(user, fridayAfterCutoff, 0, true);
      expect(releaseAt).to.equal('2017-11-21T02:30:00.000Z');
    });
  });
});
