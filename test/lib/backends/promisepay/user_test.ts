import { expect } from 'chai';
import { describe, it } from 'mocha';
import { userFixtureData } from '../../../fixtures/user';
import {
  convertUser,
  extractCompany,
} from '../../../../src/lib/backends/promisepay/user';

describe('Users', () => {
  describe('convertUser', () => {
    it('should convert a user object to the expected Zai format', () => {
      const fixture = userFixtureData({
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address_line_1: '123 Test St',
        city: 'Sydney',
        state: 'NSW',
        postcode: '2000',
        country: 'AUS',
        mobile_number: '+61412345678',
        dob: '1981-02-21T00:00:00.000Z',
        external_id: 'external-123',
      });

      const result = convertUser(fixture);
      expect(result).to.deep.equal({
        email: '<EMAIL>',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        address_line1: '123 Test St',
        city: 'Sydney',
        state: 'NSW',
        zip: '2000',
        country: 'AUS',
        mobile: '+61412345678',
        dob: '21/02/1981',
        id: 'external-123',
      });
    });

    it('should return undefined for dob if not provided', () => {
      const fixture = userFixtureData({ dob: null });
      const result = convertUser(fixture);
      expect(result.dob).to.be.undefined;
    });
  });

  describe('extractCompany', () => {
    it('should convert a user object to the expected Zai company format', () => {
      const fixture = userFixtureData({
        company_name: 'Test Company',
        company_legal_name: 'Test Legal Company',
        company_tax_number: '*********',
        company_address_line_1: '456 Company St',
        company_address_line_2: 'Suite 100',
        company_city: 'Melbourne',
        company_state: 'VIC',
        company_postcode: '3000',
        company_country: 'AUS',
        mobile_number: '+61487654321',
      });

      const result = extractCompany(fixture);
      expect(result).to.deep.equal({
        name: 'Test Company',
        legal_name: 'Test Legal Company',
        tax_number: '*********',
        address_line1: '456 Company St',
        address_line2: 'Suite 100',
        city: 'Melbourne',
        state: 'VIC',
        zip: '3000',
        country: 'AUS',
        phone: '+61487654321',
      });
    });
  });
});
