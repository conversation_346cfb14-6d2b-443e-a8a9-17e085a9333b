import sinon from 'sinon';
import { expect } from 'chai';
import { Op } from 'sequelize';
import { randomUUID } from 'crypto';
import config from 'config';
import { createPaymentTransaction } from '../../src/actions/create_payment_transaction';
import { create } from '../fixtures/user';
import { JournalName, NormalBalance } from '../../src/models/journal_v2';
import { CurrencyCodes } from '../../src/types/currency_codes';
import * as createTransactionZai from '../../src/lib/backends/promisepay';
import { Backend, states } from '../../src/models/transaction';
import { JournalTransaction } from '../../src/lib/journal_v2/journal_v2';
import { JournalEntryStatus } from '../../src/models/journal_v2_entry';
import { Job, Transaction } from '../../src/models';
import { bankFixture } from '../fixtures/bank_payment_method';
import { cardFixture } from '../fixtures/card_payment_method';
import * as transactionChangedEvent from '../../src/helpers/transaction_change_event';
import { systemConfig } from '../../src/system_config';

describe('Create payment transaction action', () => {
  let sandbox: sinon.SinonSandbox;
  let providerPaymentMethodStub: sinon.SinonStub;
  let changeEventStub: sinon.SinonStub;

  beforeEach(() => {
    systemConfig.SKIP_BACKENDS = false;
    systemConfig.DOUBLE_ENTRY_JOURNAL = true;
    sandbox = sinon.createSandbox();
    providerPaymentMethodStub = sandbox.stub(
      createTransactionZai,
      'createTransaction'
    );
    changeEventStub = sandbox
      .stub(transactionChangedEvent, 'changeEvent')
      .resolves();
  });
  afterEach(() => {
    systemConfig.SKIP_BACKENDS = true;
    systemConfig.DOUBLE_ENTRY_JOURNAL = config.get<boolean>(
      'DOUBLE_ENTRY_JOURNAL'
    );
    sandbox.restore();
  });

  describe('v1', () => {
    it('creates missing journals for the parties involved', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: {
            [Op.in]: [seller.id, buyer.id],
          },
        },
      });
      expect(journals.length).to.equal(2);
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.available.amount).to.equal(1000);
          expect(entry.journal.balances.available.credits).to.equal(1000);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.available.amount).to.equal(-1000);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
      }
    });

    it('Emits an error if journal entry fails to commit and rolls back the payment transaction in DB if there is a database failure', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      // Stub journal transaction commit to throw an error
      sandbox
        .stub(JournalTransaction.prototype, 'commit')
        .throws(new Error('Test error'));

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
        });
      } catch (e) {
        err = e;
      }
      expect(err).to.not.be.undefined;
      expect(providerPaymentMethodStub.calledOnce).to.be.false;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(0);
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.be.null;
    });

    it('Emits an error if journal entry fails to commit and rolls back the payment transaction in DB if there are balance constraints applicable on the buyer', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
        balances: {
          pending: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
          available: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
        },
        balanceConstraints: {
          available: {
            gt: 0,
          },
        },
      });

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
        });
      } catch (e) {
        err = e;
      }
      expect(err.statusCode).to.eqls(422);
      expect(providerPaymentMethodStub.calledOnce).to.be.false;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(0);
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.be.null;
    });

    it('Adds pending journal entries for a successful bank transaction', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const bankPaymentMethod = await bankFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        bank_payment_method_id: bankPaymentMethod.id,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
    });

    it('Adds settled journal entries for a successful card transaction', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.available.amount).to.equal(1000);
          expect(entry.journal.balances.available.credits).to.equal(1000);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.available.amount).to.equal(-1000);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
    });

    it('Archives journal entries if payment is failed by the payment provider', async () => {
      providerPaymentMethodStub.rejects();
      const seller = await create();
      const buyer = await create();
      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
          card_payment_method_id: cardPaymentMethod.id,
        });
      } catch (e) {
        err = e;
      }
      expect(err).to.not.be.undefined;
      expect(err.data.message).to.be.string;
      expect(err.statusCode).to.eqls(422);
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.journal.balances.pending.amount).to.equal(0);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.ARCHIVED);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.journal.balances.pending.amount).to.equal(0);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.ARCHIVED);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });

    it('Emits a stream message if payment is successful and journal entries are updated', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.available.amount).to.equal(1000);
          expect(entry.journal.balances.available.credits).to.equal(1000);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.available.amount).to.equal(-1000);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.SETTLED);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });

    it('Emits a stream message if payment is successful but journal entries fail to settle', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      // Stub journal transaction commit to throw an error
      sandbox
        .stub(JournalTransaction.prototype, 'markEntriesSettled')
        .throws(new Error('Test error'));

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });
  });

  describe('v2', () => {
    it('creates missing journals for the parties involved', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        tryAsync: true,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journals = await JournalTransaction.Journal.findAll({
        where: {
          accountId: {
            [Op.in]: [seller.id, buyer.id],
          },
        },
      });
      expect(journals.length).to.equal(2);
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
    });

    it('Emits an error if journal entry fails to commit and rolls back the payment transaction in DB if there is a database failure', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      // Stub journal transaction commit to throw an error
      sandbox
        .stub(JournalTransaction.prototype, 'commit')
        .throws(new Error('Test error'));

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
          tryAsync: true,
        });
      } catch (e) {
        err = e;
      }
      expect(err).to.not.be.undefined;
      expect(providerPaymentMethodStub.calledOnce).to.be.false;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(0);
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.be.null;
    });

    it('Emits an error if journal entry fails to commit and rolls back the payment transaction in DB if there are balance constraints applicable on the buyer', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
        balances: {
          pending: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
          available: {
            amount: 0,
            credits: 0,
            debits: 0,
          },
        },
        balanceConstraints: {
          available: {
            gt: 0,
          },
        },
      });

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
          tryAsync: true,
        });
      } catch (e) {
        err = e;
      }
      expect(err.statusCode).to.eqls(422);
      expect(providerPaymentMethodStub.calledOnce).to.be.false;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(0);
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.be.null;
    });

    it('Adds pending journal entries for a successful bank transaction', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const bankPaymentMethod = await bankFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        bank_payment_method_id: bankPaymentMethod.id,
        tryAsync: true,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
    });

    it('Adds pending journal entries for a successful async card transaction', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        tryAsync: true,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;

      // Test callback to item service
    });

    it('Archives journal entries if payment is failed by the payment provider', async () => {
      providerPaymentMethodStub.rejects();
      const seller = await create();
      const buyer = await create();
      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      let err;
      try {
        await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.PROMISEPAY,
          card_payment_method_id: cardPaymentMethod.id,
          tryAsync: true,
        });
      } catch (e) {
        err = e;
      }
      expect(err).to.not.be.undefined;
      expect(err.data.message).to.be.string;
      expect(err.statusCode).to.eqls(422);
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.journal.balances.pending.amount).to.equal(0);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.ARCHIVED);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.journal.balances.pending.amount).to.equal(0);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.ARCHIVED);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });

    it('Emits a stream message if async payment is successful and journal entries are updated', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        tryAsync: true,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });

    it('Emits a stream message if payment is successful but journal entries fail to settle', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: seller.id,
        normalBalance: NormalBalance.CREDIT,
      });
      await JournalTransaction.Journal.create({
        name: JournalName.WALLET,
        currency: CurrencyCodes.AUD,
        accountId: buyer.id,
        normalBalance: NormalBalance.CREDIT,
      });

      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      // Stub journal transaction commit to throw an error
      sandbox
        .stub(JournalTransaction.prototype, 'markEntriesSettled')
        .throws(new Error('Test error'));

      const transactionId = randomUUID();
      await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        tryAsync: true,
      });
      expect(providerPaymentMethodStub.calledOnce).to.be.true;
      const journalEntries = await JournalTransaction.JournalEntry.findAll({
        where: {
          transactionId,
        },
        include: [
          {
            model: JournalTransaction.Journal,
            as: 'journal',
          },
        ],
      });
      expect(journalEntries.length).to.equal(2);
      for (const entry of journalEntries) {
        expect(entry.amount).to.equal(1000);
        if (!entry.journal) {
          throw new Error();
        }
        if (entry.journal?.accountId === seller.id) {
          expect(entry.direction).to.equal('credit');
          expect(entry.journal.balances.pending.amount).to.equal(1000);
          expect(entry.journal.balances.pending.credits).to.equal(1000);
          expect(entry.journal.balances.pending.debits).to.equal(0);
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
        if (entry.journal?.accountId === buyer.id) {
          expect(entry.direction).to.equal('debit');
          expect(entry.journal.balances.pending.amount).to.equal(-1000);
          expect(entry.journal.balances.pending.credits).to.equal(0);
          expect(entry.journal.balances.pending.debits).to.equal(1000);
          expect(entry.journal.balances.available.amount).to.equal(0);
          expect(entry.journal.balances.available.credits).to.equal(0);
          expect(entry.journal.balances.available.debits).to.equal(0);
          expect(entry.status).to.equal(JournalEntryStatus.PENDING);
        }
      }
      const transaction = await Transaction.findByPk(transactionId);
      expect(transaction).to.not.be.null;
      expect(changeEventStub.calledOnce).to.be.true;
    });
  });

  describe('isZaiAsync context field', () => {
    it('sets isZaiAsync to true for async transactions with tryAsync and card payment', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      const result = await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        tryAsync: true,
      });

      expect(result.transaction.context?.isZaiAsync).to.be.true;
    });

    it('sets isZaiAsync to true for async transactions with tryAsync and useWalletAccount', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();

      const transactionId = randomUUID();
      const result = await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        tryAsync: true,
        useWalletAccount: true,
      });

      expect(result.transaction.context?.isZaiAsync).to.be.true;
    });

    it('sets isZaiAsync to false for transactions without tryAsync', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      const result = await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        tryAsync: false,
      });

      expect(result.transaction.context?.isZaiAsync).to.be.false;
    });

    it('sets isZaiAsync to false for transactions with non-PROMISEPAY backend', async () => {
      // Temporarily skip backends since we're testing with a non-stubbed backend
      const originalSkipBackends = systemConfig.SKIP_BACKENDS;
      systemConfig.SKIP_BACKENDS = true;

      try {
        providerPaymentMethodStub.resolves();
        const seller = await create();
        const buyer = await create();
        const cardPaymentMethod = await cardFixture({
          user_id: buyer.id,
        });

        const transactionId = randomUUID();
        const result = await createPaymentTransaction({
          id: transactionId,
          seller_id: seller.id,
          buyer_id: buyer.id,
          amount: 1000,
          currency: CurrencyCodes.AUD,
          description: 'Test',
          name: 'Test',
          state: states.pending,
          backend: Backend.STRIPE,
          card_payment_method_id: cardPaymentMethod.id,
          tryAsync: true,
        });

        expect(result.transaction.context?.isZaiAsync).to.be.false;
      } finally {
        systemConfig.SKIP_BACKENDS = originalSkipBackends;
      }
    });

    it('sets isZaiAsync to false for transactions with bank payment method', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const bankPaymentMethod = await bankFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      const result = await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        bank_payment_method_id: bankPaymentMethod.id,
        tryAsync: true,
      });

      expect(result.transaction.context?.isZaiAsync).to.be.false;
    });

    it('sets isZaiAsync to false for transactions with both card and bank payment methods', async () => {
      providerPaymentMethodStub.resolves();
      const seller = await create();
      const buyer = await create();
      const cardPaymentMethod = await cardFixture({
        user_id: buyer.id,
      });
      const bankPaymentMethod = await bankFixture({
        user_id: buyer.id,
      });

      const transactionId = randomUUID();
      const result = await createPaymentTransaction({
        id: transactionId,
        seller_id: seller.id,
        buyer_id: buyer.id,
        amount: 1000,
        currency: CurrencyCodes.AUD,
        description: 'Test',
        name: 'Test',
        state: states.pending,
        backend: Backend.PROMISEPAY,
        card_payment_method_id: cardPaymentMethod.id,
        bank_payment_method_id: bankPaymentMethod.id,
        tryAsync: true,
      });

      expect(result.transaction.context?.isZaiAsync).to.be.false;
    });
  });
});
