import { expect } from 'chai';
import { v4 as uuid } from 'uuid';
import HttpError from 'httperrors';
import { create as createUser } from '../../fixtures/user';
import { create as createUserSetting } from '../../fixtures/user_setting';
import { updateUserSettings } from '../../../src/actions/users/update_user_settings';

describe('updateUserSettings', () => {
  const mockConfigurations = {
    settlementDelay: 5,
    manualDisbursement: true,
    supplier: false,
    netSettlement: true,
    reference: 123,
    settlementRate: 0.5,
    weeklySettlement: true,
    companyDescriptor: 'Test Company',
  };

  it('should throw 404 error when user is not found', async () => {
    const nonExistentUserId = uuid(); // Generate a valid UUID that won't exist in the database

    try {
      await updateUserSettings(nonExistentUserId, mockConfigurations);
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect(error).to.be.instanceOf(HttpError);
      expect(error.statusCode).to.equal(404);
      expect(error.data.message).to.equal('User not found');
    }
  });

  it('should create new user settings when none exist', async () => {
    const user = await createUser();
    const result = await updateUserSettings(user.id, mockConfigurations);

    expect(result).to.have.property('userId', user.id);
    expect(result).to.have.property(
      'settlementDelayDays',
      mockConfigurations.settlementDelay
    );
    expect(result).to.have.property(
      'manualDisbursement',
      mockConfigurations.manualDisbursement
    );
    expect(result).to.have.property('isSupplier', mockConfigurations.supplier);
    expect(result).to.have.property(
      'netSettlement',
      mockConfigurations.netSettlement
    );
    expect(result).to.have.property('reference', mockConfigurations.reference);
    expect(result).to.have.property(
      'settlementRate',
      String(mockConfigurations.settlementRate)
    );
    expect(result).to.have.property(
      'weeklySettlement',
      mockConfigurations.weeklySettlement
    );
    expect(result).to.have.property(
      'companyDescriptor',
      mockConfigurations.companyDescriptor
    );
  });

  it('should update existing user settings', async () => {
    const user = await createUser();
    const existingSetting = await createUserSetting({ userId: user.id });

    const result = await updateUserSettings(user.id, mockConfigurations);

    expect(result).to.have.property('id', existingSetting.id);
    expect(result).to.have.property('userId', user.id);
    expect(result).to.have.property(
      'settlementDelayDays',
      mockConfigurations.settlementDelay
    );
    expect(result).to.have.property(
      'manualDisbursement',
      mockConfigurations.manualDisbursement
    );
    expect(result).to.have.property('isSupplier', mockConfigurations.supplier);
    expect(result).to.have.property(
      'netSettlement',
      mockConfigurations.netSettlement
    );
    expect(result).to.have.property('reference', mockConfigurations.reference);
    expect(result).to.have.property(
      'settlementRate',
      String(mockConfigurations.settlementRate)
    );
    expect(result).to.have.property(
      'weeklySettlement',
      mockConfigurations.weeklySettlement
    );
    expect(result).to.have.property(
      'companyDescriptor',
      mockConfigurations.companyDescriptor
    );
  });
});
