import sinon from 'sinon';
import { expect } from 'chai';
import moment from 'moment-timezone';
import business from 'moment-business';
import { transactionCreateAction } from '../../src/actions/transaction_create_action';
import * as backends from '../../src/lib/backends';
import transactionFixture from '../fixtures/transaction';
import { states } from '../../src/models/transaction';
import { User, UserSetting } from '../../src/models';

describe('transaction create action', () => {
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sandbox.stub(backends, 'getBackend').resolves({
      createTransaction: () => {
        // Empty
      },
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should error if passed a floating point amount', async () => {
    try {
      const fixture = await transactionFixture(false, states.completed, {
        amount: 100.56,
      });

      await transactionCreateAction(fixture.data, {
        tryAsync: true,
      });

      expect.fail();
    } catch (err) {
      /* empty */
    }
  });

  it('should convert fee from rounded dollar decimal value to cents integer', async () => {
    const fixture = await transactionFixture(false, states.completed, {
      amount: 156,
      type: 'mastercard',
    });

    const result = await transactionCreateAction(fixture.data, {
      tryAsync: true,
    });

    expect(result.details.feeCents).eq(16);
  });

  it('should apply settlementDelayDays from user settings to release timestamp', async () => {
    // Set a fixed time for consistent testing in Sydney timezone
    const fixedTime = moment()
      .tz('Australia/Sydney')
      .set({ hour: 10, minute: 0, second: 0, millisecond: 0 });
    sandbox.useFakeTimers(fixedTime.valueOf());

    const fixture = await transactionFixture(false, states.completed, {
      type: 'mastercard',
    });

    const seller = await User.findByPk(fixture.data.seller_id);
    if (!seller) throw new Error('Seller not found');

    await UserSetting.create({
      userId: seller.id,
      settlementDelayDays: 3,
    });

    const result = await transactionCreateAction(fixture.data, {
      tryAsync: true,
    });

    // Release should be 3 business days from the fixed time at 13:30
    // Sydney time (cutoff time for all releases)
    const expectedRelease = business
      .addWeekDays(fixedTime.clone(), 3)
      .set({ hour: 13, minute: 30, second: 0, millisecond: 0 })
      .toISOString();

    const actualTime = moment(result.transaction.release_at);
    const expectedTime = moment(expectedRelease);

    expect(actualTime.valueOf()).to.equal(expectedTime.valueOf());
  });

  it('should not add delay when seller has userSettings but no settlementDelayDays', async () => {
    // Set a fixed time for consistent testing in Sydney timezone
    const fixedTime = moment()
      .tz('Australia/Sydney')
      .set({ hour: 10, minute: 0, second: 0, millisecond: 0 });
    sandbox.useFakeTimers(fixedTime.valueOf());

    const fixture = await transactionFixture(false, states.completed, {
      type: 'mastercard',
    });

    const seller = await User.findByPk(fixture.data.seller_id);
    if (!seller) throw new Error('Seller not found');

    // Create userSettings without settlementDelayDays
    await UserSetting.create({
      userId: seller.id,
      manualDisbursement: true,
    });

    const result = await transactionCreateAction(fixture.data, {
      tryAsync: true,
    });

    // Release should be same day at 13:30 Sydney time (cutoff time for all releases)
    const expectedRelease = fixedTime
      .clone()
      .set({ hour: 13, minute: 30, second: 0, millisecond: 0 })
      .toISOString();

    const actualTime = moment(result.transaction.release_at);
    const expectedTime = moment(expectedRelease);

    expect(actualTime.valueOf()).to.equal(expectedTime.valueOf());
  });

  it('should not add delay when seller has no userSettings', async () => {
    // Set a fixed time for consistent testing in Sydney timezone
    const fixedTime = moment()
      .tz('Australia/Sydney')
      .set({ hour: 10, minute: 0, second: 0, millisecond: 0 });
    sandbox.useFakeTimers(fixedTime.valueOf());

    const fixture = await transactionFixture(false, states.completed, {
      type: 'mastercard',
    });

    const seller = await User.findByPk(fixture.data.seller_id);
    if (!seller) throw new Error('Seller not found');

    // Ensure no userSettings exist
    await UserSetting.destroy({ where: { userId: seller.id } });

    const result = await transactionCreateAction(fixture.data, {
      tryAsync: true,
    });

    // Release should be same day at 13:30 Sydney time (cutoff time for all releases)
    const expectedRelease = fixedTime
      .clone()
      .set({ hour: 13, minute: 30, second: 0, millisecond: 0 })
      .toISOString();

    const actualTime = moment(result.transaction.release_at);
    const expectedTime = moment(expectedRelease);

    expect(actualTime.valueOf()).to.equal(expectedTime.valueOf());
  });
});
