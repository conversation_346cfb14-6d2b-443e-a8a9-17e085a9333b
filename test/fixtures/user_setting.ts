import { randomUUID } from 'crypto';
import { UserSetting } from '../../src/models';
import {
  UserSettingInstance,
  UserSettingAttributes,
} from '../../src/models/user_setting';

export const userSettingFixtureData = (
  data?: Partial<UserSettingAttributes>
): UserSettingAttributes => {
  const values: UserSettingAttributes = {
    settlementDelayDays: 1,
    manualDisbursement: false,
    isSupplier: true,
    netSettlement: false,
    settlementRate: '1.00',
    weeklySettlement: false,
    companyDescriptor: 'test',
    reference: 123,
    userId: randomUUID(),
    ...data,
  };
  return values;
};

export const factory = async (attrs = {}) => {
  const data = userSettingFixtureData(attrs);
  return {
    data,
    instance: async () => {
      const instance = await UserSetting.create({
        ...data,
        ...attrs,
      });
      return instance.get();
    },
  };
};

export const create = async (values = {}): Promise<UserSettingInstance> => {
  const data = userSettingFixtureData();
  const instance = await UserSetting.create({ ...data, ...values });
  return instance;
};

export default factory;
