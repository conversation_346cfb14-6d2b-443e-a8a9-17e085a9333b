import { randomUUID } from 'crypto';

/* eslint-disable camelcase */
import faker from 'faker';
import { BankPaymentMethod } from '../../src/models';
import bankDetailsFixtureFactory from './bank_details';
import UserFactory from './user';

const getData = (data = {}) => ({
  backend: 'promisepay',
  account_name: faker.name.findName(),
  routing_number: '123456',
  account_number: '********',
  country: 'AUS',
  bank_name: 'CBA',
  currency: 'AUD',
  holder_type: 'business',
  account_type: 'savings',
  is_settlement_account: true,
  backend_id: randomUUID(),
  ...data,
});

export const factory = async passedUser => {
  const user = passedUser || (await (await UserFactory()).instance());
  const bankDetail = await (await bankDetailsFixtureFactory()).instance();
  const data = getData({ user_id: user.id, bank_name: bankDetail.name });

  return {
    data,
    instance: async (input = data) => {
      const instance = await BankPaymentMethod.create(input);
      return {
        ...instance.dataValues,
        ordermentum_id: user.ordermentum_id,
      };
    },
  };
};

export const bankFixture = async (data = {}) => {
  const params = getData(data);
  const instance = await BankPaymentMethod.create(params);
  return instance;
};

export default factory;
