import { v7 as uuid } from 'uuid';
import {
  UserSaleOutput,
  JournalUserSaleDetailsFn,
  UserRefundOutput,
  JournalUserRefundDetailsFn,
} from '../../../src/types/journal_operation_types';
import { states } from '../../../src/models/transaction';

/**
 *
 */
export function testUserSaleFn(
  overrides: Partial<UserSaleOutput> &
    Pick<UserSaleOutput, 'totalCents' | 'feeCents'>
): JournalUserSaleDetailsFn<object> {
  return () =>
    Promise.resolve({
      returned: { test: 123 },
      details: {
        buyerId: uuid(),
        buyerText: 'buyer text',
        sellerId: uuid(),
        sellerText: 'seller text',
        fundedByPrinciple: false,
        walletFundTxId: overrides?.walletFundedCents ? uuid() : undefined,
        walletFundedCents: 0,
        purchaseTxId: uuid(),
        purchaseTxState: states.completed,
        ...overrides,
      },
    });
}

/**
 *
 */
export function testUserRefundFn(
  overrides: Partial<UserRefundOutput> & Pick<UserRefundOutput, 'amountCents'>
): JournalUserRefundDetailsFn<object> {
  return () =>
    Promise.resolve({
      returned: { test: 123 },
      details: {
        refundedToId: uuid(),
        refundedFromId: uuid(),
        refundedFromText: 'refunded from text',
        refundedToText: 'refunded to text',
        refundTxId: uuid(),
        ...overrides,
      },
    });
}
