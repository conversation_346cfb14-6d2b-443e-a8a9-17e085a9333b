const faker = require('faker');
const { v4: uuid } = require('uuid');

const fakeMobile = num => {
  if (num.length < 12) return fakeMobile(num + Math.floor(Math.random() * 9));
  return num;
};

module.exports = {
  user: async () => {
    const name = faker.name.findName();
    const names = name.split(' ');

    return {
      id: uuid(),
      ordermentum_id: uuid(),
      name: {
        first: names[0],
        last: names[1],
      },
      settlement_webhook: faker.internet.url(),
      address: {
        line_1: faker.name.findName(),
        city: faker.name.findName(),
        state: faker.name.findName(),
        postcode: '2000',
        country: 'AUS',
      },
      email: faker.internet.email(),
      mobile_number: fakeMobile('+61'),
      dob: '1917-01-01T00:00:00.000Z',
      company: {
        name: faker.name.findName(),
        legal_name: faker.name.findName(),
        tax_number: faker.name.findName(),
        address: {
          line_1: faker.name.findName(),
          city: faker.name.findName(),
          state: faker.name.findName(),
          postcode: '2000',
          country: 'AUS',
        },
      },
    };
  },
};
