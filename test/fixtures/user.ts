import faker from 'faker';
import { v4 as uuid } from 'uuid';
import { User } from '../../src/models';
import { UserInstance, UserAttributes } from '../../src/models/user';
import { Backend } from '../../src/models/transaction';

const fakeMobile = num => {
  if (num.length < 12) return fakeMobile(num + Math.floor(Math.random() * 9));
  return num;
};

export const userFixtureData = (data?: Partial<UserAttributes>) => {
  const name = faker.name.findName();
  const names = name.split(' ');

  const values: UserAttributes = {
    first_name: names[0],
    last_name: names[1],
    ordermentum_id: uuid(),
    external_id: uuid().toString(),
    stripe_customer_id: uuid(),
    email: `${names[0]}${names[1]}@example.com`,
    address_line_1: faker.name.findName(),
    city: faker.name.findName(),
    state: faker.name.findName(),
    postcode: '2000',
    dd_settlement_delay_hours: 64,
    country: 'AUS',
    mobile_number: fakeMobile('+61'),
    dob: '1981-02-21T00:00:00.000Z',
    company_name: faker.name.findName(),
    company_tax_number: faker.name.findName(),
    company_address_line_1: faker.name.findName(),
    address_line_2: faker.name.findName(),
    settlement_webhook: '',
    batch_settlement_payments: false,
    status: 'active',
    company_address_line_2: faker.name.findName(),
    company_city: faker.name.findName(),
    company_state: faker.name.findName(),
    company_postcode: '2000',
    company_country: 'AUS',
    backend: Backend.PROMISEPAY,
    configuration: {
      netSettlement: true,
      manualDisbursement: true,
      rates: { mastercard: 0.1, visa: 0.1, direct: 0.1, amex: 0.1 },
    },
    company_legal_name: faker.name.findName(),
    ...data,
  };
  return values;
};

export const factory = async (attrs = {}) => {
  const data = userFixtureData();
  return {
    data,
    instance: async () => {
      const instance = await User.create({
        ...data,
        ...attrs,
      });
      return instance.get();
    },
  };
};

export const create = async (values = {}): Promise<UserInstance> => {
  const data = userFixtureData();
  const instance = await User.create({ ...data, ...values });
  return instance;
};

export default factory;
