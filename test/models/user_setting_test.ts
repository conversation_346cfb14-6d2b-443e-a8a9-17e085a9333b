import { expect } from 'chai';
import { User, UserSetting } from '../../src/models';
import userFixtureFactory from '../fixtures/user';
import userSettingFixtureFactory from '../fixtures/user_setting';

describe('UserSetting model', () => {
  let userFixture;
  let userSettingFixture;
  let user;
  let userSetting;

  beforeEach(async () => {
    // create a user
    userFixture = (await userFixtureFactory()).data;
    user = await User.create(userFixture);

    // create a user setting
    userSettingFixture = (
      await userSettingFixtureFactory({
        userId: user.id,
      })
    ).data;
    userSetting = await UserSetting.create(userSettingFixture);
  });

  it('should create user setting', async () => {
    expect(userSetting.userId).to.equal(user.id);
    expect(userSetting.settlementDelayDays).to.equal(
      userSettingFixture.settlementDelayDays
    );
    expect(userSetting.manualDisbursement).to.equal(
      userSettingFixture.manualDisbursement
    );
    expect(userSetting.isSupplier).to.equal(userSettingFixture.isSupplier);
    expect(userSetting.netSettlement).to.equal(
      userSettingFixture.netSettlement
    );
    expect(userSetting.settlementRate).to.equal(
      userSettingFixture.settlementRate
    );
    expect(userSetting.weeklySettlement).to.equal(
      userSettingFixture.weeklySettlement
    );
    expect(userSetting.companyDescriptor).to.equal(
      userSettingFixture.companyDescriptor
    );
    expect(userSetting.reference).to.equal(userSettingFixture.reference);
  });

  it('should allow null values', async () => {
    await UserSetting.update(
      {
        settlementDelayDays: null,
        reference: null,
        settlementRate: null,
        companyDescriptor: null,
      },
      { where: { id: userSetting.id } }
    );

    const updatedSetting = await UserSetting.findByPk(userSetting.id);
    expect(updatedSetting).to.not.be.null;
    expect(updatedSetting?.settlementDelayDays).to.equal(null);
    expect(updatedSetting?.reference).to.equal(null);
    expect(updatedSetting?.settlementRate).to.equal(null);
    expect(updatedSetting?.companyDescriptor).to.equal(null);
  });

  it('should update user setting', async () => {
    const newSettlementDelayDays = 2;
    await UserSetting.update(
      {
        settlementDelayDays: newSettlementDelayDays,
      },
      { where: { id: userSetting.id } }
    );

    const updatedSetting = await UserSetting.findByPk(userSetting.id);
    expect(updatedSetting).to.not.be.null;
    expect(updatedSetting?.settlementDelayDays).to.equal(
      newSettlementDelayDays
    );
  });

  it('should fetch user with user setting', async () => {
    const userWithSetting = await User.findByPk(user.id, {
      include: [{ model: UserSetting, as: 'userSetting' }],
    });
    expect(userWithSetting).to.not.be.null;
    expect(userWithSetting?.id).to.equal(user.id);
    expect(userWithSetting?.userSetting?.id).to.equal(userSetting.id);
    expect(userWithSetting?.userSetting?.settlementDelayDays).to.equal(
      userSettingFixture.settlementDelayDays
    );
    expect(userWithSetting?.userSetting?.manualDisbursement).to.equal(
      userSettingFixture.manualDisbursement
    );
    expect(userWithSetting?.userSetting?.isSupplier).to.equal(
      userSettingFixture.isSupplier
    );
    expect(userWithSetting?.userSetting?.netSettlement).to.equal(
      userSettingFixture.netSettlement
    );
    expect(userWithSetting?.userSetting?.settlementRate).to.equal(
      userSettingFixture.settlementRate
    );
    expect(userWithSetting?.userSetting?.weeklySettlement).to.equal(
      userSettingFixture.weeklySettlement
    );
    expect(userWithSetting?.userSetting?.companyDescriptor).to.equal(
      userSettingFixture.companyDescriptor
    );
    expect(userWithSetting?.userSetting?.reference).to.equal(
      userSettingFixture.reference
    );
  });
});
