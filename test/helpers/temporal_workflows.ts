import { upfrontPaymentWorkflow } from '../../src/ng/workflow/definitions/upfront_payment.workflow';
import { promotionalPaymentWorkflow } from '../../src/ng/workflow/definitions/promotional_payment.workflow';
import { walletTransferWorkflow } from '../../src/ng/workflow/definitions/wallet_transfer.workflow';
import { refundWorkflow } from '../../src/ng/workflow/definitions/refund.workflow';
import { makeZaiPaymentWorkflow } from '../../src/ng/workflow/definitions/make_zai_payment.workflow';

export {
  upfrontPaymentWorkflow,
  promotionalPaymentWorkflow,
  walletTransferWorkflow,
  refundWorkflow,
  makeZaiPaymentWorkflow,
};
