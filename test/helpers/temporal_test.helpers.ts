import {
  Worker,
  WorkerOptions,
  LogLevel,
  NativeConnection,
  DefaultLogger,
  Runtime,
} from '@temporalio/worker';

/**
 * A logger that suppresses all logs.
 */
export const silentLogger = new DefaultLogger('ERROR', () => {
  // Suppress all logs during tests
});

/**
 * Setup the temporal logger to suppress all logs during tests.
 * This is useful to reduce test log noise.
 * To enable log silencing, set the LOG_LEVEL environment variable to 'ERROR'.
 *
 * @example
 * ```
 * LOG_LEVEL=ERROR yarn spec
 * ```
 */
export function setupTemporalTestLogger() {
  const logLevel = process.env.LOG_LEVEL
    ? (process.env.LOG_LEVEL.toUpperCase() as LogLevel)
    : 'INFO';

  Runtime.install({
    logger: logLevel === 'ERROR' ? silentLogger : undefined,
  });
}

/**
 * Enhanced createTestWorker that supports multiple workflow definitions.
 * This allows testing workflows that use child workflows.
 */
export async function createTestWorker(options: {
  connection: NativeConnection;
  taskQueue: string;
  activities: object;
}): Promise<Worker> {
  // Create worker with the bundled workflows
  const workerOptions: WorkerOptions = {
    connection: options.connection,
    taskQueue: options.taskQueue,
    workflowsPath: require.resolve('./temporal_workflows'),
    activities: {
      ...options.activities,
      retry: {
        maximumAttempts: 1,
      },
    },
  };

  const worker = await Worker.create(workerOptions);

  return worker;
}
