import sinon from 'sinon';
import nock from 'nock';
import { expect } from 'chai';
import faker from 'faker';
import { client } from '../../src/lib/backends/stripe';
import bankPaymentMethods from '../../src/controllers/bank_payment_methods';
import { BankPaymentMethod, User } from '../../src/models';
import paymentMethodFixture from '../fixtures/http/payment_method';
import userFixtureFactory from '../fixtures/user';
import { UserInstance } from '../../src/models/user';
import { systemConfig } from '../../src/system_config';
import * as emailNotification from '../../src/actions/notifications/send_bank_account_changes_email';
import * as smsNotification from '../../src/actions/notifications/send_sms';
import { VALID_STRIPE_COUNTRY_CODES } from '../../src/validations/constants';

describe('bank payment method controller', () => {
  let fixture;
  let sandbox;
  let userInstance: UserInstance;

  beforeEach('generate fixture', async () => {
    sandbox = sinon.createSandbox();

    const user = (await userFixtureFactory()).data;

    const params = {
      ...user,
    };
    userInstance = await User.create(params);

    fixture = await paymentMethodFixture.bank(userInstance);
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it('should not allow multiple settlement methods for the same user', async () => {
    // first bank payment method
    fixture.is_settlement_account = true;
    await bankPaymentMethods.create(fixture);

    // second bank payment method
    fixture = await paymentMethodFixture.bank(userInstance);
    fixture.is_settlement_account = true;
    await bankPaymentMethods.create(fixture);

    const methods = await bankPaymentMethods.all({
      where: {
        is_settlement_account: true,
        user_id: fixture.user_id,
      },
    });
    expect(methods.length).to.equal(1);
  });

  it('should reject a settlement method for a promisepay user that has no company information', async () => {
    const user = (await userFixtureFactory()).data;

    const params = {
      ...user,
      company_name: undefined,
    };
    userInstance = await User.create(params);

    fixture = await paymentMethodFixture.bank(userInstance);

    fixture.is_settlement_account = true;

    let thrown = false;
    try {
      await bankPaymentMethods.create(fixture);
    } catch (e) {
      thrown = true;
    }
    expect(thrown).to.equal(true);
  });

  describe('stripe setup', () => {
    let customerCreateStub: sinon.SinonStub;
    let setupIntentCreateStub: sinon.SinonStub;

    beforeEach(async () => {
      systemConfig.SKIP_BACKENDS = false;
      const user = (await userFixtureFactory()).data;

      const params = {
        ...user,
        stripe_customer_id: '',
      };
      userInstance = await User.create(params);

      fixture = await paymentMethodFixture.bank(userInstance);
      fixture.backend = 'stripe';
      fixture.data.routing_number = '000000'; // test BSB number
      fixture.data.account_number = '*********';
      fixture.ip_address = faker.internet.ip();
      fixture.user_agent = faker.internet.userAgent();
      customerCreateStub = sandbox
        .stub(client.customers, 'create')
        .resolves({ id: 'c_123' });
      setupIntentCreateStub = sandbox
        .stub(client.setupIntents, 'create')
        .resolves({
          id: 'si_123',
        });
    });

    afterEach(() => {
      systemConfig.SKIP_BACKENDS = true;
    });

    it('should accept stripe as backend and create payment method on stripe', async () => {
      const createPaymentMethodScope = nock('https://api.stripe.com')
        .post('/v1/payment_methods', body => {
          if (body['billing_details[address][country]']) {
            const countryCode = body['billing_details[address][country]'];
            expect(VALID_STRIPE_COUNTRY_CODES.includes(countryCode)).to.be.true;
          }

          return true;
        })
        .reply(200, {
          id: 'pm_123',
        });
      let thrown = false;
      try {
        fixture.data.country = 'AU';
        await bankPaymentMethods.create(fixture);
      } catch (e) {
        thrown = true;
      }
      expect(thrown).to.equal(false);
      expect(customerCreateStub.called).to.be.true;
      expect(setupIntentCreateStub.called).to.be.true;
      expect(createPaymentMethodScope.isDone()).to.be.true;
    });

    it('should update bankPaymentMethod and stripe customer', async () => {
      const firstPaymentMethodCreateScope = nock('https://api.stripe.com')
        .post('/v1/payment_methods')
        .reply(200, {
          id: 'pm_123',
        });
      const createdBankMethod = await bankPaymentMethods.create(fixture);

      const bankPaymentMethodInstance = await BankPaymentMethod.findByPk(
        createdBankMethod.id
      );

      expect(bankPaymentMethodInstance).to.not.be.null;
      const isPaymentMethodCreated = firstPaymentMethodCreateScope.isDone();
      expect(isPaymentMethodCreated).to.be.true;

      const bankMethodParams = {
        user_id: bankPaymentMethodInstance?.user_id,
        is_settlement_account: bankPaymentMethodInstance?.is_settlement_account,
        backend: 'stripe',
        data: {
          account_name: 'Sierra Stones',
          account_number: '*********',
          account_type: bankPaymentMethodInstance?.account_type,
          bank_name: bankPaymentMethodInstance?.bank_name,
          country: bankPaymentMethodInstance?.country,
          currency: bankPaymentMethodInstance?.currency,
          holder_type: bankPaymentMethodInstance?.holder_type,
          routing_number: bankPaymentMethodInstance?.routing_number,
          type: 'bank',
        },
      };

      const last4Digits = bankMethodParams.data.account_number.substring(
        bankMethodParams.data.account_number.length - 4
      );

      const customerId = 'customer_123';
      const getSetupIntentScope = nock('https://api.stripe.com')
        .get(`/v1/setup_intents/${bankPaymentMethodInstance?.backend_id}`)
        .reply(200, {
          id: bankPaymentMethodInstance?.backend_id,
          customer: customerId,
        });

      const getPaymentMethodScope = nock('https://api.stripe.com')
        .get(`/v1/customers/${customerId}/payment_methods`)
        .query({ type: 'au_becs_debit' })
        .reply(200, {
          object: 'list',
          data: [
            {
              id: 'pm_1',
              au_becs_debit: {
                bsb_number: bankPaymentMethodInstance?.routing_number,
                last4: last4Digits,
              },
            },
            {
              id: 'pm_2',
              au_becs_debit: {
                bsb_number: '654321',
                last4: '5678',
              },
            },
          ],
        });

      const paymentMethodId = 'payment_method_123';
      const createPaymentMethodScope = nock('https://api.stripe.com')
        .post('/v1/payment_methods')
        .reply(200, {
          id: paymentMethodId,
        });

      const updateSetupIntent = nock('https://api.stripe.com')
        .post(`/v1/setup_intents/${bankPaymentMethodInstance?.backend_id}`, {
          payment_method: paymentMethodId,
        })
        .reply(200, {
          id: bankPaymentMethodInstance?.backend_id,
          object: 'setup_intent',
          payment_method: paymentMethodId,
          status: 'succeeded',
        });

      let thrown = false;
      try {
        await bankPaymentMethods.update(
          bankPaymentMethodInstance?.id,
          bankMethodParams
        );
      } catch (e) {
        thrown = true;
      }

      await bankPaymentMethodInstance?.reload();

      expect(thrown).to.equal(false);
      const isSetupIntentFetched = getSetupIntentScope.isDone();
      expect(isSetupIntentFetched).to.equal(true);
      const isPaymentMethodFetched = getPaymentMethodScope.isDone();
      expect(isPaymentMethodFetched).to.equal(true);
      const isSecondPaymentMethodCreated = createPaymentMethodScope.isDone();
      expect(isSecondPaymentMethodCreated).to.equal(true);
      const isUpdateSetupIntent = updateSetupIntent.isDone();
      expect(isUpdateSetupIntent).to.equal(true);
    });
  });

  // Only checks for function calls here. There is a separate test for the actual
  // email sending logic in the email action tests.
  describe('Sending email changes', () => {
    describe('create', () => {
      it('should send email when creating new bank account', async () => {
        const sendBankAccountChangesEmailStub = sandbox
          .stub(emailNotification, 'sendBankAccountChangesEmail')
          .resolves();
        let thrown = false;
        try {
          await bankPaymentMethods.create(fixture);
        } catch (e) {
          thrown = true;
        }

        expect(sendBankAccountChangesEmailStub.called).to.be.true;
      });
    });

    describe('update', () => {
      let sendBankAccountChangesEmailStub: sinon.SinonStub;
      // BankPaymentMethodInstance view model type
      let bankPaymentMethodModel: Record<string, any>;

      beforeEach(async () => {
        bankPaymentMethodModel = await bankPaymentMethods.create(fixture);
        sendBankAccountChangesEmailStub = sandbox
          .stub(emailNotification, 'sendBankAccountChangesEmail')
          .resolves();
      });

      it('should send email when updating bank account', async () => {
        const bankMethodParams = {
          user_id: bankPaymentMethodModel?.userId,
          is_settlement_account: bankPaymentMethodModel?.isSettlementAccount,
          backend: 'promisepay',
          data: {
            account_name: 'New account name',
            account_number: '*********',
            account_type: bankPaymentMethodModel?.data.account_type,
            bank_name: bankPaymentMethodModel?.data.bank_name,
            country: bankPaymentMethodModel?.data.country,
            currency: bankPaymentMethodModel?.data.currency,
            holder_type: bankPaymentMethodModel?.data.holder_type,
            routing_number: bankPaymentMethodModel?.data.routing_number,
            type: 'bank',
          },
        };

        let thrown = false;
        try {
          await bankPaymentMethods.update(
            bankPaymentMethodModel?.id,
            bankMethodParams
          );
        } catch (e) {
          thrown = true;
        }

        expect(thrown).to.equal(false);
        expect(sendBankAccountChangesEmailStub.called).to.be.true;
      });

      it('should not send email when updating bank account without detail changes', async () => {
        const bankMethodParams = {
          user_id: bankPaymentMethodModel?.userId,
          is_settlement_account: bankPaymentMethodModel?.isSettlementAccount,
          backend: 'promisepay',
          data: {
            account_name: bankPaymentMethodModel.data?.account_name,
            account_number: bankPaymentMethodModel?.data.account_number,
            account_type: bankPaymentMethodModel?.data.account_type,
            bank_name: bankPaymentMethodModel?.data.bank_name,
            country: bankPaymentMethodModel?.data.country,
            currency: bankPaymentMethodModel?.data.currency,
            holder_type: bankPaymentMethodModel?.data.holder_type,
            routing_number: bankPaymentMethodModel?.data.routing_number,
            type: 'bank',
          },
        };

        let thrown = false;
        try {
          await bankPaymentMethods.update(
            bankPaymentMethodModel?.id,
            bankMethodParams
          );
        } catch (e) {
          thrown = true;
        }

        expect(thrown).to.equal(false);
        expect(sendBankAccountChangesEmailStub.called).to.be.false;
      });
    });
  });

  context('Sending sms changes', () => {
    let userWithInvalidMobileNumberFixture;

    beforeEach(async () => {
      const userWithInvalidMobileNumber = (await userFixtureFactory()).data;

      const userWithInvalidMobileNumberInstance = await User.create({
        ...userWithInvalidMobileNumber,
        mobile_number: '+<EMAIL>',
      });

      userWithInvalidMobileNumberFixture = await paymentMethodFixture.bank(
        userWithInvalidMobileNumberInstance
      );
    });

    context('create', () => {
      it('should send sms when creating new bank account', async () => {
        const sendBankAccountChangesSmsStub = sandbox
          .stub(smsNotification, 'sendTemplateMessage')
          .resolves();
        let thrown = false;
        try {
          await bankPaymentMethods.create(fixture);
        } catch (e) {
          thrown = true;
        }

        expect(thrown).to.equal(false);
        expect(sendBankAccountChangesSmsStub.called).to.be.true;
      });

      it('should not send sms if user has invalid mobile number', async () => {
        const sendTemplateMessageStub = sandbox
          .stub(smsNotification, 'sendTemplateMessage')
          .resolves();
        await bankPaymentMethods.create(userWithInvalidMobileNumberFixture);

        expect(sendTemplateMessageStub.called).to.equal(false);
      });
    });

    context('update', () => {
      let sendTemplateMessageStub: sinon.SinonStub;
      // BankPaymentMethodInstance view model type
      // skipcq: JS-0323 - test file only
      let bankPaymentMethodModel: Record<string, any>;

      beforeEach(async () => {
        bankPaymentMethodModel = await bankPaymentMethods.create(fixture);
        sendTemplateMessageStub = sandbox
          .stub(smsNotification, 'sendTemplateMessage')
          .resolves();
      });

      it('should send sms when updating bank account', async () => {
        const bankMethodParams = {
          user_id: bankPaymentMethodModel?.userId,
          is_settlement_account: bankPaymentMethodModel?.isSettlementAccount,
          backend: 'promisepay',
          data: {
            account_name: 'New account name',
            account_number: '*********',
            account_type: bankPaymentMethodModel?.data.account_type,
            bank_name: bankPaymentMethodModel?.data.bank_name,
            country: bankPaymentMethodModel?.data.country,
            currency: bankPaymentMethodModel?.data.currency,
            holder_type: bankPaymentMethodModel?.data.holder_type,
            routing_number: bankPaymentMethodModel?.data.routing_number,
            type: 'bank',
          },
        };

        let thrown = false;
        try {
          await bankPaymentMethods.update(
            bankPaymentMethodModel?.id,
            bankMethodParams
          );
        } catch (e) {
          thrown = true;
        }

        expect(thrown).to.equal(false);
        expect(sendTemplateMessageStub.called).to.be.true;
      });

      it('should not send sms when updating bank account without detail changes', async () => {
        const bankMethodParams = {
          user_id: bankPaymentMethodModel?.userId,
          is_settlement_account: bankPaymentMethodModel?.isSettlementAccount,
          backend: 'promisepay',
          data: {
            account_name: bankPaymentMethodModel.data?.account_name,
            account_number: bankPaymentMethodModel?.data.account_number,
            account_type: bankPaymentMethodModel?.data.account_type,
            bank_name: bankPaymentMethodModel?.data.bank_name,
            country: bankPaymentMethodModel?.data.country,
            currency: bankPaymentMethodModel?.data.currency,
            holder_type: bankPaymentMethodModel?.data.holder_type,
            routing_number: bankPaymentMethodModel?.data.routing_number,
            type: 'bank',
          },
        };

        let thrown = false;
        try {
          await bankPaymentMethods.update(
            bankPaymentMethodModel?.id,
            bankMethodParams
          );
        } catch (e) {
          thrown = true;
        }

        expect(thrown).to.equal(false);
        expect(sendTemplateMessageStub.called).to.be.false;
      });

      it('should not send sms if user has invalid mobile number', async () => {
        const bankPaymentWithInvalidMobileNumber =
          await bankPaymentMethods.create(userWithInvalidMobileNumberFixture);
        const bankMethodParams = {
          user_id: bankPaymentWithInvalidMobileNumber?.userId,
          is_settlement_account:
            bankPaymentWithInvalidMobileNumber?.isSettlementAccount,
          backend: 'promisepay',
          data: {
            account_name: 'New account name',
            account_number: '*********',
            account_type: bankPaymentWithInvalidMobileNumber?.data.account_type,
            bank_name: bankPaymentWithInvalidMobileNumber?.data.bank_name,
            country: bankPaymentWithInvalidMobileNumber?.data.country,
            currency: bankPaymentWithInvalidMobileNumber?.data.currency,
            holder_type: bankPaymentWithInvalidMobileNumber?.data.holder_type,
            routing_number:
              bankPaymentWithInvalidMobileNumber?.data.routing_number,
            type: 'bank',
          },
        };

        await bankPaymentMethods.update(
          bankPaymentWithInvalidMobileNumber?.id,
          bankMethodParams
        );

        expect(sendTemplateMessageStub.called).to.equal(false);
      });
    });
  });
});
