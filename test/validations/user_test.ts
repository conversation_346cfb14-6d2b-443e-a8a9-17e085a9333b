import { expect } from 'chai';
import validator from '../../src/validations/user';
import { Backend } from '../../src/models/transaction';

describe('User validator', () => {
  const buildUserPayload = (overrides = {}) => ({
    id: 'cbf25350-26d1-41b1-97b8-509ab56bf447',
    ordermentum_id: 'cbf25350-26d1-41b1-97b8-509ab56bf447',
    name: { first: 'Valuable', last: 'Goshawk' },
    settlement_webhook:
      'https://localhost:3001/v1/suppliers/e2082ab5-2d77-49fa-969e-be315795a212/settlement?token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    address: {
      line_1: 'Vacuous Wombat',
      city: 'Wiry Rabbit',
      state: 'Silent Woodpecker',
      postcode: '2000',
      country: 'AUS',
    },
    email: '<EMAIL>',
    mobile_number: '+61858715168',
    dob: '1970-01-01T00:00:00.000Z',
    company: {
      name: 'Well-made Cat',
      legal_name: 'Vagabond Skunk',
      tax_number: 'Trite Ram',
      address: {
        line_1: 'Tawdry Pig',
        city: 'Wise Elk',
        state: 'Workable Oryx',
        postcode: '2000',
        country: 'AUS',
      },
    },
    ...overrides,
  });

  it('validates a valid payload', async () => {
    const result = await validator.validateAsync(buildUserPayload());
    expect(result).to.not.equal(null);
  });

  describe('address', () => {
    it('invalid if address is missing a postcode', async () => {
      try {
        await validator.validateAsync(
          buildUserPayload({
            address: {
              line_1: 'Vacuous Wombat',
              city: 'Wiry Rabbit',
              state: 'Silent Woodpecker',
              country: 'AUS',
            },
          })
        );
      } catch (e: Error) {
        expect(e?.details?.[0]?.path).to.deep.equal(['address', 'postcode']);
      }
    });
  });

  describe('dob', () => {
    describe('valid', () => {
      [
        '1982-03-27T00:00:00Z',
        '1973-02-12T00:00:00.000+00:00',
        '1992-10-31T00:00:00.000-0000',
        '1978-04-02',
        null,
      ].forEach(dob =>
        it(`valid if value is '${dob}'`, async () => {
          const result = await validator.validateAsync(
            buildUserPayload({
              dob,
            })
          );
          expect(result).to.not.equal(null);
        })
      );
    });

    describe('invalid', () => {
      [
        '23/10/1990',
        '1982-03-27T10:25:85.136Z',
        '1992-10-31T00:00:00.000',
        '2001-07-14T00:00:00.000-10:00',
      ].forEach(dob =>
        it(`invalid if value is '${dob}'`, async () => {
          try {
            await validator.validateAsync(
              buildUserPayload({
                dob,
              })
            );
          } catch (e: Error) {
            expect(e?.details?.[0]?.path).to.deep.equal(['dob']);
          }
        })
      );
    });
  });

  it('handles backend property', async () => {
    const params = buildUserPayload();

    let result = await validator.validateAsync(params);
    expect(result.backend).to.equal(Backend.PROMISEPAY);
    const secondParams = {
      ...params,
      backend: Backend.STRIPE,
    };
    result = await validator.validateAsync(secondParams);
    expect(result.backend).to.equal(Backend.STRIPE);

    const thirdParams = {
      ...params,
      backend: 'random',
    };
    try {
      await validator.validateAsync(thirdParams);
    } catch (e: Error) {
      expect(e?.details?.[0]?.path).to.deep.equal(['backend']);
    }
  });
});
